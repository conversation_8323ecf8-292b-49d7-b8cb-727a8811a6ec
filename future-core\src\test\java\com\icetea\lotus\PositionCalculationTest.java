package com.icetea.lotus;

import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.infrastructure.persistence.repository.MarkPriceJpaRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test để kiểm tra tính toán PNL và các giá trị position
 */
@SpringBootTest
public class PositionCalculationTest {

    @Autowired
    private PriceManagementService priceManagementService;

    @Autowired
    private MarkPriceJpaRepository markPriceJpaRepository;

    @Test
    public void testUnrealizedProfitCalculation() {
        // Tạo position test với dữ liệu từ ảnh
        Position position = Position.builder()
                .id(PositionId.of(1L))
                .memberId(123L)
                .symbol(Symbol.of("BTC/USDT"))
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("6.000000"))
                .openPrice(Money.of(new BigDecimal("114968.55")))
                .margin(Money.of(new BigDecimal("689811320.38"))) // Giá trị từ ảnh
                .leverage(new BigDecimal("1"))
                .status(PositionStatus.OPEN)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // Mark price từ ảnh
        Money markPrice = Money.of(new BigDecimal("415297.83"));

        // Tính toán unrealized profit
        Money unrealizedProfit = position.calculateUnrealizedProfit(markPrice);

        // Tính toán thủ công
        BigDecimal priceDiff = markPrice.getValue().subtract(position.getOpenPrice().getValue());
        BigDecimal expectedProfit = priceDiff.multiply(position.getVolume());

        // Tính ROI
        BigDecimal roi = unrealizedProfit.getValue()
                .divide(position.getMargin().getValue(), 8, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));

        // Kiểm tra tính toán
        assertEquals(expectedProfit.setScale(8, BigDecimal.ROUND_HALF_UP),
                    unrealizedProfit.getValue().setScale(8, BigDecimal.ROUND_HALF_UP));

        // Kiểm tra xem mark price có hợp lý không
        assertTrue(markPrice.getValue().compareTo(new BigDecimal("100000")) > 0,
                  "Mark price seems too high");
        assertTrue(markPrice.getValue().compareTo(new BigDecimal("500000")) < 0,
                  "Mark price seems too high");
    }

    @Test
    public void testMarkPriceCalculation() {
        // Test tính toán mark price với funding rate
        BigDecimal indexPrice = new BigDecimal("220000.00");
        BigDecimal fundingRate = new BigDecimal("-0.00003504");

        // Công thức: Mark Price = Index Price * (1 + Funding Rate)
        BigDecimal markPrice = indexPrice.multiply(BigDecimal.ONE.add(fundingRate));

        System.out.println("=== MARK PRICE CALCULATION TEST ===");
        System.out.println("Index Price: " + indexPrice);
        System.out.println("Funding Rate: " + fundingRate);
        System.out.println("Calculated Mark Price: " + markPrice);

        // Mark price should be very close to index price with small funding rate
        assertTrue(markPrice.compareTo(new BigDecimal("219000")) > 0);
        assertTrue(markPrice.compareTo(new BigDecimal("221000")) < 0);

        // Không nên có mark price 415,297.83 với index price 220,000
        assertNotEquals(new BigDecimal("415297.83"), markPrice.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    @Test
    public void testCorrectPositionValues() {
        // Test với giá trị hợp lý
        Position position = Position.builder()
                .id(PositionId.of(1L))
                .memberId(123L)
                .symbol(Symbol.of("BTC/USDT"))
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("6.000000"))
                .openPrice(Money.of(new BigDecimal("114968.55")))
                .margin(Money.of(new BigDecimal("6000.00"))) // Margin hợp lý cho 6 USDT với leverage 1x
                .leverage(new BigDecimal("1"))
                .status(PositionStatus.OPEN)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        // Mark price hợp lý (gần với index price)
        Money markPrice = Money.of(new BigDecimal("219992.29")); // Index price với funding rate

        // Tính toán unrealized profit
        Money unrealizedProfit = position.calculateUnrealizedProfit(markPrice);

        System.out.println("=== CORRECT POSITION VALUES TEST ===");
        System.out.println("Entry Price: " + position.getOpenPrice().getValue());
        System.out.println("Mark Price: " + markPrice.getValue());
        System.out.println("Volume: " + position.getVolume());
        System.out.println("Margin: " + position.getMargin().getValue());
        System.out.println("Unrealized Profit: " + unrealizedProfit.getValue());

        // Tính ROI
        BigDecimal roi = unrealizedProfit.getValue()
                .divide(position.getMargin().getValue(), 8, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));

        System.out.println("ROI %: " + roi);

        // Với giá trị hợp lý, ROI không nên quá cao
        assertTrue(roi.abs().compareTo(new BigDecimal("1000")) < 0, "ROI should not be over 1000%");
    }

    @Test
    public void testActualMarkPriceFromDatabase() {
        // Test để kiểm tra mark price thực tế từ database
        try {
            // Lấy mark price từ service
            Money currentMarkPrice = priceManagementService.getCurrentMarkPrice(Symbol.of("BTC/USDT"));

            // Lấy mark price từ repository
            var markPriceEntity = markPriceJpaRepository.findTopBySymbolValueOrderByCreateTimeDesc("BTC/USDT");

            System.out.println("=== ACTUAL MARK PRICE TEST ===");
            System.out.println("Mark Price from Service: " + (currentMarkPrice != null ? currentMarkPrice.getValue() : "null"));
            System.out.println("Mark Price from Repository: " + (markPriceEntity != null ? markPriceEntity.getPrice() : "null"));

            // Kiểm tra xem mark price có hợp lý không (nên trong khoảng 100k-300k cho BTC)
            if (currentMarkPrice != null) {
                assertTrue(currentMarkPrice.getValue().compareTo(new BigDecimal("50000")) > 0,
                          "Mark price should be greater than 50,000");
                assertTrue(currentMarkPrice.getValue().compareTo(new BigDecimal("300000")) < 0,
                          "Mark price should be less than 300,000");
            }

        } catch (Exception e) {
            System.out.println("Error getting mark price: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
