package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberInviteStasticRank is a Querydsl query type for MemberInviteStasticRank
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberInviteStasticRank extends EntityPathBase<MemberInviteStasticRank> {

    private static final long serialVersionUID = -17484140L;

    public static final QMemberInviteStasticRank memberInviteStasticRank = new QMemberInviteStasticRank("memberInviteStasticRank");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isRobot = createNumber("isRobot", Integer.class);

    public final NumberPath<Integer> levelOne = createNumber("levelOne", Integer.class);

    public final NumberPath<Integer> levelTwo = createNumber("levelTwo", Integer.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final DateTimePath<java.util.Date> stasticDate = createDateTime("stasticDate", java.util.Date.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final StringPath userIdentify = createString("userIdentify");

    public QMemberInviteStasticRank(String variable) {
        super(MemberInviteStasticRank.class, forVariable(variable));
    }

    public QMemberInviteStasticRank(Path<? extends MemberInviteStasticRank> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberInviteStasticRank(PathMetadata metadata) {
        super(MemberInviteStasticRank.class, metadata);
    }

}

