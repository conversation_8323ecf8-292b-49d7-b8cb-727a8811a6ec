-- <PERSON><PERSON><PERSON> để thêm bảng contract_account và contract_account_transaction vào cơ sở dữ liệu

-- Tạo bảng contract_account nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS contract_account (
    id VARCHAR(255) PRIMARY KEY,
    member_id VARCHAR(255) NOT NULL UNIQUE,
    balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    frozen_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    available_balance DECIMAL(18,8) NOT NULL DEFAULT 0,
    create_time TIMESTAMP NOT NULL,
    update_time TIMESTAMP NOT NULL
);

-- Tạo chỉ mục cho bảng contract_account
CREATE INDEX IF NOT EXISTS idx_account_member_id ON contract_account (member_id);

-- Tạo bảng contract_account_transaction nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS contract_account_transaction (
    id VARCHAR(255) PRIMARY KEY,
    member_id VARCHAR(255) NOT NULL,
    amount DECIMAL(18,8) NOT NULL,
    balance DECIMAL(18,8) NOT NULL,
    type VARCHAR(50) NOT NULL,
    create_time TIMESTAMP NOT NULL,
    remark TEXT
);

-- Tạo chỉ mục cho bảng contract_account_transaction
CREATE INDEX IF NOT EXISTS idx_account_transaction_member_id ON contract_account_transaction (member_id);
CREATE INDEX IF NOT EXISTS idx_account_transaction_type ON contract_account_transaction (type);
CREATE INDEX IF NOT EXISTS idx_account_transaction_create_time ON contract_account_transaction (create_time);

-- Đảm bảo extension uuid-ossp đã được cài đặt
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Thêm dữ liệu mẫu cho bảng contract_account
INSERT INTO contract_account (id, member_id, balance, frozen_balance, available_balance, create_time, update_time)
VALUES
('ACC-' || uuid_generate_v4(), '1', 1000.********, 100.********, 900.********, NOW() - INTERVAL '1 DAY', NOW()),
('ACC-' || uuid_generate_v4(), '2', 2000.********, 200.********, 1800.********, NOW() - INTERVAL '2 DAY', NOW()),
('ACC-' || uuid_generate_v4(), '3', 3000.********, 300.********, 2700.********, NOW() - INTERVAL '3 DAY', NOW()),
('ACC-' || uuid_generate_v4(), 'INSURANCE_FUND', 10000.********, 0.********, 10000.********, NOW() - INTERVAL '10 DAY', NOW());

-- Thêm dữ liệu mẫu cho bảng contract_account_transaction
INSERT INTO contract_account_transaction (id, member_id, amount, balance, type, create_time, remark)
VALUES
('TRX-' || uuid_generate_v4(), '1', 1000.********, 1000.********, 'DEPOSIT', NOW() - INTERVAL '1 DAY', 'Initial deposit'),
('TRX-' || uuid_generate_v4(), '1', -100.********, 900.********, 'TRADING_FEE', NOW() - INTERVAL '12 HOUR', 'Trading fee for order ORD123456789'),
('TRX-' || uuid_generate_v4(), '2', 2000.********, 2000.********, 'DEPOSIT', NOW() - INTERVAL '2 DAY', 'Initial deposit'),
('TRX-' || uuid_generate_v4(), '2', -200.********, 1800.********, 'TRADING_FEE', NOW() - INTERVAL '1 DAY', 'Trading fee for order ORD987654321'),
('TRX-' || uuid_generate_v4(), '3', 3000.********, 3000.********, 'DEPOSIT', NOW() - INTERVAL '3 DAY', 'Initial deposit'),
('TRX-' || uuid_generate_v4(), '3', -300.********, 2700.********, 'TRADING_FEE', NOW() - INTERVAL '2 DAY', 'Trading fee for order ORD456789123'),
('TRX-' || uuid_generate_v4(), 'INSURANCE_FUND', 10000.********, 10000.********, 'DEPOSIT', NOW() - INTERVAL '10 DAY', 'Initial insurance fund');

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE contract_account IS 'Bảng lưu trữ thông tin tài khoản của thành viên';
COMMENT ON COLUMN contract_account.id IS 'ID của tài khoản';
COMMENT ON COLUMN contract_account.member_id IS 'ID của thành viên';
COMMENT ON COLUMN contract_account.balance IS 'Số dư tài khoản';
COMMENT ON COLUMN contract_account.frozen_balance IS 'Số dư đóng băng';
COMMENT ON COLUMN contract_account.available_balance IS 'Số dư khả dụng';
COMMENT ON COLUMN contract_account.create_time IS 'Thời gian tạo tài khoản';
COMMENT ON COLUMN contract_account.update_time IS 'Thời gian cập nhật tài khoản';

COMMENT ON TABLE contract_account_transaction IS 'Bảng lưu trữ các giao dịch tài khoản';
COMMENT ON COLUMN contract_account_transaction.id IS 'ID của giao dịch';
COMMENT ON COLUMN contract_account_transaction.member_id IS 'ID của thành viên';
COMMENT ON COLUMN contract_account_transaction.amount IS 'Số tiền giao dịch';
COMMENT ON COLUMN contract_account_transaction.balance IS 'Số dư sau giao dịch';
COMMENT ON COLUMN contract_account_transaction.type IS 'Loại giao dịch: DEPOSIT, WITHDRAW, TRADING_FEE, TRADING_PROFIT, TRADING_LOSS, LIQUIDATION, FUNDING_PAYMENT, INSURANCE_FUND, LIQUIDATION_REFUND, ADL_RECOVERY';
COMMENT ON COLUMN contract_account_transaction.create_time IS 'Thời gian tạo giao dịch';
COMMENT ON COLUMN contract_account_transaction.remark IS 'Ghi chú';
