package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QWalletTransRecord is a Querydsl query type for WalletTransRecord
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QWalletTransRecord extends EntityPathBase<WalletTransRecord> {

    private static final long serialVersionUID = -44744688L;

    public static final QWalletTransRecord walletTransRecord = new QWalletTransRecord("walletTransRecord");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final EnumPath<com.icetea.lotus.constant.WalletType> source = createEnum("source", com.icetea.lotus.constant.WalletType.class);

    public final EnumPath<com.icetea.lotus.constant.WalletType> target = createEnum("target", com.icetea.lotus.constant.WalletType.class);

    public final StringPath unit = createString("unit");

    public QWalletTransRecord(String variable) {
        super(WalletTransRecord.class, forVariable(variable));
    }

    public QWalletTransRecord(Path<? extends WalletTransRecord> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWalletTransRecord(PathMetadata metadata) {
        super(WalletTransRecord.class, metadata);
    }

}

