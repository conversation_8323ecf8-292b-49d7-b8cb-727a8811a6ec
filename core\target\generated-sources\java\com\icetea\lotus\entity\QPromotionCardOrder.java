package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QPromotionCardOrder is a Querydsl query type for PromotionCardOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QPromotionCardOrder extends EntityPathBase<PromotionCardOrder> {

    private static final long serialVersionUID = 689799691L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QPromotionCardOrder promotionCardOrder = new QPromotionCardOrder("promotionCardOrder");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final QPromotionCard card;

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isFree = createNumber("isFree", Integer.class);

    public final NumberPath<Integer> isLock = createNumber("isLock", Integer.class);

    public final NumberPath<Integer> lockDays = createNumber("lockDays", Integer.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<Integer> state = createNumber("state", Integer.class);

    public QPromotionCardOrder(String variable) {
        this(PromotionCardOrder.class, forVariable(variable), INITS);
    }

    public QPromotionCardOrder(Path<? extends PromotionCardOrder> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QPromotionCardOrder(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QPromotionCardOrder(PathMetadata metadata, PathInits inits) {
        this(PromotionCardOrder.class, metadata, inits);
    }

    public QPromotionCardOrder(Class<? extends PromotionCardOrder> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.card = inits.isInitialized("card") ? new QPromotionCard(forProperty("card"), inits.get("card")) : null;
    }

}

