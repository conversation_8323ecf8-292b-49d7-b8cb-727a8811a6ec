package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QConvertOrder is a Querydsl query type for ConvertOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QConvertOrder extends EntityPathBase<ConvertOrder> {

    private static final long serialVersionUID = -811163989L;

    public static final QConvertOrder convertOrder = new QConvertOrder("convertOrder");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> fee = createNumber("fee", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> fromAmount = createNumber("fromAmount", java.math.BigDecimal.class);

    public final StringPath fromUnit = createString("fromUnit");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> price = createNumber("price", java.math.BigDecimal.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final NumberPath<java.math.BigDecimal> toAmount = createNumber("toAmount", java.math.BigDecimal.class);

    public final StringPath toUnit = createString("toUnit");

    public QConvertOrder(String variable) {
        super(ConvertOrder.class, forVariable(variable));
    }

    public QConvertOrder(Path<? extends ConvertOrder> path) {
        super(path.getType(), path.getMetadata());
    }

    public QConvertOrder(PathMetadata metadata) {
        super(ConvertOrder.class, metadata);
    }

}

