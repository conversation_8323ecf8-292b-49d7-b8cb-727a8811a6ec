package com.icetea.lotus.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Configuration để quản lý các scheduler cập nhật giá
 * Đảm bảo không có conflict giữa các scheduler
 */
@Configuration
@Slf4j
public class PriceSchedulerConfig {

    @Value("${contract.pricing.realtime-update-enabled:false}")
    private boolean realtimeUpdateEnabled;

    @Value("${contract.pricing.market-data-calculation-enabled:true}")
    private boolean marketDataCalculationEnabled;

    @Value("${contract.pricing.scheduled-tasks-enabled:true}")
    private boolean scheduledTasksEnabled;

    @Value("${contract.pricing.price-update-scheduler-enabled:true}")
    private boolean priceUpdateSchedulerEnabled;

    @Value("${contract.pricing.funding-rate-scheduler-enabled:true}")
    private boolean fundingRateSchedulerEnabled;

    @PostConstruct
    public void validateConfiguration() {
        log.info("=== PRICE SCHEDULER CONFIGURATION ===");
        log.info("RealTimeMarketPriceUpdater: {}", realtimeUpdateEnabled ? "ENABLED" : "DISABLED");
        log.info("MarketDataService calculation: {}", marketDataCalculationEnabled ? "ENABLED" : "DISABLED");
        log.info("ScheduledTasks mark price cache: {}", scheduledTasksEnabled ? "ENABLED" : "DISABLED");
        log.info("PriceUpdateScheduler: {}", priceUpdateSchedulerEnabled ? "ENABLED" : "DISABLED");
        log.info("FundingRateScheduler: {}", fundingRateSchedulerEnabled ? "ENABLED" : "DISABLED");

        // Kiểm tra conflict
        int enabledSchedulers = 0;
        if (realtimeUpdateEnabled) enabledSchedulers++;
        if (marketDataCalculationEnabled) enabledSchedulers++;
        if (priceUpdateSchedulerEnabled) enabledSchedulers++;

        if (enabledSchedulers > 1) {
            log.warn("⚠️  WARNING: Multiple mark price schedulers are enabled!");
            log.warn("⚠️  This may cause race conditions and inconsistent mark prices!");
            log.warn("⚠️  Recommended: Enable only PriceUpdateScheduler in production");
        }

        // Khuyến nghị cấu hình production
        if (realtimeUpdateEnabled) {
            log.warn("🚨 ALERT: RealTimeMarketPriceUpdater is enabled!");
            log.warn("🚨 This scheduler can cause abnormal mark prices!");
            log.warn("🚨 Recommended: Disable in production environment");
        }

        // Cấu hình khuyến nghị
        log.info("=== RECOMMENDED PRODUCTION CONFIGURATION ===");
        log.info("contract.pricing.realtime-update-enabled=false");
        log.info("contract.pricing.market-data-calculation-enabled=false");
        log.info("contract.pricing.scheduled-tasks-enabled=true");
        log.info("contract.pricing.price-update-scheduler-enabled=true");
        log.info("contract.pricing.funding-rate-scheduler-enabled=true");
    }

    public boolean isRealtimeUpdateEnabled() {
        return realtimeUpdateEnabled;
    }

    public boolean isMarketDataCalculationEnabled() {
        return marketDataCalculationEnabled;
    }

    public boolean isScheduledTasksEnabled() {
        return scheduledTasksEnabled;
    }

    public boolean isPriceUpdateSchedulerEnabled() {
        return priceUpdateSchedulerEnabled;
    }

    public boolean isFundingRateSchedulerEnabled() {
        return fundingRateSchedulerEnabled;
    }
}
