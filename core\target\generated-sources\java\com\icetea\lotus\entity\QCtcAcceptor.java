package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QCtcAcceptor is a Querydsl query type for CtcAcceptor
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QCtcAcceptor extends EntityPathBase<CtcAcceptor> {

    private static final long serialVersionUID = 488037837L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QCtcAcceptor ctcAcceptor = new QCtcAcceptor("ctcAcceptor");

    public final NumberPath<java.math.BigDecimal> cnyIn = createNumber("cnyIn", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> cnyOut = createNumber("cnyOut", java.math.BigDecimal.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final QMember member;

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final NumberPath<java.math.BigDecimal> usdtIn = createNumber("usdtIn", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> usdtOut = createNumber("usdtOut", java.math.BigDecimal.class);

    public QCtcAcceptor(String variable) {
        this(CtcAcceptor.class, forVariable(variable), INITS);
    }

    public QCtcAcceptor(Path<? extends CtcAcceptor> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QCtcAcceptor(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QCtcAcceptor(PathMetadata metadata, PathInits inits) {
        this(CtcAcceptor.class, metadata, inits);
    }

    public QCtcAcceptor(Class<? extends CtcAcceptor> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.member = inits.isInitialized("member") ? new QMember(forProperty("member"), inits.get("member")) : null;
    }

}

