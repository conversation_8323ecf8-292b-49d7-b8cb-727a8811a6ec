package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QLockedOrderDetail is a Querydsl query type for LockedOrderDetail
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QLockedOrderDetail extends EntityPathBase<LockedOrderDetail> {

    private static final long serialVersionUID = 922527877L;

    public static final QLockedOrderDetail lockedOrderDetail = new QLockedOrderDetail("lockedOrderDetail");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> lockedOrderId = createNumber("lockedOrderId", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> output = createNumber("output", java.math.BigDecimal.class);

    public final StringPath releaseUnit = createString("releaseUnit");

    public QLockedOrderDetail(String variable) {
        super(LockedOrderDetail.class, forVariable(variable));
    }

    public QLockedOrderDetail(Path<? extends LockedOrderDetail> path) {
        super(path.getType(), path.getMetadata());
    }

    public QLockedOrderDetail(PathMetadata metadata) {
        super(LockedOrderDetail.class, metadata);
    }

}

