package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QSysHelp is a Querydsl query type for SysHelp
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QSysHelp extends EntityPathBase<SysHelp> {

    private static final long serialVersionUID = 836514910L;

    public static final QSysHelp sysHelp = new QSysHelp("sysHelp");

    public final StringPath author = createString("author");

    public final StringPath content = createString("content");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath imgUrl = createString("imgUrl");

    public final StringPath isTop = createString("isTop");

    public final StringPath lang = createString("lang");

    public final NumberPath<Integer> sort = createNumber("sort", Integer.class);

    public final EnumPath<com.icetea.lotus.constant.CommonStatus> status = createEnum("status", com.icetea.lotus.constant.CommonStatus.class);

    public final EnumPath<com.icetea.lotus.constant.SysHelpClassification> sysHelpClassification = createEnum("sysHelpClassification", com.icetea.lotus.constant.SysHelpClassification.class);

    public final StringPath title = createString("title");

    public QSysHelp(String variable) {
        super(SysHelp.class, forVariable(variable));
    }

    public QSysHelp(Path<? extends SysHelp> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSysHelp(PathMetadata metadata) {
        super(SysHelp.class, metadata);
    }

}

