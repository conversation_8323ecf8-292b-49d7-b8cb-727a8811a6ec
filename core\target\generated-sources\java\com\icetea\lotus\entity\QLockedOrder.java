package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QLockedOrder is a Querydsl query type for LockedOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QLockedOrder extends EntityPathBase<LockedOrder> {

    private static final long serialVersionUID = 149898004L;

    public static final QLockedOrder lockedOrder = new QLockedOrder("lockedOrder");

    public final NumberPath<Long> activityId = createNumber("activityId", Long.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> currentReleaseamount = createNumber("currentReleaseamount", java.math.BigDecimal.class);

    public final DateTimePath<java.util.Date> endTime = createDateTime("endTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath image = createString("image");

    public final NumberPath<Integer> lockedDays = createNumber("lockedDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> lockedInvite = createNumber("lockedInvite", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> lockedInvitelimit = createNumber("lockedInvitelimit", java.math.BigDecimal.class);

    public final NumberPath<Integer> lockedStatus = createNumber("lockedStatus", Integer.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> originReleaseamount = createNumber("originReleaseamount", java.math.BigDecimal.class);

    public final NumberPath<Integer> period = createNumber("period", Integer.class);

    public final NumberPath<java.math.BigDecimal> releaseCurrentpercent = createNumber("releaseCurrentpercent", java.math.BigDecimal.class);

    public final NumberPath<Integer> releasedDays = createNumber("releasedDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> releasePercent = createNumber("releasePercent", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> releaseTimes = createNumber("releaseTimes", java.math.BigDecimal.class);

    public final NumberPath<Integer> releaseType = createNumber("releaseType", Integer.class);

    public final StringPath releaseUnit = createString("releaseUnit");

    public final StringPath title = createString("title");

    public final NumberPath<java.math.BigDecimal> totalLocked = createNumber("totalLocked", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> totalRelease = createNumber("totalRelease", java.math.BigDecimal.class);

    public QLockedOrder(String variable) {
        super(LockedOrder.class, forVariable(variable));
    }

    public QLockedOrder(Path<? extends LockedOrder> path) {
        super(path.getType(), path.getMetadata());
    }

    public QLockedOrder(PathMetadata metadata) {
        super(LockedOrder.class, metadata);
    }

}

