package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QCtcOrder is a Querydsl query type for CtcOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QCtcOrder extends EntityPathBase<CtcOrder> {

    private static final long serialVersionUID = 1748190636L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QCtcOrder ctcOrder = new QCtcOrder("ctcOrder");

    public final QMember acceptor;

    public final QAlipay alipay;

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final QBankInfo bankInfo;

    public final StringPath cancelReason = createString("cancelReason");

    public final DateTimePath<java.util.Date> cancelTime = createDateTime("cancelTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> completeTime = createDateTime("completeTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> confirmTime = createDateTime("confirmTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> direction = createNumber("direction", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final QMember member;

    public final NumberPath<java.math.BigDecimal> money = createNumber("money", java.math.BigDecimal.class);

    public final StringPath orderSn = createString("orderSn");

    public final StringPath payMode = createString("payMode");

    public final DateTimePath<java.util.Date> payTime = createDateTime("payTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> price = createNumber("price", java.math.BigDecimal.class);

    public final StringPath realName = createString("realName");

    public final StringPath remark = createString("remark");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath unit = createString("unit");

    public final QWechatPay wechatPay;

    public QCtcOrder(String variable) {
        this(CtcOrder.class, forVariable(variable), INITS);
    }

    public QCtcOrder(Path<? extends CtcOrder> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QCtcOrder(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QCtcOrder(PathMetadata metadata, PathInits inits) {
        this(CtcOrder.class, metadata, inits);
    }

    public QCtcOrder(Class<? extends CtcOrder> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.acceptor = inits.isInitialized("acceptor") ? new QMember(forProperty("acceptor"), inits.get("acceptor")) : null;
        this.alipay = inits.isInitialized("alipay") ? new QAlipay(forProperty("alipay")) : null;
        this.bankInfo = inits.isInitialized("bankInfo") ? new QBankInfo(forProperty("bankInfo")) : null;
        this.member = inits.isInitialized("member") ? new QMember(forProperty("member"), inits.get("member")) : null;
        this.wechatPay = inits.isInitialized("wechatPay") ? new QWechatPay(forProperty("wechatPay")) : null;
    }

}

