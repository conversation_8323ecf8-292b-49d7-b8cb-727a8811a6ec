package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberWeightUpper is a Querydsl query type for MemberWeightUpper
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberWeightUpper extends EntityPathBase<MemberWeightUpper> {

    private static final long serialVersionUID = -369389888L;

    public static final QMemberWeightUpper memberWeightUpper = new QMemberWeightUpper("memberWeightUpper");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> firstMemberId = createNumber("firstMemberId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<Integer> rate = createNumber("rate", Integer.class);

    public final StringPath upper = createString("upper");

    public QMemberWeightUpper(String variable) {
        super(MemberWeightUpper.class, forVariable(variable));
    }

    public QMemberWeightUpper(Path<? extends MemberWeightUpper> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberWeightUpper(PathMetadata metadata) {
        super(MemberWeightUpper.class, metadata);
    }

}

