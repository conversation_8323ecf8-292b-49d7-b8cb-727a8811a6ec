package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QConvertCoin is a Querydsl query type for ConvertCoin
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QConvertCoin extends EntityPathBase<ConvertCoin> {

    private static final long serialVersionUID = -1134905452L;

    public static final QConvertCoin convertCoin = new QConvertCoin("convertCoin");

    public final StringPath coinUnit = createString("coinUnit");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> fee = createNumber("fee", java.math.BigDecimal.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<java.math.BigDecimal> maxAmount = createNumber("maxAmount", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minAmount = createNumber("minAmount", java.math.BigDecimal.class);

    public final NumberPath<Integer> sort = createNumber("sort", Integer.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final DateTimePath<java.util.Date> updateTime = createDateTime("updateTime", java.util.Date.class);

    public QConvertCoin(String variable) {
        super(ConvertCoin.class, forVariable(variable));
    }

    public QConvertCoin(Path<? extends ConvertCoin> path) {
        super(path.getType(), path.getMetadata());
    }

    public QConvertCoin(PathMetadata metadata) {
        super(ConvertCoin.class, metadata);
    }

}

