package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QActivity is a Querydsl query type for Activity
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QActivity extends EntityPathBase<Activity> {

    private static final long serialVersionUID = 865708127L;

    public static final QActivity activity = new QActivity("activity");

    public final StringPath acceptUnit = createString("acceptUnit");

    public final StringPath activityLink = createString("activityLink");

    public final NumberPath<Integer> amountScale = createNumber("amountScale", Integer.class);

    public final StringPath bannerImageUrl = createString("bannerImageUrl");

    public final StringPath content = createString("content");

    public final StringPath contentEN = createString("contentEN");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final StringPath detail = createString("detail");

    public final StringPath detailEN = createString("detailEN");

    public final StringPath endTime = createString("endTime");

    public final NumberPath<java.math.BigDecimal> freezeAmount = createNumber("freezeAmount", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> holdLimit = createNumber("holdLimit", java.math.BigDecimal.class);

    public final StringPath holdUnit = createString("holdUnit");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> leveloneCount = createNumber("leveloneCount", Integer.class);

    public final NumberPath<Integer> limitTimes = createNumber("limitTimes", Integer.class);

    public final NumberPath<Integer> lockedDays = createNumber("lockedDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> lockedFee = createNumber("lockedFee", java.math.BigDecimal.class);

    public final NumberPath<Integer> lockedPeriod = createNumber("lockedPeriod", Integer.class);

    public final StringPath lockedUnit = createString("lockedUnit");

    public final NumberPath<java.math.BigDecimal> maxLimitAmout = createNumber("maxLimitAmout", java.math.BigDecimal.class);

    public final NumberPath<Integer> miningDays = createNumber("miningDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> miningDaysprofit = createNumber("miningDaysprofit", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> miningInvite = createNumber("miningInvite", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> miningInvitelimit = createNumber("miningInvitelimit", java.math.BigDecimal.class);

    public final NumberPath<Integer> miningPeriod = createNumber("miningPeriod", Integer.class);

    public final StringPath miningUnit = createString("miningUnit");

    public final NumberPath<java.math.BigDecimal> minLimitAmout = createNumber("minLimitAmout", java.math.BigDecimal.class);

    public final StringPath noticeLink = createString("noticeLink");

    public final NumberPath<java.math.BigDecimal> price = createNumber("price", java.math.BigDecimal.class);

    public final NumberPath<Integer> priceScale = createNumber("priceScale", Integer.class);

    public final NumberPath<Integer> progress = createNumber("progress", Integer.class);

    public final NumberPath<java.math.BigDecimal> releaseAmount = createNumber("releaseAmount", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> releasePercent = createNumber("releasePercent", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> releaseTimes = createNumber("releaseTimes", java.math.BigDecimal.class);

    public final NumberPath<Integer> releaseType = createNumber("releaseType", Integer.class);

    public final StringPath settings = createString("settings");

    public final StringPath smallImageUrl = createString("smallImageUrl");

    public final StringPath startTime = createString("startTime");

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> status = createEnum("status", com.icetea.lotus.constant.BooleanEnum.class);

    public final NumberPath<Integer> step = createNumber("step", Integer.class);

    public final StringPath title = createString("title");

    public final StringPath titleEN = createString("titleEN");

    public final NumberPath<java.math.BigDecimal> totalSupply = createNumber("totalSupply", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> tradedAmount = createNumber("tradedAmount", java.math.BigDecimal.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final StringPath unit = createString("unit");

    public QActivity(String variable) {
        super(Activity.class, forVariable(variable));
    }

    public QActivity(Path<? extends Activity> path) {
        super(path.getType(), path.getMetadata());
    }

    public QActivity(PathMetadata metadata) {
        super(Activity.class, metadata);
    }

}

