# Korean messages

# Common messages
common.success=성공
common.error=오류
common.warning=경고
common.info=정보

# Order Matching messages
orderMatching.error.cancelOrder=주문 취소 오류, 주문 ID = {0}, 심볼 = {1}
orderMatching.error.placeOrder=주문 배치 오류, 주문 ID = {0}, 심볼 = {1}
orderMatching.error.cancelAllOrders=모든 주문 취소 오류, 회원 ID = {0}, 심볼 = {1}
orderMatching.error.liquidatePosition=포지션 청산 오류, 포지션 ID = {0}, 심볼 = {1}
orderMatching.error.matchOrderFIFO=FIFO 알고리즘으로 주문 매칭 오류, 주문 ID = {0}, 심볼 = {1}
orderMatching.error.matchOrderProRata=Pro-rata 알고리즘으로 주문 매칭 오류, 주문 ID = {0}, 심볼 = {1}
orderMatching.error.matchOrderHybrid=Hybrid 알고리즘으로 주문 매칭 오류, 주문 ID = {0}, 심볼 = {1}
orderMatching.error.shutdown=OrderMatchingEngineService 리소스 해제 오류
orderMatching.error.syncContracts=매칭 엔진 동기화 실패
orderMatching.error.initEngine=매칭 엔진 초기화 오류, 심볼 = {0}
orderMatching.error.checkTriggerOrders=트리거 주문 확인 오류, 심볼 = {0}
orderMatching.error.checkLiquidations=청산 확인 오류, 심볼 = {0}
orderMatching.error.lock.timeout=심볼 {0}에 대한 잠금을 획득할 수 없습니다
orderMatching.error.lock.interrupted=심볼 {0}에 대한 잠금을 기다리는 동안 중단되었습니다
orderMatching.error.contract.notFound=심볼 {0}에 대한 계약을 찾을 수 없습니다
orderMatching.error.setAlgorithm=심볼 {0}에 대한 매칭 알고리즘 설정 오류, 알고리즘: {1}
orderMatching.error.getOrderBook=심볼 {0}에 대한 주문장 가져오기 오류
orderMatching.info.algorithmSet=심볼 {0}에 대한 매칭 알고리즘 설정 완료, 알고리즘: {1}

orderMatching.warn.engineNotFound=심볼 {0}에 대한 매칭 엔진을 찾을 수 없습니다
orderMatching.warn.invalidSymbol=유효하지 않은 심볼
orderMatching.warn.invalidOrder=유효하지 않은 주문
orderMatching.warn.tradingHalt=심볼 {0}에 대한 거래가 중지되었습니다
orderMatching.warn.enabledContractsNotFound=활성화된 계약 목록을 검색할 수 없습니다
orderMatching.warn.symbol.notOwned=심볼 {0}은(는) 이 포드에 속하지 않습니다

orderMatching.info.engineInitialized=심볼 {0}에 대한 매칭 엔진이 초기화되었습니다
orderMatching.info.engineShutdown=심볼 {0}에 대한 매칭 엔진이 중지되고 리소스가 해제되었습니다
orderMatching.info.tradingPaused=심볼 {0}에 대한 거래가 일시 중지되었습니다
orderMatching.info.tradingResumed=심볼 {0}에 대한 거래가 재개되었습니다
orderMatching.info.syncCompleted=동기화가 완료되었습니다. 매칭 엔진 수: {0}
orderMatching.info.syncStarted=계약과 매칭 엔진 동기화 중
orderMatching.info.shutdownStarted=OrderMatchingEngineService 리소스 해제 중
orderMatching.info.shutdownCompleted=OrderMatchingEngineService 리소스가 해제되었습니다
orderMatching.info.positionLiquidated=포지션 {0}이(가) 청산되었습니다, 심볼 = {1}
orderMatching.info.orderCanceled=주문 {0}이(가) 취소되었습니다, 심볼 = {1}
orderMatching.info.distributedEngineInitialized=분산 잠금 매칭 엔진이 초기화되었습니다
orderMatching.info.optimizedEngineInitialized=최적화된 매칭 엔진이 초기화되었습니다

orderMatching.debug.markPriceUpdated=심볼 {0}에 대한 마크 가격이 업데이트되었습니다, 마크 가격 = {1}
orderMatching.debug.indexPriceUpdated=심볼 {0}에 대한 인덱스 가격이 업데이트되었습니다, 인덱스 가격 = {1}
orderMatching.debug.triggerOrdersChecked=심볼 {0}에 대한 트리거 주문이 확인되었습니다
orderMatching.debug.liquidationsChecked=심볼 {0}에 대한 청산이 확인되었습니다

# Contract Service messages
contractService.error.createContract=계약 생성 오류, 심볼 = {0}
contractService.error.updateContract=계약 업데이트 오류, 심볼 = {0}
contractService.error.deleteContract=계약 삭제 오류, 심볼 = {0}

contractService.warn.contractNotFound=심볼 {0}을(를) 가진 계약을 찾을 수 없습니다

contractService.info.contractCreated=계약이 생성되었습니다, 심볼 = {0}
contractService.info.contractUpdated=계약이 업데이트되었습니다, 심볼 = {0}
contractService.info.contractDeleted=계약이 삭제되었습니다, 심볼 = {0}

# Position Service messages
positionService.error.createPosition=포지션 생성 오류, 회원 ID = {0}, 심볼 = {1}
positionService.error.updatePosition=포지션 업데이트 오류, 포지션 ID = {0}
positionService.error.closePosition=포지션 종료 오류, 포지션 ID = {0}

positionService.warn.positionNotFound=ID {0}을(를) 가진 포지션을 찾을 수 없습니다

positionService.info.positionCreated=포지션이 생성되었습니다, 회원 ID = {0}, 심볼 = {1}
positionService.info.positionUpdated=포지션이 업데이트되었습니다, 포지션 ID = {0}
positionService.info.positionClosed=포지션이 종료되었습니다, 포지션 ID = {0}

# Messaging Service messages
messagingService.error.processFundingRate=펀딩 비율 처리 오류, 심볼 = {0}, 값 = {1}
messagingService.error.processNewOrder=새 주문 처리 오류
messagingService.error.processCancelOrder=주문 취소 처리 오류
messagingService.error.processFundingRateUpdate=펀딩 비율 업데이트 처리 오류
messagingService.error.processTrade=거래 처리 오류, 심볼 = {0}, 값 = {1}

messagingService.info.receivedFundingRate=펀딩 비율을 수신했습니다, 심볼 = {0}, 값 = {1}
messagingService.info.receivedNewOrder=새 주문을 수신했습니다, 토픽 = {0}, 키 = {1}
messagingService.info.receivedCancelOrder=주문 취소를 수신했습니다, 토픽 = {0}, 키 = {1}
messagingService.info.receivedFundingRateUpdate=펀딩 비율 업데이트를 수신했습니다, 토픽 = {0}, 키 = {1}
messagingService.info.receivedTrade=거래를 수신했습니다, 심볼 = {0}, 값 = {1}
messagingService.info.receivedLiquidation=청산을 수신했습니다, 심볼 = {0}, 값 = {1}
messagingService.info.markPriceFromHeader=헤더에서 마크 가격을 성공적으로 가져왔습니다, 마크 가격 = {0}
messagingService.warn.markPriceFromService=헤더에서 마크 가격을 가져올 수 없습니다, 서비스에서 마크 가격을 사용합니다, 오류 = {0}
messagingService.warn.useLiquidationPrice=마크 가격을 찾을 수 없습니다, 청산 가격을 사용합니다, 심볼 = {0}
messagingService.info.processLiquidation=마크 가격 = {0}, 심볼 = {1}, 포지션 ID = {2}로 청산 처리 중
messagingService.error.processLiquidation=청산 처리 오류, 심볼 = {0}, 값 = {1}

# Special Order Service messages
specialOrderService.error.processTimeOrders=시간 주문 처리 오류
specialOrderService.error.processExpiredOrders=만료된 주문 처리 오류
specialOrderService.error.processTriggerOrders=트리거 주문 처리 오류
specialOrderService.error.checkTriggerOrders=트리거 주문 확인 오류, 심볼 = {0}
specialOrderService.error.processTimeOrder=시간 주문 처리 오류

specialOrderService.info.processTimeOrders=시간 주문 처리 중
specialOrderService.info.processExpiredOrders=만료된 주문 처리 중
specialOrderService.info.processTriggerOrders=트리거 주문 확인 중

specialOrderService.debug.processTimeOrder=시간 주문 처리 중, 주문 ID = {0}

# Report Service messages
reportService.error.createReportsAsync=완료된 모든 주문에 대한 비동기 보고서 생성 오류
reportService.error.processStatisticsAsync=비동기 통계 처리 오류

reportService.info.createReportsAsync=완료된 모든 주문에 대한 비동기 보고서 생성 중
reportService.info.completedOrdersCount=보고서가 없는 완료된 주문 수: {0}
reportService.info.createReportsAsyncCompleted=완료된 모든 주문에 대한 비동기 보고서 생성 완료
reportService.info.processStatisticsAsync=비동기 통계 처리 중, 시작일 = {0}, 종료일 = {1}
reportService.info.processStatisticsAsyncCompleted=비동기 통계 처리 완료, 시작일 = {0}, 종료일 = {1}

# Sharding Manager messages
shardingManager.error.rebalancingInterrupted=포드 {0}에 대한 심볼 재조정 중 중단됨

shardingManager.warn.rebalancingLockFailed=포드 {0}에 대한 심볼 재조정을 위한 잠금을 획득할 수 없음
shardingManager.warn.no.active.pods=재조정을 위한 활성 포드를 찾을 수 없음

shardingManager.info.initialized=포드 {0}에 대한 샤딩 관리자 초기화됨
shardingManager.info.symbolAssigned=심볼 {0}이(가) 포드 {1}에 할당됨, 이전 소유자: {2}
shardingManager.info.symbolUnassigned=심볼 {0}이(가) 포드 {1}에서 할당 해제됨
shardingManager.info.symbolRemoved=심볼 {0}이(가) 포드 매핑에서 제거됨
shardingManager.info.rebalancingStarted=포드 {0}에 대한 심볼 재조정 시작
shardingManager.info.rebalancingCompleted=포드 {0}에 대한 심볼 재조정 완료
shardingManager.info.symbolMoved=심볼 {0}이(가) 포드 {1}에서 포드 {2}로 이동됨
shardingManager.info.rebalanceStarted=포드 {0}에 대한 심볼 재조정 시작
shardingManager.info.rebalanceCompleted=포드 {0}에 대한 심볼 재조정 완료

shardingManager.debug.heartbeatSent=포드 {0}에 대한 하트비트 전송됨

# Reshard Executor messages
reshardExecutor.error.lockTimeout=심볼 {0}에 대한 잠금을 획득할 수 없습니다
reshardExecutor.error.lockInterrupted=심볼 {0}에 대한 잠금을 기다리는 동안 중단되었습니다
reshardExecutor.error.reshardFailed=심볼 {0}에 대한 리샤딩 실패
reshardExecutor.error.statusUpdateFailed=심볼 {0}의 리샤딩 상태를 {1}(으)로 업데이트하지 못했습니다
reshardExecutor.error.prepareNotificationFailed=심볼 {0}에 대한 준비 알림 전송 실패
reshardExecutor.error.pauseOrderProcessingFailed=심볼 {0}에 대한 주문 처리 일시 중지 실패
reshardExecutor.error.engineNotFound=심볼 {0}에 대한 매칭 엔진을 찾을 수 없습니다
reshardExecutor.error.snapshotCreationFailed=심볼 {0}에 대한 스냅샷 생성 실패
reshardExecutor.error.snapshotTransferFailed=심볼 {0}의 스냅샷을 샤드 {1}로 전송하지 못했습니다
reshardExecutor.error.mappingUpdateFailed=심볼 {0}의 매핑을 샤드 {1}로 업데이트하지 못했습니다
reshardExecutor.error.completeNotificationFailed=심볼 {0}에 대한 완료 알림 전송 실패
reshardExecutor.error.rollbackFailed=심볼 {0}에 대한 롤백 실패

reshardExecutor.info.executingReshard=심볼 {0}을(를) 샤드 {1}에서 샤드 {2}로 리샤딩 실행 중
reshardExecutor.info.statusUpdated=심볼 {0}의 리샤딩 상태를 {1}(으)로 업데이트했습니다
reshardExecutor.info.prepareNotificationSent=심볼 {0}에 대한 준비 알림 전송됨
reshardExecutor.info.orderProcessingPaused=심볼 {0}에 대한 주문 처리가 일시 중지됨
reshardExecutor.info.snapshotCreated=심볼 {0}에 대한 스냅샷 생성됨
reshardExecutor.info.snapshotTransferred=심볼 {0}의 스냅샷이 샤드 {1}로 전송됨
reshardExecutor.info.mappingUpdated=심볼 {0}의 매핑이 샤드 {1}로 업데이트됨
reshardExecutor.info.completeNotificationSent=심볼 {0}에 대한 완료 알림 전송됨
reshardExecutor.info.rollingBack=심볼 {0}에 대한 리샤딩 롤백 중
reshardExecutor.info.rollbackCompleted=심볼 {0}에 대한 롤백 완료
reshardExecutor.info.reshardCompleted=심볼 {0}을(를) 샤드 {1}에서 샤드 {2}로 리샤딩 완료

# Reshard Planner messages
reshardPlanner.info.planningReshard=심볼 {0}에 대한 리샤딩 계획 중, 이유: {1}
reshardPlanner.info.reshardPlanExists=심볼 {0}은(는) 이미 리샤딩 계획이 있습니다
reshardPlanner.error.currentShardNotFound=심볼 {0}에 대한 현재 샤드를 찾을 수 없습니다
reshardPlanner.error.targetShardNotFound=심볼 {0}에 대한 대상 샤드를 찾을 수 없습니다
reshardPlanner.info.sameShard=심볼 {0}은(는) 이미 샤드 {1}에 있으므로 리샤딩이 필요하지 않습니다
reshardPlanner.info.reshardPlanCreated=심볼 {0}에 대한 리샤딩 계획이 생성되었습니다 (샤드 {1}에서 샤드 {2}로)
reshardPlanner.error.reshardPlanNotFound=심볼 {0}에 대한 리샤딩 계획을 찾을 수 없습니다
reshardPlanner.info.approvingReshardPlan=심볼 {0}에 대한 리샤딩 계획을 승인 중 (샤드 {1}에서 샤드 {2}로)
reshardPlanner.info.rejectingReshardPlan=심볼 {0}에 대한 리샤딩 계획을 거부 중

# Hot Spot Detector messages
hotSpotDetector.debug.detectingHotSpots=핫스팟 감지 중
hotSpotDetector.info.hotSpotsDetected={0}개의 핫스팟이 감지되었습니다
hotSpotDetector.info.hotSpotDetails=핫스팟: 심볼={0}, 심각도={1}
hotSpotDetector.debug.hotSpotRecentlyDetected=핫스팟 {0}이(가) 최근에 감지되었으므로 건너뜁니다
hotSpotDetector.info.lowSeverityHotSpot=핫스팟 {0}은(는) 심각도가 낮으므로 로깅만 합니다

# Load Imbalance Detector messages
loadImbalanceDetector.debug.detectingImbalance=부하 불균형 감지 중
loadImbalanceDetector.info.imbalanceDetected=샤드 {1}에 대한 {0} 불균형이 감지되었습니다: {2}% (임계값: {3}%)
loadImbalanceDetector.info.processingImbalances={0}개의 불균형 처리 중
loadImbalanceDetector.info.shardImbalanceDetails=샤드 {0}에 대한 불균형: {1}
loadImbalanceDetector.debug.imbalanceRecentlyDetected=샤드 {0}에 대한 불균형이 최근에 감지되었으므로 건너뜁니다

# Symbol Selector messages
symbolSelector.info.selectingSymbols=샤드 {0}의 부하를 재조정하기 위한 심볼 선택 중
symbolSelector.info.notEnoughInstances=샤드 {0}에 부하를 재조정할 인스턴스가 충분하지 않습니다
symbolSelector.error.cannotDetermineInstances=샤드 {0}에 대한 최고 및 최저 부하 인스턴스를 결정할 수 없습니다
symbolSelector.info.loadInstancesIdentified=최고 부하 인스턴스 {0}과(와) 최저 부하 인스턴스 {1}을(를) 식별했습니다
symbolSelector.info.noSymbolsFound=인스턴스 {0}에 대한 심볼을 찾을 수 없습니다
symbolSelector.info.noSuitableSymbolsFound=이동할 적합한 심볼을 찾을 수 없습니다
symbolSelector.info.symbolsSelected=인스턴스 {1}에서 인스턴스 {2}로 이동할 {0}개의 심볼을 선택했습니다

# Migration Planner messages
migrationPlanner.info.planningMigration=인스턴스 {1}에서 인스턴스 {2}로 {0}개의 심볼 마이그레이션 계획 중
migrationPlanner.info.tooManyMigrations=이미 {0}개의 마이그레이션이 진행 중이며, 제한 {1}을(를) 초과합니다
migrationPlanner.info.migrationPlanExists=심볼 {0}은(는) 이미 마이그레이션 계획이 있습니다
migrationPlanner.info.migrationPlanCreated=심볼 {0}에 대한 마이그레이션 계획이 생성되었습니다 (인스턴스 {1}에서 인스턴스 {2}로)
migrationPlanner.error.migrationPlanNotFound=심볼 {0}에 대한 마이그레이션 계획을 찾을 수 없습니다
migrationPlanner.info.approvingMigrationPlan=심볼 {0}에 대한 마이그레이션 계획을 승인 중 (인스턴스 {1}에서 인스턴스 {2}로)
migrationPlanner.info.rejectingMigrationPlan=심볼 {0}에 대한 마이그레이션 계획을 거부 중

# Migration Executor messages
migrationExecutor.info.executingMigration=심볼 {0}을(를) 인스턴스 {1}에서 인스턴스 {2}로 마이그레이션 실행 중
migrationExecutor.error.lockTimeout=심볼 {0}에 대한 잠금을 획득할 수 없습니다
migrationExecutor.error.lockInterrupted=심볼 {0}에 대한 잠금을 기다리는 동안 중단되었습니다
migrationExecutor.error.migrationFailed=심볼 {0}에 대한 마이그레이션 실패
migrationExecutor.info.statusUpdated=심볼 {0}의 마이그레이션 상태를 {1}(으)로 업데이트했습니다
migrationExecutor.error.statusUpdateFailed=심볼 {0}의 마이그레이션 상태를 {1}(으)로 업데이트하지 못했습니다
migrationExecutor.info.prepareNotificationSent=심볼 {0}에 대한 준비 알림 전송됨
migrationExecutor.error.prepareNotificationFailed=심볼 {0}에 대한 준비 알림 전송 실패
migrationExecutor.info.orderProcessingPaused=심볼 {0}에 대한 주문 처리가 일시 중지됨
migrationExecutor.error.pauseOrderProcessingFailed=심볼 {0}에 대한 주문 처리 일시 중지 실패
migrationExecutor.info.snapshotCreated=심볼 {0}에 대한 스냅샷 생성됨
migrationExecutor.error.engineNotFound=심볼 {0}에 대한 매칭 엔진을 찾을 수 없습니다
migrationExecutor.error.snapshotCreationFailed=심볼 {0}에 대한 스냅샷 생성 실패
migrationExecutor.info.snapshotTransferred=심볼 {0}의 스냅샷이 인스턴스 {1}로 전송됨
migrationExecutor.error.snapshotTransferFailed=심볼 {0}의 스냅샷을 인스턴스 {1}로 전송하지 못했습니다
migrationExecutor.info.mappingUpdated=심볼 {0}의 매핑이 인스턴스 {1}로 업데이트됨
migrationExecutor.error.mappingUpdateFailed=심볼 {0}의 매핑을 인스턴스 {1}로 업데이트하지 못했습니다
migrationExecutor.info.completeNotificationSent=심볼 {0}에 대한 완료 알림 전송됨
migrationExecutor.error.completeNotificationFailed=심볼 {0}에 대한 완료 알림 전송 실패
migrationExecutor.info.rollingBack=심볼 {0}에 대한 마이그레이션 롤백 중
migrationExecutor.info.rollbackCompleted=심볼 {0}에 대한 롤백 완료
migrationExecutor.error.rollbackFailed=심볼 {0}에 대한 롤백 실패
migrationExecutor.info.migrationCompleted=심볼 {0}을(를) 인스턴스 {1}에서 인스턴스 {2}로 마이그레이션 완료

# Settlement messages
settlement.error.scheduledFundingSettlement=심볼 {0}에 대한 예약된 펀딩 정산 오류
settlement.error.scheduledContractSettlement=심볼 {0}에 대한 예약된 계약 정산 오류
settlement.error.initializeFundingSettlementSchedules=펀딩 정산 일정 초기화 오류
settlement.error.initializeContractSettlementSchedules=계약 정산 일정 초기화 오류
settlement.error.calculateFundingRate=심볼 {0}에 대한 펀딩 비율 계산 오류
settlement.error.settleContract=심볼 {0}에 대한 계약 정산 오류
settlement.error.settleFunding=심볼 {0}, 타임스탬프 {1}에 대한 펀딩 정산 오류
settlement.error.autoFundingSettlement=심볼 {0}에 대한 자동 펀딩 정산 오류
settlement.error.autoContractSettlement=심볼 {0}에 대한 자동 계약 정산 오류
settlement.error.autoFundingSettlementAll=모든 심볼에 대한 자동 펀딩 정산 오류
settlement.error.autoContractSettlementAll=모든 심볼에 대한 자동 계약 정산 오류
settlement.error.scheduleFundingSettlement=심볼 {0}, 일정 {1}에 대한 펀딩 정산 일정 설정 오류
settlement.error.scheduleContractSettlement=심볼 {0}, 일정 {1}에 대한 계약 정산 일정 설정 오류
settlement.error.scheduleFundingSettlementAll=일정 {0}으로 모든 심볼에 대한 펀딩 정산 일정 설정 오류
settlement.error.scheduleContractSettlementAll=일정 {0}으로 모든 심볼에 대한 계약 정산 일정 설정 오류
settlement.error.cancelFundingSettlementSchedule=심볼 {0}에 대한 펀딩 정산 일정 취소 오류
settlement.error.cancelContractSettlementSchedule=심볼 {0}에 대한 계약 정산 일정 취소 오류
settlement.error.cancelFundingSettlementScheduleAll=모든 심볼에 대한 펀딩 정산 일정 취소 오류
settlement.error.cancelContractSettlementScheduleAll=모든 심볼에 대한 계약 정산 일정 취소 오류
settlement.error.getNextFundingSettlementTime=심볼 {0}에 대한 다음 펀딩 정산 시간 가져오기 오류
settlement.error.fundingSettlement=심볼 {0}, 타임스탬프 {1}에 대한 펀딩 정산 오류
settlement.error.fundingSettlementRetry=심볼 {0}, 타임스탬프 {1}에 대한 펀딩 정산을 3번 재시도했지만 실패함
settlement.error.contractSettlementRetry=심볼 {0}, 타임스탬프 {1}에 대한 계약 정산을 3번 재시도했지만 실패함

settlement.warn.fundingRateZero=심볼 {0}에 대한 펀딩 비율이 0입니다
settlement.warn.settlementPriceZero=심볼 {0}에 대한 정산 가격이 0입니다
settlement.warn.noPositions=심볼 {0}에 대한 포지션을 찾을 수 없습니다

settlement.info.scheduledFundingSettlementStarted=심볼 {0}에 대한 예약된 펀딩 정산 시작됨
settlement.info.scheduledFundingSettlementCompleted=심볼 {0}에 대한 예약된 펀딩 정산 완료됨
settlement.info.scheduledContractSettlementStarted=심볼 {0}에 대한 예약된 계약 정산 시작됨
settlement.info.scheduledContractSettlementCompleted=심볼 {0}에 대한 예약된 계약 정산 완료됨
settlement.info.initializeFundingSettlementSchedules=펀딩 정산 일정 초기화 중
settlement.info.initializeFundingSettlementSchedulesCompleted=펀딩 정산 일정 초기화 완료됨
settlement.info.initializeContractSettlementSchedules=계약 정산 일정 초기화 중
settlement.info.initializeContractSettlementSchedulesCompleted=계약 정산 일정 초기화 완료됨
settlement.info.autoFundingSettlementStarted=심볼 {0}에 대한 자동 펀딩 정산 시작됨
settlement.info.autoFundingSettlementCompleted=심볼 {0}에 대한 자동 펀딩 정산 완료됨
settlement.info.autoContractSettlementStarted=심볼 {0}에 대한 자동 계약 정산 시작됨
settlement.info.autoContractSettlementCompleted=심볼 {0}에 대한 자동 계약 정산 완료됨
settlement.info.autoFundingSettlementAllCompleted=모든 심볼에 대한 자동 펀딩 정산 완료됨
settlement.info.autoContractSettlementAllCompleted=모든 심볼에 대한 자동 계약 정산 완료됨
settlement.info.scheduleFundingSettlement=심볼 {0}, 다음 시간 {1}에 대한 펀딩 정산 일정 설정 중
settlement.info.scheduleContractSettlement=심볼 {0}, 다음 시간 {1}에 대한 계약 정산 일정 설정 중
settlement.info.scheduleFundingSettlementCompleted=심볼 {0}, 다음 시간 {1}에 대한 펀딩 정산 일정 설정 완료됨
settlement.info.scheduleContractSettlementCompleted=심볼 {0}, 다음 시간 {1}에 대한 계약 정산 일정 설정 완료됨
settlement.info.scheduleFundingSettlementAll=일정 {0}으로 모든 심볼에 대한 펀딩 정산 일정 설정 중
settlement.info.scheduleFundingSettlementAllCompleted=일정 {0}으로 모든 심볼에 대한 펀딩 정산 일정 설정 완료됨, 성공 수: {1}
settlement.info.scheduleContractSettlementAll=일정 {0}으로 모든 심볼에 대한 계약 정산 일정 설정 중
settlement.info.scheduleContractSettlementAllCompleted=일정 {0}으로 모든 심볼에 대한 계약 정산 일정 설정 완료됨, 성공 수: {1}
settlement.info.cancelFundingSettlementSchedule=심볼 {0}에 대한 펀딩 정산 일정 취소 중
settlement.info.cancelFundingSettlementScheduleCompleted=심볼 {0}에 대한 펀딩 정산 일정 취소됨
settlement.info.cancelContractSettlementSchedule=심볼 {0}에 대한 계약 정산 일정 취소 중
settlement.info.cancelContractSettlementScheduleCompleted=심볼 {0}에 대한 계약 정산 일정 취소됨
settlement.info.cancelFundingSettlementScheduleAll=모든 심볼에 대한 펀딩 정산 일정 취소 중
settlement.info.cancelFundingSettlementScheduleAllCompleted=모든 심볼에 대한 펀딩 정산 일정 취소됨, 성공 수: {0}
settlement.info.cancelContractSettlementScheduleAll=모든 심볼에 대한 계약 정산 일정 취소 중
settlement.info.cancelContractSettlementScheduleAllCompleted=모든 심볼에 대한 계약 정산 일정 취소됨, 성공 수: {0}
settlement.info.fundingSettlementStarted=심볼 {0}, 타임스탬프 {1}에 대한 펀딩 정산 시작됨
settlement.info.fundingSettlementCompleted=심볼 {0}, 타임스탬프 {1}에 대한 펀딩 정산 완료됨
settlement.info.contractSettlementStarted=심볼 {0}, 타임스탬프 {1}에 대한 계약 정산 시작됨
settlement.info.contractSettlementCompleted=심볼 {0}, 타임스탬프 {1}에 대한 계약 정산 완료됨
settlement.info.autoFundingSettlementAllStarted=타임스탬프 {0}에 모든 심볼에 대한 자동 펀딩 정산 시작됨
settlement.info.autoContractSettlementAllStarted=타임스탬프 {0}에 모든 심볼에 대한 자동 계약 정산 시작됨

# Funding Service messages
fundingService.debug.calculateFundingRate=심볼 {0}, 인덱스 가격 {1}, 마크 가격 {2}에 대한 펀딩 비율 계산 중
fundingService.debug.getCurrentFundingRate=심볼 {0}에 대한 현재 펀딩 비율 가져오는 중
fundingService.debug.getNextFundingTime=심볼 {0}에 대한 다음 펀딩 시간 가져오는 중

fundingService.error.calculateFundingRate=심볼 {0}에 대한 펀딩 비율 계산 중 오류 발생
fundingService.error.getCurrentFundingRate=심볼 {0}에 대한 현재 펀딩 비율 가져오는 중 오류 발생
fundingService.error.getNextFundingTime=심볼 {0}에 대한 다음 펀딩 시간 가져오는 중 오류 발생

# Price Service messages
priceService.debug.getIndexPrice=심볼 {0}에 대한 인덱스 가격 가져오는 중
priceService.debug.getMarkPrice=심볼 {0}에 대한 마크 가격 가져오는 중
priceService.debug.calculateIndexPrice=심볼 {0}에 대한 인덱스 가격 계산 중
priceService.debug.calculateMarkPrice=심볼 {0}에 대한 마크 가격 계산 중

priceService.error.getIndexPrice=심볼 {0}에 대한 인덱스 가격 가져오는 중 오류 발생
priceService.error.getMarkPrice=심볼 {0}에 대한 마크 가격 가져오는 중 오류 발생
priceService.error.calculateIndexPrice=심볼 {0}에 대한 인덱스 가격 계산 중 오류 발생
priceService.error.calculateMarkPrice=심볼 {0}에 대한 마크 가격 계산 중 오류 발생
