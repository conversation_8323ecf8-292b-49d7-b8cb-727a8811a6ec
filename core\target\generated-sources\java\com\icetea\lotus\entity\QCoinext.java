package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QCoinext is a Querydsl query type for Coinext
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QCoinext extends EntityPathBase<Coinext> {

    private static final long serialVersionUID = -773036384L;

    public static final QCoinext coinext = new QCoinext("coinext");

    public final NumberPath<Integer> coinid = createNumber("coinid", Integer.class);

    public final StringPath coinname = createString("coinname");

    public final NumberPath<Integer> confirms = createNumber("confirms", Integer.class);

    public final NumberPath<Integer> decimals = createNumber("decimals", Integer.class);

    public final StringPath ext = createString("ext");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final BooleanPath isautowithdraw = createBoolean("isautowithdraw");

    public final BooleanPath isrecharge = createBoolean("isrecharge");

    public final BooleanPath iswithdraw = createBoolean("iswithdraw");

    public final StringPath mainaddress = createString("mainaddress");

    public final NumberPath<java.math.BigDecimal> maxwithdraw = createNumber("maxwithdraw", java.math.BigDecimal.class);

    public final StringPath memoaddress = createString("memoaddress");

    public final NumberPath<java.math.BigDecimal> minrecharge = createNumber("minrecharge", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minwithdraw = createNumber("minwithdraw", java.math.BigDecimal.class);

    public final NumberPath<Double> minwithdrawfee = createNumber("minwithdrawfee", Double.class);

    public final NumberPath<Integer> protocol = createNumber("protocol", Integer.class);

    public final StringPath protocolname = createString("protocolname");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final NumberPath<Double> withdrawfee = createNumber("withdrawfee", Double.class);

    public QCoinext(String variable) {
        super(Coinext.class, forVariable(variable));
    }

    public QCoinext(Path<? extends Coinext> path) {
        super(path.getType(), path.getMetadata());
    }

    public QCoinext(PathMetadata metadata) {
        super(Coinext.class, metadata);
    }

}

