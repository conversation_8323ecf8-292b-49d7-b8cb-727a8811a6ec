package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QActivityOrder is a Querydsl query type for ActivityOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QActivityOrder extends EntityPathBase<ActivityOrder> {

    private static final long serialVersionUID = 2027573807L;

    public static final QActivityOrder activityOrder = new QActivityOrder("activityOrder");

    public final NumberPath<Long> activityId = createNumber("activityId", Long.class);

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final StringPath baseSymbol = createString("baseSymbol");

    public final StringPath coinSymbol = createString("coinSymbol");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> freezeAmount = createNumber("freezeAmount", java.math.BigDecimal.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> price = createNumber("price", java.math.BigDecimal.class);

    public final NumberPath<Integer> state = createNumber("state", Integer.class);

    public final NumberPath<java.math.BigDecimal> turnover = createNumber("turnover", java.math.BigDecimal.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public QActivityOrder(String variable) {
        super(ActivityOrder.class, forVariable(variable));
    }

    public QActivityOrder(Path<? extends ActivityOrder> path) {
        super(path.getType(), path.getMetadata());
    }

    public QActivityOrder(PathMetadata metadata) {
        super(ActivityOrder.class, metadata);
    }

}

