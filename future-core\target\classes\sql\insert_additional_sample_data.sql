-- <PERSON><PERSON><PERSON><PERSON> dữ liệu mẫu cho bảng contract_coin
INSERT INTO contract_coin (symbol, name, base_symbol, quote_symbol, multiplier, min_volume, max_volume, price_precision, volume_precision, maintenance_margin_rate, initial_margin_rate, leverage_max, leverage_min, sort, fee, funding_rate_coefficient, max_funding_rate, min_funding_rate, funding_interval, margin_mode, enable, create_time, update_time)
VALUES
('DOGEUSDT', 'Dogecoin Perpetual', 'DOGE', 'USDT', 1.00000000, 10.00000000, 1000000.00000000, 0.00001000, 10.00000000, 0.00500000, 0.01000000, 50, 1, 6, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', 1, NOW(), NOW()),
('XRPUSDT', 'Ripple Perpetual', 'XRP', 'USDT', 1.00000000, 1.00000000, 100000.00000000, 0.00010000, 1.00000000, 0.00500000, 0.01000000, 50, 1, 7, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', 1, NOW(), NOW()),
('DOTUSDT', 'Polkadot Perpetual', 'DOT', 'USDT', 1.00000000, 0.10000000, 10000.00000000, 0.00100000, 0.10000000, 0.00500000, 0.01000000, 50, 1, 8, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', 1, NOW(), NOW()),
('AVAXUSDT', 'Avalanche Perpetual', 'AVAX', 'USDT', 1.00000000, 0.10000000, 10000.00000000, 0.00100000, 0.10000000, 0.00500000, 0.01000000, 50, 1, 9, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', 1, NOW(), NOW()),
('MATICUSDT', 'Polygon Perpetual', 'MATIC', 'USDT', 1.00000000, 1.00000000, 100000.00000000, 0.00010000, 1.00000000, 0.00500000, 0.01000000, 50, 1, 10, 0.00040000, 0.00025000, 0.00100000, -0.00100000, 8, 'CROSSED', 1, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_price_configuration
INSERT INTO contract_price_configuration (symbol, index_price_method, mark_price_method, custom_index_price_formula, custom_mark_price_formula, parameters, create_time, update_time)
VALUES
('DOGEUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('XRPUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('DOTUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('AVAXUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW()),
('MATICUSDT', 'WEIGHTED_AVERAGE', 'WEIGHTED_AVERAGE', NULL, NULL, '{"exchanges": ["binance", "okex", "huobi"], "weights": [0.5, 0.3, 0.2]}', NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_insurance_fund
INSERT INTO contract_insurance_fund (contract_id, symbol, balance, frozen_balance, available_balance, create_time, update_time)
VALUES
(6, 'DOGEUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(7, 'XRPUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(8, 'DOTUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(9, 'AVAXUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW()),
(10, 'MATICUSDT', 1000000.00000000, 0.00000000, 1000000.00000000, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_funding_rate
INSERT INTO contract_funding_rate (contract_id, symbol, rate, mark_price, index_price, time, next_time)
VALUES
(6, 'DOGEUSDT', 0.00025000, 0.12000000, 0.12010000, NOW(), NOW() + INTERVAL '8 HOUR'),
(7, 'XRPUSDT', 0.00022000, 0.50000000, 0.50050000, NOW(), NOW() + INTERVAL '8 HOUR'),
(8, 'DOTUSDT', 0.00018000, 15.00000000, 15.02000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(9, 'AVAXUSDT', 0.00020000, 25.00000000, 25.05000000, NOW(), NOW() + INTERVAL '8 HOUR'),
(10, 'MATICUSDT', 0.00015000, 0.80000000, 0.80100000, NOW(), NOW() + INTERVAL '8 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_user_position_mode
INSERT INTO contract_user_position_mode (member_id, position_mode, create_time, update_time)
VALUES
(6, 'ONE_WAY', NOW(), NOW()),
(7, 'HEDGE', NOW(), NOW()),
(8, 'ONE_WAY', NOW(), NOW()),
(9, 'HEDGE', NOW(), NOW()),
(10, 'ONE_WAY', NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_user_leverage_setting
INSERT INTO contract_user_leverage_setting (member_id, symbol, leverage, create_time, update_time)
VALUES
(6, 'DOGEUSDT', 10.00000000, NOW(), NOW()),
(6, 'XRPUSDT', 15.00000000, NOW(), NOW()),
(7, 'DOTUSDT', 20.00000000, NOW(), NOW()),
(8, 'AVAXUSDT', 10.00000000, NOW(), NOW()),
(9, 'MATICUSDT', 15.00000000, NOW(), NOW()),
(10, 'DOGEUSDT', 5.00000000, NOW(), NOW()),
(1, 'DOGEUSDT', 20.00000000, NOW(), NOW()),
(2, 'XRPUSDT', 15.00000000, NOW(), NOW()),
(3, 'DOTUSDT', 10.00000000, NOW(), NOW()),
(4, 'AVAXUSDT', 20.00000000, NOW(), NOW()),
(5, 'MATICUSDT', 10.00000000, NOW(), NOW());

-- Thêm dữ liệu mẫu cho bảng contract_wallet
INSERT INTO contract_wallet (member_id, coin, balance, frozen_balance, available_balance, address, unrealized_pnl, realized_pnl, used_margin, total_fee, total_funding_fee, is_locked, create_time, update_time, version)
VALUES
(6, 'USDT', 50000.00000000, 0.00000000, 50000.00000000, 'TXyz111222333', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(7, 'USDT', 75000.00000000, 0.00000000, 75000.00000000, 'TXyz222333444', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(8, 'USDT', 100000.00000000, 0.00000000, 100000.00000000, 'TXyz333444555', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(9, 'USDT', 25000.00000000, 0.00000000, 25000.00000000, 'TXyz444555666', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0),
(10, 'USDT', 30000.00000000, 0.00000000, 30000.00000000, 'TXyz555666777', 0.00000000, 0.00000000, 0.00000000, 0.00000000, 0.00000000, FALSE, NOW(), NOW(), 0);

-- Thêm dữ liệu mẫu cho bảng contract_order
INSERT INTO contract_order (order_id, member_id, contract_id, symbol, coin_symbol, base_symbol, direction, type, price, trigger_price, volume, filled_volume, turnover, fee, status, create_time, canceled_time, execute_time, expire_time, liquidation, adl, implied, source_order_id, oco_id, oco_order_no, leverage, reduce_only, callback_rate, time_in_force, trigger_type, slippage)
VALUES
('ORD202305010001', 6, 6, 'DOGEUSDT', 'DOGE', 'USDT', 'BUY', 'LIMIT', 0.12000000, NULL, 10000.00000000, 10000.00000000, 1200.00000000, 0.48000000, 'FILLED', NOW() - INTERVAL '3 HOUR', NULL, NOW() - INTERVAL '2 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD202305010002', 7, 6, 'DOGEUSDT', 'DOGE', 'USDT', 'SELL', 'LIMIT', 0.12000000, NULL, 10000.00000000, 10000.00000000, 1200.00000000, 0.48000000, 'FILLED', NOW() - INTERVAL '3 HOUR', NULL, NOW() - INTERVAL '2 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD202305010003', 8, 7, 'XRPUSDT', 'XRP', 'USDT', 'BUY', 'LIMIT', 0.50000000, NULL, 1000.00000000, 1000.00000000, 500.00000000, 0.20000000, 'FILLED', NOW() - INTERVAL '4 HOUR', NULL, NOW() - INTERVAL '3 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 15.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD202305010004', 9, 7, 'XRPUSDT', 'XRP', 'USDT', 'SELL', 'LIMIT', 0.50000000, NULL, 1000.00000000, 1000.00000000, 500.00000000, 0.20000000, 'FILLED', NOW() - INTERVAL '4 HOUR', NULL, NOW() - INTERVAL '3 HOUR 55 MINUTE', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 15.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD202305010005', 10, 8, 'DOTUSDT', 'DOT', 'USDT', 'BUY', 'MARKET', NULL, NULL, 100.00000000, 100.00000000, 1500.00000000, 0.60000000, 'FILLED', NOW() - INTERVAL '2 HOUR', NULL, NOW() - INTERVAL '2 HOUR', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'IOC', NULL, 0.00000000),
('ORD202305010006', 1, 8, 'DOTUSDT', 'DOT', 'USDT', 'SELL', 'MARKET', NULL, NULL, 100.00000000, 100.00000000, 1500.00000000, 0.60000000, 'FILLED', NOW() - INTERVAL '2 HOUR', NULL, NOW() - INTERVAL '2 HOUR', NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'IOC', NULL, 0.00000000),
('ORD202305010007', 2, 9, 'AVAXUSDT', 'AVAX', 'USDT', 'BUY', 'STOP_LIMIT', 25.00000000, 24.50000000, 50.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '1 HOUR', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 20.00000000, FALSE, NULL, 'GTC', 'MARK_PRICE', 0.00000000),
('ORD202305010008', 3, 10, 'MATICUSDT', 'MATIC', 'USDT', 'SELL', 'STOP_MARKET', NULL, 0.85000000, 1000.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '45 MINUTE', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 10.00000000, FALSE, NULL, 'GTC', 'LAST_PRICE', 0.00000000),
('ORD202305010009', 4, 6, 'DOGEUSDT', 'DOGE', 'USDT', 'BUY', 'LIMIT', 0.11500000, NULL, 5000.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '30 MINUTE', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 20.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000),
('ORD202305010010', 5, 7, 'XRPUSDT', 'XRP', 'USDT', 'SELL', 'LIMIT', 0.52000000, NULL, 500.00000000, 0.00000000, 0.00000000, 0.00000000, 'NEW', NOW() - INTERVAL '20 MINUTE', NULL, NULL, NULL, FALSE, FALSE, FALSE, NULL, NULL, NULL, 15.00000000, FALSE, NULL, 'GTC', NULL, 0.00000000);

-- Thêm dữ liệu mẫu cho bảng contract_trade
INSERT INTO contract_trade (symbol, buy_order_id, sell_order_id, buy_member_id, sell_member_id, price, volume, buy_fee, sell_fee, buy_turnover, sell_turnover, time)
VALUES
('DOGEUSDT', 'ORD202305010001', 'ORD202305010002', 6, 7, 0.12000000, 10000.00000000, 0.48000000, 0.48000000, 1200.00000000, 1200.00000000, NOW() - INTERVAL '2 HOUR 55 MINUTE'),
('XRPUSDT', 'ORD202305010003', 'ORD202305010004', 8, 9, 0.50000000, 1000.00000000, 0.20000000, 0.20000000, 500.00000000, 500.00000000, NOW() - INTERVAL '3 HOUR 55 MINUTE'),
('DOTUSDT', 'ORD202305010005', 'ORD202305010006', 10, 1, 15.00000000, 100.00000000, 0.60000000, 0.60000000, 1500.00000000, 1500.00000000, NOW() - INTERVAL '2 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_position
INSERT INTO contract_position (member_id, symbol, direction, volume, open_price, close_price, liquidation_price, maintenance_margin, margin, profit, margin_mode, leverage, create_time, update_time, remark, status)
VALUES
(6, 'DOGEUSDT', 'LONG', 10000.00000000, 0.12000000, NULL, 0.10800000, 6.00000000, 120.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '2 HOUR 55 MINUTE', NOW() - INTERVAL '2 HOUR 55 MINUTE', NULL, 'OPEN'),
(7, 'DOGEUSDT', 'SHORT', 10000.00000000, 0.12000000, NULL, 0.13200000, 6.00000000, 120.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '2 HOUR 55 MINUTE', NOW() - INTERVAL '2 HOUR 55 MINUTE', NULL, 'OPEN'),
(8, 'XRPUSDT', 'LONG', 1000.00000000, 0.50000000, NULL, 0.46700000, 2.50000000, 33.33333333, 0.00000000, 'CROSSED', 15.00000000, NOW() - INTERVAL '3 HOUR 55 MINUTE', NOW() - INTERVAL '3 HOUR 55 MINUTE', NULL, 'OPEN'),
(9, 'XRPUSDT', 'SHORT', 1000.00000000, 0.50000000, NULL, 0.53300000, 2.50000000, 33.33333333, 0.00000000, 'CROSSED', 15.00000000, NOW() - INTERVAL '3 HOUR 55 MINUTE', NOW() - INTERVAL '3 HOUR 55 MINUTE', NULL, 'OPEN'),
(10, 'DOTUSDT', 'LONG', 100.00000000, 15.00000000, NULL, 13.50000000, 7.50000000, 150.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '2 HOUR', NOW() - INTERVAL '2 HOUR', NULL, 'OPEN'),
(1, 'DOTUSDT', 'SHORT', 100.00000000, 15.00000000, NULL, 16.50000000, 7.50000000, 150.00000000, 0.00000000, 'CROSSED', 10.00000000, NOW() - INTERVAL '2 HOUR', NOW() - INTERVAL '2 HOUR', NULL, 'OPEN');

-- Thêm dữ liệu mẫu cho bảng contract_fee
INSERT INTO contract_fee (contract_id, member_id, symbol, order_id, direction, volume, price, turnover, fee, maker, create_time)
VALUES
(6, 6, 'DOGEUSDT', 'ORD202305010001', 'BUY', 10000.00000000, 0.12000000, 1200.00000000, 0.48000000, FALSE, NOW() - INTERVAL '2 HOUR 55 MINUTE'),
(6, 7, 'DOGEUSDT', 'ORD202305010002', 'SELL', 10000.00000000, 0.12000000, 1200.00000000, 0.48000000, TRUE, NOW() - INTERVAL '2 HOUR 55 MINUTE'),
(7, 8, 'XRPUSDT', 'ORD202305010003', 'BUY', 1000.00000000, 0.50000000, 500.00000000, 0.20000000, FALSE, NOW() - INTERVAL '3 HOUR 55 MINUTE'),
(7, 9, 'XRPUSDT', 'ORD202305010004', 'SELL', 1000.00000000, 0.50000000, 500.00000000, 0.20000000, TRUE, NOW() - INTERVAL '3 HOUR 55 MINUTE'),
(8, 10, 'DOTUSDT', 'ORD202305010005', 'BUY', 100.00000000, 15.00000000, 1500.00000000, 0.60000000, FALSE, NOW() - INTERVAL '2 HOUR'),
(8, 1, 'DOTUSDT', 'ORD202305010006', 'SELL', 100.00000000, 15.00000000, 1500.00000000, 0.60000000, TRUE, NOW() - INTERVAL '2 HOUR');

-- Thêm dữ liệu mẫu cho bảng contract_transaction
INSERT INTO contract_transaction (member_id, amount, coin, reference_id, create_time, type, address, fee, flag, real_fee, discount_fee, is_reward, contract_id, symbol, order_id, trade_id, leverage, margin_mode, realized_pnl, liquidation, adl, funding_fee)
VALUES
(6, -120.00000000, 'USDT', 'ORD202305010001', NOW() - INTERVAL '3 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 6, 'DOGEUSDT', 'ORD202305010001', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(7, -120.00000000, 'USDT', 'ORD202305010002', NOW() - INTERVAL '3 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 6, 'DOGEUSDT', 'ORD202305010002', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(8, -33.33333333, 'USDT', 'ORD202305010003', NOW() - INTERVAL '4 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 7, 'XRPUSDT', 'ORD202305010003', NULL, 15, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(9, -33.33333333, 'USDT', 'ORD202305010004', NOW() - INTERVAL '4 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 7, 'XRPUSDT', 'ORD202305010004', NULL, 15, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(10, -150.00000000, 'USDT', 'ORD202305010005', NOW() - INTERVAL '2 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 8, 'DOTUSDT', 'ORD202305010005', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(1, -150.00000000, 'USDT', 'ORD202305010006', NOW() - INTERVAL '2 HOUR', 'OPEN_POSITION', NULL, 0.00000000, 0, NULL, NULL, 0, 8, 'DOTUSDT', 'ORD202305010006', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(6, -0.48000000, 'USDT', 'FEE-ORD202305010001', NOW() - INTERVAL '2 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 6, 'DOGEUSDT', 'ORD202305010001', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(7, -0.48000000, 'USDT', 'FEE-ORD202305010002', NOW() - INTERVAL '2 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 6, 'DOGEUSDT', 'ORD202305010002', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(8, -0.20000000, 'USDT', 'FEE-ORD202305010003', NOW() - INTERVAL '3 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 7, 'XRPUSDT', 'ORD202305010003', NULL, 15, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(9, -0.20000000, 'USDT', 'FEE-ORD202305010004', NOW() - INTERVAL '3 HOUR 55 MINUTE', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 7, 'XRPUSDT', 'ORD202305010004', NULL, 15, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(10, -0.60000000, 'USDT', 'FEE-ORD202305010005', NOW() - INTERVAL '2 HOUR', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 8, 'DOTUSDT', 'ORD202305010005', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000),
(1, -0.60000000, 'USDT', 'FEE-ORD202305010006', NOW() - INTERVAL '2 HOUR', 'FEE', NULL, 0.00000000, 0, NULL, NULL, 0, 8, 'DOTUSDT', 'ORD202305010006', NULL, 10, 'CROSSED', 0.00000000, FALSE, FALSE, 0.00000000);
