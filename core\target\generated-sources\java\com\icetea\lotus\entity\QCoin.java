package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QCoin is a Querydsl query type for Coin
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QCoin extends EntityPathBase<Coin> {

    private static final long serialVersionUID = -1781822495L;

    public static final QCoin coin = new QCoin("coin");

    public final NumberPath<Integer> accountType = createNumber("accountType", Integer.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> canAutoWithdraw = createEnum("canAutoWithdraw", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> canRecharge = createEnum("canRecharge", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> canTransfer = createEnum("canTransfer", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> canWithdraw = createEnum("canWithdraw", com.icetea.lotus.constant.BooleanEnum.class);

    public final NumberPath<Double> cnyRate = createNumber("cnyRate", Double.class);

    public final StringPath coldWalletAddress = createString("coldWalletAddress");

    public final StringPath depositAddress = createString("depositAddress");

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> enableRpc = createEnum("enableRpc", com.icetea.lotus.constant.BooleanEnum.class);

    public final BooleanPath hasLegal = createBoolean("hasLegal");

    public final StringPath iconUrl = createString("iconUrl");

    public final StringPath infolink = createString("infolink");

    public final StringPath information = createString("information");

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> isPlatformCoin = createEnum("isPlatformCoin", com.icetea.lotus.constant.BooleanEnum.class);

    public final NumberPath<Double> maxTxFee = createNumber("maxTxFee", Double.class);

    public final NumberPath<java.math.BigDecimal> maxWithdrawAmount = createNumber("maxWithdrawAmount", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minerFee = createNumber("minerFee", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> minRechargeAmount = createNumber("minRechargeAmount", java.math.BigDecimal.class);

    public final NumberPath<Double> minTxFee = createNumber("minTxFee", Double.class);

    public final NumberPath<java.math.BigDecimal> minWithdrawAmount = createNumber("minWithdrawAmount", java.math.BigDecimal.class);

    public final StringPath name = createString("name");

    public final StringPath nameCn = createString("nameCn");

    public final NumberPath<Integer> sort = createNumber("sort", Integer.class);

    public final EnumPath<com.icetea.lotus.constant.CommonStatus> status = createEnum("status", com.icetea.lotus.constant.CommonStatus.class);

    public final StringPath unit = createString("unit");

    public final NumberPath<Double> usdRate = createNumber("usdRate", Double.class);

    public final NumberPath<Integer> withdrawScale = createNumber("withdrawScale", Integer.class);

    public final NumberPath<java.math.BigDecimal> withdrawThreshold = createNumber("withdrawThreshold", java.math.BigDecimal.class);

    public QCoin(String variable) {
        super(Coin.class, forVariable(variable));
    }

    public QCoin(Path<? extends Coin> path) {
        super(path.getType(), path.getMetadata());
    }

    public QCoin(PathMetadata metadata) {
        super(Coin.class, metadata);
    }

}

