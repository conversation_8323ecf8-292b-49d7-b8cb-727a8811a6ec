package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QQuickExchange is a Querydsl query type for QuickExchange
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QQuickExchange extends EntityPathBase<QuickExchange> {

    private static final long serialVersionUID = -2134378528L;

    public static final QQuickExchange quickExchange = new QQuickExchange("quickExchange");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> exAmount = createNumber("exAmount", java.math.BigDecimal.class);

    public final StringPath fromUnit = createString("fromUnit");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> rate = createNumber("rate", java.math.BigDecimal.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath toUnit = createString("toUnit");

    public QQuickExchange(String variable) {
        super(QuickExchange.class, forVariable(variable));
    }

    public QQuickExchange(Path<? extends QuickExchange> path) {
        super(path.getType(), path.getMetadata());
    }

    public QQuickExchange(PathMetadata metadata) {
        super(QuickExchange.class, metadata);
    }

}

