lotus:
  matching-engine:
    # <PERSON><PERSON><PERSON> hình kích hoạt các loại matching engine
    # <PERSON><PERSON> thể kích hoạt nhiều loại cùng lúc
    use-distributed: true
    use-lock-free: true
    use-optimized: true

    # <PERSON><PERSON><PERSON> hình thuật toán khớp lệnh
    default-algorithm: FIFO
    algorithm-by-symbol:
      BTC-USDT: FIFO
      ETH-USDT: PRO_RATA
      BNB-USDT: HYBRID
      SOL-USDT: TWAP

    # C<PERSON><PERSON> hình chuyển đổi tự động thuật toán khớp lệnh
    algorithm-switch:
      enabled: false
      pro-rata-market-depth-threshold: 10
      pro-rata-liquidity-threshold: 1000.0
      hybrid-market-depth-threshold: 5
      hybrid-liquidity-threshold: 500.0
      twap-volume-threshold: 100.0
      check-interval-seconds: 60

    # <PERSON><PERSON><PERSON> hình cho thuật toán TWAP
    twap:
      minimum-volume-threshold: 10.0
      max-slices: 10
      min-slices: 2
      volume-per-slice: 10.0
      interval-between-slices-ms: 1000

    # <PERSON><PERSON><PERSON> hình cho distributed matching engine
    distributed:
      # Thời gian timeout cho lock (giây)
      lock-timeout-seconds: 5
      # Thời gian giữ lock (giây)
      lock-lease-time-seconds: 30
      # <PERSON><PERSON><PERSON> thước phân đoạn cho distributed order book
      segment-size: "100"
      # Có sử dụng distributed order book không
      use-distributed-order-book: true

    # Cấu hình cho optimized matching engine
    optimized:
      # Kích thước cache cho order book
      order-book-cache-size: 1000
      # Thời gian hết hạn cache (giây)
      cache-expiration-seconds: 60

    # Cấu hình cho lock-free matching engine
    lock-free:
      # Số lần thử lại tối đa khi CAS thất bại
      max-retries: 10
      # Thời gian chờ giữa các lần thử lại (mili giây)
      retry-delay-millis: 10
