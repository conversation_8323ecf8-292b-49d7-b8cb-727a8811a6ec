-- Thê<PERSON> cột remark vào bảng contract_insurance_fund nếu chưa tồn tại
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contract_insurance_fund' AND column_name = 'remark'
    ) THEN
        ALTER TABLE contract_insurance_fund ADD COLUMN remark VARCHAR(255);
    END IF;
END $$;

-- Thêm cột total_used vào bảng contract_insurance_fund nếu chưa tồn tại
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contract_insurance_fund' AND column_name = 'total_used'
    ) THEN
        ALTER TABLE contract_insurance_fund ADD COLUMN total_used DECIMAL(18,8) DEFAULT 0;
    END IF;
END $$;

-- Thêm cột total_deposit vào bảng contract_insurance_fund nếu chưa tồn tại
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contract_insurance_fund' AND column_name = 'total_deposit'
    ) THEN
        ALTER TABLE contract_insurance_fund ADD COLUMN total_deposit DECIMAL(18,8) DEFAULT 0;
    END IF;
END $$;
