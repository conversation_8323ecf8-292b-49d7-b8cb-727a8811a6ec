package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QPaymentType is a Querydsl query type for PaymentType
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QPaymentType extends EntityPathBase<PaymentType> {

    private static final long serialVersionUID = -1590801648L;

    public static final QPaymentType paymentType = new QPaymentType("paymentType");

    public final StringPath code = createString("code");

    public final StringPath configJson = createString("configJson");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public QPaymentType(String variable) {
        super(PaymentType.class, forVariable(variable));
    }

    public QPaymentType(Path<? extends PaymentType> path) {
        super(path.getType(), path.getMetadata());
    }

    public QPaymentType(PathMetadata metadata) {
        super(PaymentType.class, metadata);
    }

}

