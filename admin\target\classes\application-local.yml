keycloak:
  auth-server-url: http://************:8082
  realm: cex-lotus
  resource: cex-admin
  credentials:
    secret: 6e4Le4p6rMNw6LZiUDEMHXUbCpq6YAAK

spring:
  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:8082/realms/cex-lotus

  datasource:
    driver-class-name: org.postgresql.Driver
    url: ${DATASOURCE_URL:*******************************************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:lVQghABsax17OsBnjuMDqgOOGrt4T1LT0fN3jcgRDoYKyqxkjz2Ydl2XujUYtmZW}
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:public}
    show-sql: ${SHOW_SQL:true}
    database: postgresql
  data:
    mongodb:
      #      uri: ${SPRING_MONGODB_URI:****************************************************}
      database: admin
      host: ************
      port: 27017
      username: root
      password: 2m0881Xc30Wh

    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:2m0881Xc30Wh}
      connect-timeout: ${REDIS_TIMEOUT:30000}
      jedis:
        pool:
          min-idle: 20
          max-idle: 100
          max-wait: 60000
          max-active: 300
    elasticsearch:
      uris: ${ES_URI:localhost}:9200

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}
      discovery:
        enabled: ${CONSUL_DISCOVERY:true}
        service-name: admin
        health-check-path: /admin/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true
        instance-id: ${spring.application.name}-${random.value}

    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true



  jackson:
    serialization:
      indent_output: ${SPRING_JACKSON_INDENT_OUTPUT:true}
    date-format: ${SPRING_JACKSON_DATE_FORMAT:yyyy-MM-dd HH:mm:ss}
    time-zone: ${SPRING_JACKSON_TIME_ZONE:GMT+8}

  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:************:9092}
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      batch:
        size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer:
        memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
    consumer:
      enable:
        auto:
          commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      session:
        timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto:
        commit:
          interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:100}
        offset:
          reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      group:
        id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
      concurrency: ${KAFKA_CONSUMER_CONCURRENCY:9}
      maxPollRecordsConfig: ${KAFKA_CONSUMER_MAX_POLL_RECORDS:50}

  devtools:
    restart:
      enabled: ${SPRING_DEVTOOLS_RESTART_ENABLED:true}

  freemarker:
    cache: ${SPRING_FREEMARKER_CACHE:false}
    template-loader-path: ${SPRING_FREEMARKER_TEMPLATE_LOADER_PATH:classpath:/templates}

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}


management:
  context-path: /actuator
  health:
    mail:
      enabled: false
    elasticsearch:
      enabled: false
    redis:
      enabled: false
  security:
    enabled: false


endpoints:
  health:
    sensitive: false
  info:
    sensitive: false
  metrics:
    sensitive: false

aliyun:
  accessKeyId: ${ALIYUN_ACCESS_KEY_ID:LTAI5zG6W9gFE32Uw}
  accessKeySecret: ${ALIYUN_ACCESS_KEY_SECRET:D0RFaFT55JSU3223Daqln1T2uWGFP}
  ossEndpoint: ${ALIYUN_OSS_ENDPOINT:oss-cn-shenzhen.aliyuncs.com}
  ossBucketName: ${ALIYUN_OSS_BUCKET_NAME:bizzan}
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

sms:
  driver: ${SMS_DRIVER:diyi}
  gateway: ${SMS_GATEWAY:}
  username: ${SMS_USERNAME:**********}
  password: ${SMS_PASSWORD:4901B0E56BD8CB679D8CAA321133}
  sign: ${SMS_SIGN:BIZZAN}

commission:
  need:
    real-name: ${COMMISSION_NEED_REAL_NAME:1}
  promotion:
    second-level: ${COMMISSION_PROMOTION_SECOND_LEVEL:1}

spark:
  system:
    md5:
      key: ${SPARK_SYSTEM_MD5_KEY:XehGyeyrVgOV4P8Uf70REVpIw332iVNwNs}
    work-id: ${SPARK_SYSTEM_WORK_ID:1}
    data-center-id: ${SPARK_SYSTEM_DATA_CENTER_ID:1}
    host: ${SPARK_SYSTEM_HOST:smtp.126.com}
    name: ${SPARK_SYSTEM_NAME:BIZZAN}
    admins: ${SPARK_SYSTEM_ADMINS:<EMAIL>}
    admin-phones: ${SPARK_SYSTEM_ADMIN_PHONES:18000000}

access:
  key:
    id: ${ACCESS_KEY_ID:}
    secret: ${ACCESS_KEY_SECRET:}

es:
  username: ${ES_USERNAME:}
  password: ${ES_PASSWORD:}
  mine:
    index: ${ES_MINE_INDEX:}
    type: ${ES_MINE_TYPE:}
  public:
    ip: ${ES_PUBLIC_IP:}
  private:
    ip: ${ES_PRIVATE_IP:#}
  port: ${ES_PORT:9200}

shiro:
  enabled: false
  web:
    enabled: false
  sessionManager:
    sessionIdCookieEnabled: false
    sessionIdUrlRewritingEnabled: false

google:
  host: BIZZAN.PRO

oss:
  name: oss

email:
  driver: java

# Add this under the existing configuration
logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.security.web: DEBUG
    org.springframework.security.authentication: DEBUG
    org.springframework.security.access: DEBUG
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

cex-security:
  resource-server-enabled: true
  permit-all-endpoints:
    - "/system/employee/login"
    - "/system/employee/logout"
    - "/system/employee/refresh-token"

#Minio Config
minio:
  protocol: ${MINIO_PROTOCOL:http}
  host: ${MINIO_HOST:************}
  port: ${MINIO_PORT:9000}
  access-key: ${MINO_ACCESS_KEY:VTp9kKgOQBdn8OdCHn0V}
  secret-key: ${MINIO_SECRET-KEY:Fda7Rfd1Roafn0e9SND57T2T16MXfkIlVQaK6fJe}
  bucket-name: ${MINIO_BUCKET_NAME:icetea-software-file}
