package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberRecord is a Querydsl query type for MemberRecord
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberRecord extends EntityPathBase<MemberRecord> {

    private static final long serialVersionUID = -1993304869L;

    public static final QMemberRecord memberRecord = new QMemberRecord("memberRecord");

    public final StringPath action = createString("action");

    public final NumberPath<Integer> actionType = createNumber("actionType", Integer.class);

    public final NumberPath<Integer> analysised = createNumber("analysised", Integer.class);

    public final StringPath cityName = createString("cityName");

    public final StringPath cityNameZh = createString("cityNameZh");

    public final StringPath countryName = createString("countryName");

    public final StringPath countryNameZh = createString("countryNameZh");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final StringPath email = createString("email");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath ip = createString("ip");

    public final StringPath isoCode = createString("isoCode");

    public final StringPath latitude = createString("latitude");

    public final StringPath longitude = createString("longitude");

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final StringPath phone = createString("phone");

    public final StringPath remark = createString("remark");

    public final StringPath result = createString("result");

    public final StringPath subdivision = createString("subdivision");

    public final StringPath subdivisionZh = createString("subdivisionZh");

    public QMemberRecord(String variable) {
        super(MemberRecord.class, forVariable(variable));
    }

    public QMemberRecord(Path<? extends MemberRecord> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberRecord(PathMetadata metadata) {
        super(MemberRecord.class, metadata);
    }

}

