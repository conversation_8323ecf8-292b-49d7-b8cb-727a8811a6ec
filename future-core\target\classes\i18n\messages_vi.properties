# Vietnamese messages

# Common messages
common.success=Thành công
common.error=Lỗi
common.warning=Cảnh báo
common.info=Thông tin

# Order Matching messages
orderMatching.error.cancelOrder=Lỗi khi hủy lệnh, orderId = {0}, symbol = {1}
orderMatching.error.placeOrder=Lỗi khi đặt lệnh, orderId = {0}, symbol = {1}
orderMatching.error.cancelAllOrders=Lỗi khi hủy tất cả lệnh, memberId = {0}, symbol = {1}
orderMatching.error.liquidatePosition=Lỗi khi thanh lý vị thế, positionId = {0}, symbol = {1}
orderMatching.error.matchOrderFIFO=Lỗi khi khớp lệnh FIFO, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderProRata=Lỗi khi khớp lệnh Pro-rata, orderId = {0}, symbol = {1}
orderMatching.error.matchOrderHybrid=Lỗi khi khớp lệnh Hybrid, orderId = {0}, symbol = {1}
orderMatching.error.shutdown=Lỗi khi giải phóng tài nguyên của OrderMatchingEngineService
orderMatching.error.syncContracts=Đồng bộ hóa matching engine thất bại
orderMatching.error.initEngine=Lỗi khi khởi tạo matching engine, symbol = {0}
orderMatching.error.checkTriggerOrders=Lỗi khi kiểm tra lệnh chờ, symbol = {0}
orderMatching.error.checkLiquidations=Lỗi khi kiểm tra thanh lý, symbol = {0}
orderMatching.error.lock.timeout=Không thể lấy lock cho symbol: {0}
orderMatching.error.lock.interrupted=Bị gián đoạn khi chờ lock cho symbol: {0}
orderMatching.error.contract.notFound=Không tìm thấy hợp đồng cho symbol: {0}
orderMatching.error.setAlgorithm=Lỗi khi thiết lập thuật toán khớp lệnh cho symbol: {0}, thuật toán: {1}
orderMatching.error.getOrderBook=Lỗi khi lấy sổ lệnh cho symbol: {0}
orderMatching.info.algorithmSet=Đã thiết lập thuật toán khớp lệnh cho symbol: {0}, thuật toán: {1}

orderMatching.warn.engineNotFound=Không tìm thấy matching engine cho symbol {0}
orderMatching.warn.invalidSymbol=Symbol không hợp lệ
orderMatching.warn.invalidOrder=Lệnh không hợp lệ
orderMatching.warn.tradingHalt=Giao dịch đang tạm dừng cho symbol {0}
orderMatching.warn.enabledContractsNotFound=Không thể lấy danh sách hợp đồng đã kích hoạt
orderMatching.warn.symbol.notOwned=Symbol {0} không được gán cho pod này

orderMatching.info.engineInitialized=Đã khởi tạo matching engine cho symbol {0}
orderMatching.info.engineShutdown=Đã dừng và giải phóng tài nguyên cho matching engine, symbol = {0}
orderMatching.info.tradingPaused=Đã tạm dừng giao dịch cho symbol {0}
orderMatching.info.tradingResumed=Đã tiếp tục giao dịch cho symbol {0}
orderMatching.info.syncCompleted=Đồng bộ hóa hoàn tất. Số lượng matching engine: {0}
orderMatching.info.syncStarted=Đồng bộ hóa matching engine với hợp đồng
orderMatching.info.shutdownStarted=Đang giải phóng tài nguyên của OrderMatchingEngineService
orderMatching.info.shutdownCompleted=Đã giải phóng tài nguyên của OrderMatchingEngineService
orderMatching.info.positionLiquidated=Đã thanh lý vị thế {0}, symbol = {1}
orderMatching.info.orderCanceled=Đã hủy lệnh {0}, symbol = {1}
orderMatching.info.distributedEngineInitialized=Đã khởi tạo distributed locking matching engine
orderMatching.info.optimizedEngineInitialized=Đã khởi tạo optimized matching engine

orderMatching.debug.markPriceUpdated=Đã cập nhật giá đánh dấu cho symbol {0}, markPrice = {1}
orderMatching.debug.indexPriceUpdated=Đã cập nhật giá chỉ số cho symbol {0}, indexPrice = {1}
orderMatching.debug.triggerOrdersChecked=Đã kiểm tra lệnh chờ cho symbol {0}
orderMatching.debug.liquidationsChecked=Đã kiểm tra thanh lý cho symbol {0}

# Contract Service messages
contractService.error.createContract=Lỗi khi tạo hợp đồng, symbol = {0}
contractService.error.updateContract=Lỗi khi cập nhật hợp đồng, symbol = {0}
contractService.error.deleteContract=Lỗi khi xóa hợp đồng, symbol = {0}

contractService.warn.contractNotFound=Không tìm thấy hợp đồng với symbol {0}

contractService.info.contractCreated=Đã tạo hợp đồng, symbol = {0}
contractService.info.contractUpdated=Đã cập nhật hợp đồng, symbol = {0}
contractService.info.contractDeleted=Đã xóa hợp đồng, symbol = {0}

# Position Service messages
positionService.error.createPosition=Lỗi khi tạo vị thế, memberId = {0}, symbol = {1}
positionService.error.updatePosition=Lỗi khi cập nhật vị thế, positionId = {0}
positionService.error.closePosition=Lỗi khi đóng vị thế, positionId = {0}

positionService.warn.positionNotFound=Không tìm thấy vị thế với id {0}

positionService.info.positionCreated=Đã tạo vị thế, memberId = {0}, symbol = {1}
positionService.info.positionUpdated=Đã cập nhật vị thế, positionId = {0}
positionService.info.positionClosed=Đã đóng vị thế, positionId = {0}

# Messaging Service messages
messagingService.error.processFundingRate=Lỗi khi xử lý funding rate, symbol = {0}, value = {1}
messagingService.error.processNewOrder=Lỗi khi xử lý lệnh mới
messagingService.error.processCancelOrder=Lỗi khi xử lý lệnh hủy
messagingService.error.processFundingRateUpdate=Lỗi khi xử lý cập nhật tỷ lệ tài trợ
messagingService.error.processTrade=Lỗi khi xử lý giao dịch, symbol = {0}, value = {1}

messagingService.info.receivedFundingRate=Đã nhận funding rate, symbol = {0}, value = {1}
messagingService.info.receivedNewOrder=Đã nhận lệnh mới, topic = {0}, key = {1}
messagingService.info.receivedCancelOrder=Đã nhận lệnh hủy, topic = {0}, key = {1}
messagingService.info.receivedFundingRateUpdate=Đã nhận cập nhật tỷ lệ tài trợ, topic = {0}, key = {1}
messagingService.info.receivedTrade=Đã nhận giao dịch, symbol = {0}, value = {1}
messagingService.info.receivedLiquidation=Đã nhận thanh lý, symbol = {0}, value = {1}
messagingService.info.markPriceFromHeader=Lấy giá đánh dấu từ header thành công, markPrice = {0}
messagingService.warn.markPriceFromService=Không thể lấy giá đánh dấu từ header, sử dụng giá đánh dấu từ service, error = {0}
messagingService.warn.useLiquidationPrice=Không tìm thấy giá đánh dấu, sử dụng giá thanh lý, symbol = {0}
messagingService.info.processLiquidation=Xử lý thanh lý với giá đánh dấu = {0}, symbol = {1}, positionId = {2}
messagingService.error.processLiquidation=Xử lý thanh lý thất bại, symbol = {0}, value = {1}

# Special Order Service messages
specialOrderService.error.processTimeOrders=Lỗi khi xử lý lệnh theo thời gian
specialOrderService.error.processExpiredOrders=Lỗi khi xử lý lệnh hết hạn
specialOrderService.error.processTriggerOrders=Lỗi khi xử lý lệnh chờ
specialOrderService.error.checkTriggerOrders=Lỗi khi kiểm tra lệnh chờ, symbol = {0}
specialOrderService.error.processTimeOrder=Lỗi khi xử lý lệnh theo thời gian

specialOrderService.info.processTimeOrders=Đang xử lý lệnh theo thời gian
specialOrderService.info.processExpiredOrders=Đang xử lý lệnh hết hạn
specialOrderService.info.processTriggerOrders=Đang kiểm tra lệnh chờ

specialOrderService.debug.processTimeOrder=Đang xử lý lệnh theo thời gian, orderId = {0}

# Report Service messages
reportService.error.createReportsAsync=Lỗi khi tạo báo cáo bất đồng bộ cho tất cả lệnh đã hoàn thành
reportService.error.processStatisticsAsync=Lỗi khi xử lý dữ liệu thống kê bất đồng bộ

reportService.info.createReportsAsync=Tạo báo cáo giao dịch bất đồng bộ cho tất cả lệnh đã hoàn thành
reportService.info.completedOrdersCount=Số lượng lệnh đã hoàn thành chưa có báo cáo: {0}
reportService.info.createReportsAsyncCompleted=Đã tạo báo cáo bất đồng bộ cho tất cả lệnh đã hoàn thành
reportService.info.processStatisticsAsync=Đang xử lý dữ liệu thống kê bất đồng bộ, startDate = {0}, endDate = {1}
reportService.info.processStatisticsAsyncCompleted=Đã xử lý dữ liệu thống kê bất đồng bộ, startDate = {0}, endDate = {1}

# Sharding Manager messages
shardingManager.error.rebalancingInterrupted=Bị gián đoạn khi cân bằng lại các symbol cho pod: {0}

shardingManager.warn.rebalancingLockFailed=Không thể lấy khóa để cân bằng lại các symbol cho pod: {0}
shardingManager.warn.no.active.pods=Không tìm thấy pod nào đang hoạt động để cân bằng lại

shardingManager.info.initialized=Đã khởi tạo sharding manager cho pod: {0}
shardingManager.info.symbolAssigned=Symbol {0} đã được gán cho pod {1}, chủ sở hữu trước đó: {2}
shardingManager.info.symbolUnassigned=Symbol {0} đã được hủy gán khỏi pod {1}
shardingManager.info.symbolRemoved=Symbol {0} đã bị xóa khỏi mapping pod
shardingManager.info.rebalancingStarted=Bắt đầu cân bằng lại các symbol cho pod: {0}
shardingManager.info.rebalancingCompleted=Đã hoàn thành cân bằng lại các symbol cho pod: {0}
shardingManager.info.symbolMoved=Symbol {0} đã được di chuyển từ pod {1} sang pod {2}
shardingManager.info.rebalanceStarted=Bắt đầu cân bằng lại các symbol cho pod: {0}
shardingManager.info.rebalanceCompleted=Đã hoàn thành cân bằng lại các symbol cho pod: {0}

shardingManager.debug.heartbeatSent=Đã gửi heartbeat cho pod: {0}

# Reshard Executor messages
reshardExecutor.error.lockTimeout=Không thể lấy khóa cho symbol: {0}
reshardExecutor.error.lockInterrupted=Bị gián đoạn khi chờ khóa cho symbol: {0}
reshardExecutor.error.reshardFailed=Re-sharding thất bại cho symbol: {0}
reshardExecutor.error.statusUpdateFailed=Cập nhật trạng thái re-sharding thất bại cho symbol {0} thành {1}
reshardExecutor.error.prepareNotificationFailed=Gửi thông báo chuẩn bị re-sharding thất bại cho symbol: {0}
reshardExecutor.error.pauseOrderProcessingFailed=Tạm dừng xử lý lệnh thất bại cho symbol: {0}
reshardExecutor.error.engineNotFound=Không tìm thấy matching engine cho symbol: {0}
reshardExecutor.error.snapshotCreationFailed=Tạo snapshot thất bại cho symbol: {0}
reshardExecutor.error.snapshotTransferFailed=Chuyển snapshot thất bại cho symbol {0} đến shard {1}
reshardExecutor.error.mappingUpdateFailed=Cập nhật mapping thất bại cho symbol {0} sang shard {1}
reshardExecutor.error.completeNotificationFailed=Gửi thông báo hoàn thành re-sharding thất bại cho symbol: {0}
reshardExecutor.error.rollbackFailed=Rollback re-sharding thất bại cho symbol: {0}

reshardExecutor.info.executingReshard=Đang thực hiện re-sharding cho symbol {0} từ shard {1} sang shard {2}
reshardExecutor.info.statusUpdated=Đã cập nhật trạng thái re-sharding cho symbol {0} thành {1}
reshardExecutor.info.prepareNotificationSent=Đã gửi thông báo chuẩn bị re-sharding cho symbol: {0}
reshardExecutor.info.orderProcessingPaused=Đã tạm dừng xử lý lệnh cho symbol: {0}
reshardExecutor.info.snapshotCreated=Đã tạo snapshot cho symbol: {0}
reshardExecutor.info.snapshotTransferred=Đã chuyển snapshot cho symbol {0} đến shard {1}
reshardExecutor.info.mappingUpdated=Đã cập nhật mapping cho symbol {0} sang shard {1}
reshardExecutor.info.completeNotificationSent=Đã gửi thông báo hoàn thành re-sharding cho symbol: {0}
reshardExecutor.info.rollingBack=Đang rollback re-sharding cho symbol: {0}
reshardExecutor.info.rollbackCompleted=Đã hoàn thành rollback re-sharding cho symbol: {0}
reshardExecutor.info.reshardCompleted=Đã hoàn thành re-sharding cho symbol {0} từ shard {1} sang shard {2}

# Reshard Planner messages
reshardPlanner.info.planningReshard=Đang lên kế hoạch re-sharding cho symbol {0} với lý do {1}
reshardPlanner.info.reshardPlanExists=Symbol {0} đã có kế hoạch re-sharding
reshardPlanner.error.currentShardNotFound=Không tìm thấy shard hiện tại cho symbol {0}
reshardPlanner.error.targetShardNotFound=Không tìm thấy shard đích cho symbol {0}
reshardPlanner.info.sameShard=Symbol {0} đã ở shard {1}, không cần re-sharding
reshardPlanner.info.reshardPlanCreated=Đã tạo kế hoạch re-sharding cho symbol {0} từ shard {1} sang shard {2}
reshardPlanner.error.reshardPlanNotFound=Không tìm thấy kế hoạch re-sharding cho symbol {0}
reshardPlanner.info.approvingReshardPlan=Đang phê duyệt kế hoạch re-sharding cho symbol {0} từ shard {1} sang shard {2}
reshardPlanner.info.rejectingReshardPlan=Đang từ chối kế hoạch re-sharding cho symbol {0}

# Hot Spot Detector messages
hotSpotDetector.debug.detectingHotSpots=Đang phát hiện các hot spot
hotSpotDetector.info.hotSpotsDetected=Đã phát hiện {0} hot spot
hotSpotDetector.info.hotSpotDetails=Hot spot: symbol={0}, severity={1}
hotSpotDetector.debug.hotSpotRecentlyDetected=Hot spot {0} đã được phát hiện gần đây, bỏ qua
hotSpotDetector.info.lowSeverityHotSpot=Hot spot {0} có mức độ nghiêm trọng thấp, chỉ ghi nhận

# Load Imbalance Detector messages
loadImbalanceDetector.debug.detectingImbalance=Đang phát hiện mất cân bằng tải
loadImbalanceDetector.info.imbalanceDetected=Đã phát hiện mất cân bằng {0} cho shard {1}: {2}% (ngưỡng: {3}%)
loadImbalanceDetector.info.processingImbalances=Đang xử lý {0} mất cân bằng
loadImbalanceDetector.info.shardImbalanceDetails=Mất cân bằng cho shard {0}: {1}
loadImbalanceDetector.debug.imbalanceRecentlyDetected=Mất cân bằng cho shard {0} đã được phát hiện gần đây, bỏ qua

# Symbol Selector messages
symbolSelector.info.selectingSymbols=Đang lựa chọn các symbol để cân bằng lại tải cho shard {0}
symbolSelector.info.notEnoughInstances=Không đủ instance trong shard {0} để cân bằng lại tải
symbolSelector.error.cannotDetermineInstances=Không thể xác định instance có tải cao nhất và thấp nhất cho shard {0}
symbolSelector.info.loadInstancesIdentified=Đã xác định instance có tải cao nhất {0} và thấp nhất {1}
symbolSelector.info.noSymbolsFound=Không tìm thấy symbol nào cho instance {0}
symbolSelector.info.noSuitableSymbolsFound=Không tìm thấy symbol nào phù hợp để di chuyển
symbolSelector.info.symbolsSelected=Đã chọn {0} symbol để di chuyển từ instance {1} sang instance {2}

# Migration Planner messages
migrationPlanner.info.planningMigration=Đang lên kế hoạch di chuyển {0} symbol từ instance {1} sang instance {2}
migrationPlanner.info.tooManyMigrations=Đã có {0} di chuyển đang diễn ra, vượt quá giới hạn {1}
migrationPlanner.info.migrationPlanExists=Symbol {0} đã có kế hoạch di chuyển
migrationPlanner.info.migrationPlanCreated=Đã tạo kế hoạch di chuyển cho symbol {0} từ instance {1} sang instance {2}
migrationPlanner.error.migrationPlanNotFound=Không tìm thấy kế hoạch di chuyển cho symbol {0}
migrationPlanner.info.approvingMigrationPlan=Đang phê duyệt kế hoạch di chuyển cho symbol {0} từ instance {1} sang instance {2}
migrationPlanner.info.rejectingMigrationPlan=Đang từ chối kế hoạch di chuyển cho symbol {0}

# Migration Executor messages
migrationExecutor.info.executingMigration=Đang thực hiện di chuyển cho symbol {0} từ instance {1} sang instance {2}
migrationExecutor.error.lockTimeout=Timeout khi lấy lock cho symbol {0}
migrationExecutor.error.lockInterrupted=Bị gián đoạn khi lấy lock cho symbol {0}
migrationExecutor.error.migrationFailed=Di chuyển thất bại cho symbol {0}
migrationExecutor.info.statusUpdated=Đã cập nhật trạng thái di chuyển cho symbol {0} thành {1}
migrationExecutor.error.statusUpdateFailed=Cập nhật trạng thái di chuyển thất bại cho symbol {0} thành {1}
migrationExecutor.info.prepareNotificationSent=Đã gửi thông báo chuẩn bị di chuyển cho symbol {0}
migrationExecutor.error.prepareNotificationFailed=Gửi thông báo chuẩn bị di chuyển thất bại cho symbol {0}
migrationExecutor.info.orderProcessingPaused=Đã tạm dừng xử lý lệnh cho symbol {0}
migrationExecutor.error.pauseOrderProcessingFailed=Tạm dừng xử lý lệnh thất bại cho symbol {0}
migrationExecutor.info.snapshotCreated=Đã tạo snapshot cho symbol {0}
migrationExecutor.error.engineNotFound=Không tìm thấy matching engine cho symbol {0}
migrationExecutor.error.snapshotCreationFailed=Tạo snapshot thất bại cho symbol {0}
migrationExecutor.info.snapshotTransferred=Đã chuyển snapshot cho symbol {0} đến instance {1}
migrationExecutor.error.snapshotTransferFailed=Chuyển snapshot thất bại cho symbol {0} đến instance {1}
migrationExecutor.info.mappingUpdated=Đã cập nhật mapping cho symbol {0} sang instance {1}
migrationExecutor.error.mappingUpdateFailed=Cập nhật mapping thất bại cho symbol {0} sang instance {1}
migrationExecutor.info.completeNotificationSent=Đã gửi thông báo hoàn thành di chuyển cho symbol {0}
migrationExecutor.error.completeNotificationFailed=Gửi thông báo hoàn thành di chuyển thất bại cho symbol {0}
migrationExecutor.info.rollingBack=Đang rollback di chuyển cho symbol {0}
migrationExecutor.info.rollbackCompleted=Đã hoàn thành rollback di chuyển cho symbol {0}
migrationExecutor.error.rollbackFailed=Rollback di chuyển thất bại cho symbol {0}
migrationExecutor.info.migrationCompleted=Đã hoàn thành di chuyển cho symbol {0} từ instance {1} sang instance {2}

# Settlement messages
settlement.error.scheduledFundingSettlement=Lỗi trong thanh toán tài trợ theo lịch cho symbol: {0}
settlement.error.scheduledContractSettlement=Lỗi trong thanh toán hợp đồng theo lịch cho symbol: {0}
settlement.error.initializeFundingSettlementSchedules=Lỗi khi khởi tạo lịch thanh toán tài trợ
settlement.error.initializeContractSettlementSchedules=Lỗi khi khởi tạo lịch thanh toán hợp đồng
settlement.error.calculateFundingRate=Lỗi khi tính toán tỷ lệ tài trợ cho symbol: {0}
settlement.error.settleContract=Lỗi khi thanh toán hợp đồng cho symbol: {0}
settlement.error.autoFundingSettlement=Lỗi trong thanh toán tài trợ tự động cho symbol: {0}
settlement.error.autoContractSettlement=Lỗi trong thanh toán hợp đồng tự động cho symbol: {0}
settlement.error.autoFundingSettlementAll=Lỗi trong thanh toán tài trợ tự động cho tất cả symbol
settlement.error.autoContractSettlementAll=Lỗi trong thanh toán hợp đồng tự động cho tất cả symbol

settlement.info.scheduledFundingSettlementStarted=Đã bắt đầu thanh toán tài trợ theo lịch cho symbol: {0}
settlement.info.scheduledFundingSettlementCompleted=Đã hoàn thành thanh toán tài trợ theo lịch cho symbol: {0}
settlement.info.scheduledContractSettlementStarted=Đã bắt đầu thanh toán hợp đồng theo lịch cho symbol: {0}
settlement.info.scheduledContractSettlementCompleted=Đã hoàn thành thanh toán hợp đồng theo lịch cho symbol: {0}
settlement.info.initializeFundingSettlementSchedules=Đang khởi tạo lịch thanh toán tài trợ
settlement.info.initializeFundingSettlementSchedulesCompleted=Đã khởi tạo lịch thanh toán tài trợ
settlement.info.initializeContractSettlementSchedules=Đang khởi tạo lịch thanh toán hợp đồng
settlement.info.initializeContractSettlementSchedulesCompleted=Đã khởi tạo lịch thanh toán hợp đồng
settlement.info.autoFundingSettlementStarted=Đã bắt đầu thanh toán tài trợ tự động cho symbol: {0}
settlement.info.autoFundingSettlementCompleted=Đã hoàn thành thanh toán tài trợ tự động cho symbol: {0}
settlement.info.autoContractSettlementStarted=Đã bắt đầu thanh toán hợp đồng tự động cho symbol: {0}
settlement.info.autoContractSettlementCompleted=Đã hoàn thành thanh toán hợp đồng tự động cho symbol: {0}
settlement.info.autoFundingSettlementAllCompleted=Đã hoàn thành thanh toán tài trợ tự động cho tất cả symbol
settlement.info.autoContractSettlementAllCompleted=Đã hoàn thành thanh toán hợp đồng tự động cho tất cả symbol
settlement.info.scheduleFundingSettlement=Đang lên lịch thanh toán tài trợ cho symbol: {0}, thời gian tiếp theo: {1}
settlement.info.scheduleContractSettlement=Đang lên lịch thanh toán hợp đồng cho symbol: {0}, thời gian tiếp theo: {1}
settlement.info.scheduleFundingSettlementCompleted=Đã hoàn thành lên lịch thanh toán tài trợ cho symbol: {0}, thời gian tiếp theo: {1}
settlement.info.scheduleContractSettlementCompleted=Đã hoàn thành lên lịch thanh toán hợp đồng cho symbol: {0}, thời gian tiếp theo: {1}
settlement.info.scheduleFundingSettlementAll=Đang lên lịch thanh toán tài trợ cho tất cả symbol với lịch: {0}
settlement.info.scheduleFundingSettlementAllCompleted=Đã hoàn thành lên lịch thanh toán tài trợ cho tất cả symbol với lịch: {0}, số lượng thành công: {1}
settlement.info.scheduleContractSettlementAll=Đang lên lịch thanh toán hợp đồng cho tất cả symbol với lịch: {0}
settlement.info.scheduleContractSettlementAllCompleted=Đã hoàn thành lên lịch thanh toán hợp đồng cho tất cả symbol với lịch: {0}, số lượng thành công: {1}
settlement.info.cancelFundingSettlementSchedule=Đang hủy lịch thanh toán tài trợ cho symbol: {0}
settlement.info.cancelFundingSettlementScheduleCompleted=Đã hủy lịch thanh toán tài trợ cho symbol: {0}
settlement.info.cancelContractSettlementSchedule=Đang hủy lịch thanh toán hợp đồng cho symbol: {0}
settlement.info.cancelContractSettlementScheduleCompleted=Đã hủy lịch thanh toán hợp đồng cho symbol: {0}
settlement.info.cancelFundingSettlementScheduleAll=Đang hủy lịch thanh toán tài trợ cho tất cả symbol
settlement.info.cancelFundingSettlementScheduleAllCompleted=Đã hủy lịch thanh toán tài trợ cho tất cả symbol, số lượng thành công: {0}
settlement.info.cancelContractSettlementScheduleAll=Đang hủy lịch thanh toán hợp đồng cho tất cả symbol
settlement.info.cancelContractSettlementScheduleAllCompleted=Đã hủy lịch thanh toán hợp đồng cho tất cả symbol, số lượng thành công: {0}
settlement.info.fundingSettlementStarted=Đã bắt đầu thanh toán tài trợ cho symbol: {0}, thời điểm: {1}
settlement.info.fundingSettlementCompleted=Đã hoàn thành thanh toán tài trợ cho symbol: {0}, thời điểm: {1}
settlement.info.contractSettlementStarted=Đã bắt đầu thanh toán hợp đồng cho symbol: {0}, thời điểm: {1}
settlement.info.contractSettlementCompleted=Đã hoàn thành thanh toán hợp đồng cho symbol: {0}, thời điểm: {1}
settlement.info.autoFundingSettlementAllStarted=Đã bắt đầu thanh toán tài trợ tự động cho tất cả symbol, thời điểm: {0}
settlement.info.autoContractSettlementAllStarted=Đã bắt đầu thanh toán hợp đồng tự động cho tất cả symbol, thời điểm: {0}

settlement.error.settleFunding=Lỗi khi thanh toán tài trợ cho symbol: {0}, thời điểm: {1}
settlement.error.fundingSettlement=Lỗi trong thanh toán tài trợ cho symbol: {0}, thời điểm: {1}
settlement.error.fundingSettlementRetry=Đã thử lại thanh toán tài trợ 3 lần nhưng thất bại cho symbol: {0}, thời điểm: {1}
settlement.error.contractSettlementRetry=Đã thử lại thanh toán hợp đồng 3 lần nhưng thất bại cho symbol: {0}, thời điểm: {1}
settlement.error.scheduleFundingSettlement=Lỗi khi lên lịch thanh toán tài trợ cho symbol: {0}, lịch: {1}
settlement.error.scheduleContractSettlement=Lỗi khi lên lịch thanh toán hợp đồng cho symbol: {0}, lịch: {1}
settlement.error.scheduleFundingSettlementAll=Lỗi khi lên lịch thanh toán tài trợ cho tất cả symbol với lịch: {0}
settlement.error.scheduleContractSettlementAll=Lỗi khi lên lịch thanh toán hợp đồng cho tất cả symbol với lịch: {0}
settlement.error.cancelFundingSettlementSchedule=Lỗi khi hủy lịch thanh toán tài trợ cho symbol: {0}
settlement.error.cancelContractSettlementSchedule=Lỗi khi hủy lịch thanh toán hợp đồng cho symbol: {0}
settlement.error.cancelFundingSettlementScheduleAll=Lỗi khi hủy lịch thanh toán tài trợ cho tất cả symbol
settlement.error.cancelContractSettlementScheduleAll=Lỗi khi hủy lịch thanh toán hợp đồng cho tất cả symbol
settlement.error.getNextFundingSettlementTime=Lỗi khi lấy thời điểm thanh toán tài trợ tiếp theo cho symbol: {0}

settlement.warn.fundingRateZero=Tỷ lệ tài trợ bằng 0 cho symbol: {0}
settlement.warn.settlementPriceZero=Giá thanh toán bằng 0 cho symbol: {0}
settlement.warn.noPositions=Không tìm thấy vị thế nào cho symbol: {0}

# Funding Service messages
fundingService.debug.calculateFundingRate=Đang tính toán tỷ lệ tài trợ cho symbol: {0}, giá chỉ số: {1}, giá đánh dấu: {2}
fundingService.debug.getCurrentFundingRate=Đang lấy tỷ lệ tài trợ hiện tại cho symbol: {0}
fundingService.debug.getNextFundingTime=Đang lấy thời gian tài trợ tiếp theo cho symbol: {0}

fundingService.error.calculateFundingRate=Lỗi khi tính toán tỷ lệ tài trợ cho symbol: {0}
fundingService.error.getCurrentFundingRate=Lỗi khi lấy tỷ lệ tài trợ hiện tại cho symbol: {0}
fundingService.error.getNextFundingTime=Lỗi khi lấy thời gian tài trợ tiếp theo cho symbol: {0}

# Price Service messages
priceService.debug.getIndexPrice=Đang lấy giá chỉ số cho symbol: {0}
priceService.debug.getMarkPrice=Đang lấy giá đánh dấu cho symbol: {0}
priceService.debug.calculateIndexPrice=Đang tính toán giá chỉ số cho symbol: {0}
priceService.debug.calculateMarkPrice=Đang tính toán giá đánh dấu cho symbol: {0}

priceService.error.getIndexPrice=Lỗi khi lấy giá chỉ số cho symbol: {0}
priceService.error.getMarkPrice=Lỗi khi lấy giá đánh dấu cho symbol: {0}
priceService.error.calculateIndexPrice=Lỗi khi tính toán giá chỉ số cho symbol: {0}
priceService.error.calculateMarkPrice=Lỗi khi tính toán giá đánh dấu cho symbol: {0}
