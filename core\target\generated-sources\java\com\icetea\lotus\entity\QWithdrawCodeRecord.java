package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QWithdrawCodeRecord is a Querydsl query type for WithdrawCodeRecord
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QWithdrawCodeRecord extends EntityPathBase<WithdrawCodeRecord> {

    private static final long serialVersionUID = 261395608L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QWithdrawCodeRecord withdrawCodeRecord = new QWithdrawCodeRecord("withdrawCodeRecord");

    public final QCoin coin;

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final DateTimePath<java.util.Date> dealTime = createDateTime("dealTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final StringPath remark = createString("remark");

    public final NumberPath<Long> rmemberId = createNumber("rmemberId", Long.class);

    public final EnumPath<com.icetea.lotus.constant.WithdrawStatus> status = createEnum("status", com.icetea.lotus.constant.WithdrawStatus.class);

    public final NumberPath<java.math.BigDecimal> withdrawAmount = createNumber("withdrawAmount", java.math.BigDecimal.class);

    public final StringPath withdrawCode = createString("withdrawCode");

    public QWithdrawCodeRecord(String variable) {
        this(WithdrawCodeRecord.class, forVariable(variable), INITS);
    }

    public QWithdrawCodeRecord(Path<? extends WithdrawCodeRecord> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QWithdrawCodeRecord(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QWithdrawCodeRecord(PathMetadata metadata, PathInits inits) {
        this(WithdrawCodeRecord.class, metadata, inits);
    }

    public QWithdrawCodeRecord(Class<? extends WithdrawCodeRecord> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.coin = inits.isInitialized("coin") ? new QCoin(forProperty("coin")) : null;
    }

}

