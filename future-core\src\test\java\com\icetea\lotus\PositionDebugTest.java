package com.icetea.lotus;

import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.PositionStatus;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.application.port.output.PositionPersistencePort;
import com.icetea.lotus.core.domain.service.PriceManagementService;
import com.icetea.lotus.application.service.ManagePositionService;
import com.icetea.lotus.application.dto.PositionDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Test để debug vấn đề tính toán margin và PNL
 */
@SpringBootTest
public class PositionDebugTest {

    @Autowired
    private PositionPersistencePort positionPersistencePort;

    @Autowired
    private PriceManagementService priceManagementService;

    @Autowired
    private ManagePositionService managePositionService;

    @Test
    public void debugPositionCalculation() {
        System.out.println("=== POSITION DEBUG TEST ===");

        try {
            // Lấy tất cả positions từ database
            List<Position> allPositions = positionPersistencePort.findAll();
            System.out.println("Total positions found: " + allPositions.size());

            for (Position position : allPositions) {
                System.out.println("\n--- Position ID: " + position.getId().getValue() + " ---");
                System.out.println("Member ID: " + position.getMemberId());
                System.out.println("Symbol: " + position.getSymbol().getValue());
                System.out.println("Direction: " + position.getDirection());
                System.out.println("Volume: " + position.getVolume());
                System.out.println("Open Price: " + position.getOpenPrice().getValue());
                System.out.println("Margin (from DB): " + position.getMargin().getValue());
                System.out.println("Leverage: " + position.getLeverage());
                System.out.println("Status: " + position.getStatus());

                // Tính toán margin mong đợi
                BigDecimal expectedMargin = position.getOpenPrice().getValue()
                        .multiply(position.getVolume())
                        .divide(position.getLeverage(), 8, RoundingMode.HALF_UP);

                System.out.println("Expected Margin (calculated): " + expectedMargin);

                // Kiểm tra sự khác biệt
                BigDecimal difference = position.getMargin().getValue().subtract(expectedMargin).abs();
                System.out.println("Margin Difference: " + difference);

                // Lấy mark price hiện tại
                try {
                    Money currentMarkPrice = priceManagementService.getCurrentMarkPrice(position.getSymbol());
                    System.out.println("Current Mark Price: " + (currentMarkPrice != null ? currentMarkPrice.getValue() : "null"));

                    if (currentMarkPrice != null) {
                        // Tính toán unrealized profit
                        Money unrealizedProfit = position.calculateUnrealizedProfit(currentMarkPrice);
                        System.out.println("Unrealized Profit: " + unrealizedProfit.getValue());

                        // Tính toán profit ratio
                        BigDecimal profitRatio = unrealizedProfit.getValue()
                                .divide(position.getMargin().getValue(), 8, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        System.out.println("Profit Ratio (%): " + profitRatio);
                    }
                } catch (Exception e) {
                    System.out.println("Error getting mark price: " + e.getMessage());
                }

                // Kiểm tra xem margin có bất thường không
                if (position.getMargin().getValue().compareTo(new BigDecimal("1000000")) > 0) {
                    System.out.println("*** WARNING: EXTREMELY HIGH MARGIN DETECTED! ***");
                }
            }

        } catch (Exception e) {
            System.out.println("Error in debug test: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void debugManagePositionServiceOutput() {
        System.out.println("\n=== MANAGE POSITION SERVICE DEBUG ===");

        try {
            // Lấy tất cả positions từ service
            List<Position> allPositions = positionPersistencePort.findAll();

            for (Position position : allPositions) {
                System.out.println("\n--- Testing Position ID: " + position.getId().getValue() + " ---");

                // Gọi service để lấy position DTO
                List<PositionDto> positionDtos = managePositionService.getAllPositions(position.getMemberId());

                for (PositionDto dto : positionDtos) {
                    if (dto.getId().equals(position.getId().getValue())) {
                        System.out.println("DTO Margin: " + dto.getMargin());
                        System.out.println("DTO Mark Price: " + dto.getMarkPrice());
                        System.out.println("DTO Unrealized Profit: " + dto.getUnrealizedProfit());
                        System.out.println("DTO Profit Ratio: " + dto.getProfitRatio());
                        System.out.println("DTO Break Even Price: " + dto.getBreakEvenPrice());

                        // So sánh với dữ liệu gốc
                        System.out.println("Original Margin: " + position.getMargin().getValue());

                        if (dto.getMargin() != null && dto.getMargin().compareTo(position.getMargin().getValue()) != 0) {
                            System.out.println("*** MARGIN MISMATCH DETECTED! ***");
                        }

                        break;
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("Error in service debug test: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testSpecificPositionCalculation() {
        System.out.println("\n=== SPECIFIC POSITION CALCULATION TEST ===");

        // Tạo position test với dữ liệu từ ảnh
        Position testPosition = Position.builder()
                .memberId(123L)
                .symbol(Symbol.of("BTC/USDT"))
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("6.000000"))
                .openPrice(Money.of(new BigDecimal("114968.55")))
                .margin(Money.of(new BigDecimal("6000.00"))) // Margin hợp lý
                .leverage(new BigDecimal("1"))
                .status(PositionStatus.OPEN)
                .build();

        // Test với mark price hợp lý
        Money reasonableMarkPrice = Money.of(new BigDecimal("220000.00"));

        System.out.println("Test Position:");
        System.out.println("Volume: " + testPosition.getVolume());
        System.out.println("Open Price: " + testPosition.getOpenPrice().getValue());
        System.out.println("Margin: " + testPosition.getMargin().getValue());
        System.out.println("Leverage: " + testPosition.getLeverage());
        System.out.println("Mark Price: " + reasonableMarkPrice.getValue());

        // Tính toán unrealized profit
        Money unrealizedProfit = testPosition.calculateUnrealizedProfit(reasonableMarkPrice);
        System.out.println("Unrealized Profit: " + unrealizedProfit.getValue());

        // Tính toán profit ratio
        BigDecimal profitRatio = unrealizedProfit.getValue()
                .divide(testPosition.getMargin().getValue(), 8, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        System.out.println("Profit Ratio (%): " + profitRatio);

        // Tính toán thủ công để kiểm tra
        BigDecimal priceDiff = reasonableMarkPrice.getValue().subtract(testPosition.getOpenPrice().getValue());
        BigDecimal manualProfit = priceDiff.multiply(testPosition.getVolume());
        System.out.println("Manual Calculation - Price Diff: " + priceDiff);
        System.out.println("Manual Calculation - Profit: " + manualProfit);

        // So sánh kết quả
        if (unrealizedProfit.getValue().compareTo(manualProfit) == 0) {
            System.out.println("✓ Unrealized profit calculation is CORRECT");
        } else {
            System.out.println("✗ Unrealized profit calculation is INCORRECT");
        }

        // Test với mark price bất thường như trong ảnh
        Money abnormalMarkPrice = Money.of(new BigDecimal("415297.83"));
        System.out.println("\n--- Test with Abnormal Mark Price ---");
        System.out.println("Abnormal Mark Price: " + abnormalMarkPrice.getValue());

        Money abnormalUnrealizedProfit = testPosition.calculateUnrealizedProfit(abnormalMarkPrice);
        System.out.println("Abnormal Unrealized Profit: " + abnormalUnrealizedProfit.getValue());

        BigDecimal abnormalProfitRatio = abnormalUnrealizedProfit.getValue()
                .divide(testPosition.getMargin().getValue(), 8, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        System.out.println("Abnormal Profit Ratio (%): " + abnormalProfitRatio);

        // Tính toán thủ công với mark price bất thường
        BigDecimal abnormalPriceDiff = abnormalMarkPrice.getValue().subtract(testPosition.getOpenPrice().getValue());
        BigDecimal abnormalManualProfit = abnormalPriceDiff.multiply(testPosition.getVolume());
        System.out.println("Abnormal Manual Calculation - Price Diff: " + abnormalPriceDiff);
        System.out.println("Abnormal Manual Calculation - Profit: " + abnormalManualProfit);

        // Test với margin bất thường như trong ảnh
        Position abnormalPosition = Position.builder()
                .memberId(123L)
                .symbol(Symbol.of("BTC/USDT"))
                .direction(PositionDirection.LONG)
                .volume(new BigDecimal("6.000000"))
                .openPrice(Money.of(new BigDecimal("114968.55")))
                .margin(Money.of(new BigDecimal("689811320.38"))) // Margin bất thường từ ảnh
                .leverage(new BigDecimal("1"))
                .status(PositionStatus.OPEN)
                .build();

        System.out.println("\n--- Test with Abnormal Margin ---");
        System.out.println("Abnormal Margin: " + abnormalPosition.getMargin().getValue());

        Money abnormalMarginUnrealizedProfit = abnormalPosition.calculateUnrealizedProfit(reasonableMarkPrice);
        System.out.println("Unrealized Profit with Abnormal Margin: " + abnormalMarginUnrealizedProfit.getValue());

        BigDecimal abnormalMarginProfitRatio = abnormalMarginUnrealizedProfit.getValue()
                .divide(abnormalPosition.getMargin().getValue(), 8, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        System.out.println("Profit Ratio with Abnormal Margin (%): " + abnormalMarginProfitRatio);
    }
}
