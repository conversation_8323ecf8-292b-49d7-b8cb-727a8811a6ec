log4j.rootLogger=info,A1,logFile
log4j.logger.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

log4j.appender.A1=org.apache.log4j.ConsoleAppender
log4j.appender.A1.Target=System.out
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=[%c]%m%n

##\u914d\u7f6e\u8f93\u51fa\u5230\u6587\u4ef6\u7684\u65e5\u5fd7\u7ec4\u4ef6(appenderName)
log4j.appender.logFile=org.apache.log4j.RollingFileAppender
##\u6307\u5b9a\u8f93\u51fa\u6587\u4ef6\u5730\u5740
log4j.appender.logFile.File=d:/junit.log
##\u6307\u5b9a\u7f16\u7801\u683c\u5f0f
log4j.appender.logFile.encoding=utf-8
##\u6307\u5b9a\u6587\u4ef6\u5927\u5c0f
log4j.appender.logFile.MaxFileSize=5KB
##\u6307\u5b9a\u8ffd\u52a0\u8fd8\u662f\u8986\u76d6(\u9ed8\u8ba4true\u8ffd\u52a0)
log4j.appender.logFile.Append=true
##\u8f93\u51faDEBUG\u7ea7\u522b\u4ee5\u4e0a\u7684\u65e5\u5fd7
##log4j.appender.logFile.Threshold=error
##\u6307\u5b9a\u8f93\u51fa\u683c\u5f0f
log4j.appender.logFile.layout=org.apache.log4j.PatternLayout
##\u81ea\u5b9a\u4e49\u8f93\u51fa\u683c\u5f0f
log4j.appender.logFile.layout.ConversionPattern=[%d{yyyy-MM-dd HH:mm:ss:SSS} %p] %c - %m%n

log4j.logger.org.springframework=off