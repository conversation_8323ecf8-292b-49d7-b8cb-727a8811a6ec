package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QPaymentTypeRecord is a Querydsl query type for PaymentTypeRecord
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QPaymentTypeRecord extends EntityPathBase<PaymentTypeRecord> {

    private static final long serialVersionUID = -635889791L;

    public static final QPaymentTypeRecord paymentTypeRecord = new QPaymentTypeRecord("paymentTypeRecord");

    public final StringPath field_1 = createString("field_1");

    public final StringPath field_2 = createString("field_2");

    public final StringPath field_3 = createString("field_3");

    public final StringPath field_4 = createString("field_4");

    public final StringPath field_5 = createString("field_5");

    public final StringPath field_6 = createString("field_6");

    public final StringPath field_7 = createString("field_7");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<Long> type = createNumber("type", Long.class);

    public QPaymentTypeRecord(String variable) {
        super(PaymentTypeRecord.class, forVariable(variable));
    }

    public QPaymentTypeRecord(Path<? extends PaymentTypeRecord> path) {
        super(path.getType(), path.getMetadata());
    }

    public QPaymentTypeRecord(PathMetadata metadata) {
        super(PaymentTypeRecord.class, metadata);
    }

}

