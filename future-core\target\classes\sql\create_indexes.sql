-- T<PERSON><PERSON> các chỉ mục cho bảng contract_coin
CREATE INDEX IF NOT EXISTS idx_contract_coin_symbol ON contract_coin (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_coin_base_symbol ON contract_coin (base_symbol);
CREATE INDEX IF NOT EXISTS idx_contract_coin_quote_symbol ON contract_coin (quote_symbol);

-- Tạo các chỉ mục cho bảng contract_order
CREATE INDEX IF NOT EXISTS idx_contract_order_member_id ON contract_order (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_order_contract_id ON contract_order (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_order_symbol ON contract_order (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_order_status ON contract_order (status);
CREATE INDEX IF NOT EXISTS idx_contract_order_create_time ON contract_order (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_order_member_symbol ON contract_order (member_id, symbol);
CREATE INDEX IF NOT EXISTS idx_contract_order_member_status ON contract_order (member_id, status);
CREATE INDEX IF NOT EXISTS idx_contract_order_symbol_status ON contract_order (symbol, status);
CREATE INDEX IF NOT EXISTS idx_contract_order_member_symbol_status ON contract_order (member_id, symbol, status);

-- Tạo các chỉ mục cho bảng contract_order_detail
CREATE INDEX IF NOT EXISTS idx_contract_order_detail_order_id ON contract_order_detail (order_id);
CREATE INDEX IF NOT EXISTS idx_contract_order_detail_contract_id ON contract_order_detail (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_order_detail_time ON contract_order_detail (time);

-- Tạo các chỉ mục cho bảng contract_position
CREATE INDEX IF NOT EXISTS idx_contract_position_member_id ON contract_position (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_position_symbol ON contract_position (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_position_direction ON contract_position (direction);
CREATE INDEX IF NOT EXISTS idx_contract_position_status ON contract_position (status);
CREATE INDEX IF NOT EXISTS idx_contract_position_member_symbol ON contract_position (member_id, symbol);
CREATE INDEX IF NOT EXISTS idx_contract_position_member_symbol_direction ON contract_position (member_id, symbol, direction);
CREATE INDEX IF NOT EXISTS idx_contract_position_member_status ON contract_position (member_id, status);
CREATE INDEX IF NOT EXISTS idx_contract_position_symbol_status ON contract_position (symbol, status);
CREATE INDEX IF NOT EXISTS idx_contract_position_create_time ON contract_position (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_position_update_time ON contract_position (update_time);

-- Tạo các chỉ mục cho bảng contract_trade
CREATE INDEX IF NOT EXISTS idx_contract_trade_symbol ON contract_trade (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_trade_buy_order_id ON contract_trade (buy_order_id);
CREATE INDEX IF NOT EXISTS idx_contract_trade_sell_order_id ON contract_trade (sell_order_id);
CREATE INDEX IF NOT EXISTS idx_contract_trade_buy_member_id ON contract_trade (buy_member_id);
CREATE INDEX IF NOT EXISTS idx_contract_trade_sell_member_id ON contract_trade (sell_member_id);
CREATE INDEX IF NOT EXISTS idx_contract_trade_time ON contract_trade (time);
CREATE INDEX IF NOT EXISTS idx_contract_trade_buy_member_symbol ON contract_trade (buy_member_id, symbol);
CREATE INDEX IF NOT EXISTS idx_contract_trade_sell_member_symbol ON contract_trade (sell_member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_wallet
CREATE INDEX IF NOT EXISTS idx_contract_wallet_member_id ON contract_wallet (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_wallet_coin ON contract_wallet (coin);
CREATE INDEX IF NOT EXISTS idx_contract_wallet_create_time ON contract_wallet (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_wallet_update_time ON contract_wallet (update_time);

-- Tạo các chỉ mục cho bảng contract_transaction
CREATE INDEX IF NOT EXISTS idx_contract_transaction_member_id ON contract_transaction (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_coin ON contract_transaction (coin);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_reference_id ON contract_transaction (reference_id);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_type ON contract_transaction (type);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_create_time ON contract_transaction (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_contract_id ON contract_transaction (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_symbol ON contract_transaction (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_order_id ON contract_transaction (order_id);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_trade_id ON contract_transaction (trade_id);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_member_type ON contract_transaction (member_id, type);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_member_coin ON contract_transaction (member_id, coin);
CREATE INDEX IF NOT EXISTS idx_contract_transaction_member_symbol ON contract_transaction (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_funding_rate
CREATE INDEX IF NOT EXISTS idx_contract_funding_rate_contract_id ON contract_funding_rate (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_funding_rate_symbol ON contract_funding_rate (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_funding_rate_time ON contract_funding_rate (time);
CREATE INDEX IF NOT EXISTS idx_contract_funding_rate_next_time ON contract_funding_rate (next_time);

-- Tạo các chỉ mục cho bảng contract_funding_payment
CREATE INDEX IF NOT EXISTS idx_contract_funding_payment_position_id ON contract_funding_payment (position_id);
CREATE INDEX IF NOT EXISTS idx_contract_funding_payment_member_id ON contract_funding_payment (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_funding_payment_symbol ON contract_funding_payment (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_funding_payment_time ON contract_funding_payment (time);
CREATE INDEX IF NOT EXISTS idx_contract_funding_payment_member_symbol ON contract_funding_payment (member_id, symbol);

-- Tạo các chỉ mục cho bảng funding_settlement
CREATE INDEX IF NOT EXISTS idx_funding_settlement_symbol ON funding_settlement (symbol);
CREATE INDEX IF NOT EXISTS idx_funding_settlement_member_id ON funding_settlement (member_id);
CREATE INDEX IF NOT EXISTS idx_funding_settlement_position_id ON funding_settlement (position_id);
CREATE INDEX IF NOT EXISTS idx_funding_settlement_timestamp ON funding_settlement (timestamp);
CREATE INDEX IF NOT EXISTS idx_funding_settlement_member_symbol ON funding_settlement (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_settlement
CREATE INDEX IF NOT EXISTS idx_contract_settlement_contract_id ON contract_settlement (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_settlement_symbol ON contract_settlement (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_settlement_member_id ON contract_settlement (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_settlement_position_id ON contract_settlement (position_id);
CREATE INDEX IF NOT EXISTS idx_contract_settlement_member_symbol ON contract_settlement (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_fee
CREATE INDEX IF NOT EXISTS idx_contract_fee_contract_id ON contract_fee (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_fee_member_id ON contract_fee (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_fee_symbol ON contract_fee (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_fee_order_id ON contract_fee (order_id);
CREATE INDEX IF NOT EXISTS idx_contract_fee_direction ON contract_fee (direction);
CREATE INDEX IF NOT EXISTS idx_contract_fee_create_time ON contract_fee (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_fee_member_symbol ON contract_fee (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_insurance_fund
CREATE INDEX IF NOT EXISTS idx_contract_insurance_fund_contract_id ON contract_insurance_fund (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_insurance_fund_symbol ON contract_insurance_fund (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_insurance_fund_create_time ON contract_insurance_fund (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_insurance_fund_update_time ON contract_insurance_fund (update_time);

-- Tạo các chỉ mục cho bảng contract_liquidation
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_contract_id ON contract_liquidation (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_symbol ON contract_liquidation (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_member_id ON contract_liquidation (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_position_id ON contract_liquidation (position_id);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_liquidation_order_id ON contract_liquidation (liquidation_order_id);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_type ON contract_liquidation (type);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_create_time ON contract_liquidation (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_liquidation_member_symbol ON contract_liquidation (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_adl_record
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_position_id ON contract_adl_record (position_id);
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_member_id ON contract_adl_record (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_symbol ON contract_adl_record (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_direction ON contract_adl_record (direction);
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_time ON contract_adl_record (time);
CREATE INDEX IF NOT EXISTS idx_contract_adl_record_member_symbol ON contract_adl_record (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_clawback_position
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_contract_id ON contract_clawback_position (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_symbol ON contract_clawback_position (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_member_id ON contract_clawback_position (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_position_id ON contract_clawback_position (position_id);
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_create_time ON contract_clawback_position (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_clawback_position_member_symbol ON contract_clawback_position (member_id, symbol);

-- Tạo các chỉ mục cho bảng contract_circuit_breaker
CREATE INDEX IF NOT EXISTS idx_contract_circuit_breaker_symbol ON contract_circuit_breaker (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_circuit_breaker_status ON contract_circuit_breaker (status);
CREATE INDEX IF NOT EXISTS idx_contract_circuit_breaker_create_time ON contract_circuit_breaker (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_circuit_breaker_update_time ON contract_circuit_breaker (update_time);

-- Tạo các chỉ mục cho bảng contract_user_leverage_setting
CREATE INDEX IF NOT EXISTS idx_contract_user_leverage_setting_member_id ON contract_user_leverage_setting (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_user_leverage_setting_symbol ON contract_user_leverage_setting (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_user_leverage_setting_create_time ON contract_user_leverage_setting (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_user_leverage_setting_update_time ON contract_user_leverage_setting (update_time);

-- Tạo các chỉ mục cho bảng contract_user_position_mode
CREATE INDEX IF NOT EXISTS idx_contract_user_position_mode_member_id ON contract_user_position_mode (member_id);
CREATE INDEX IF NOT EXISTS idx_contract_user_position_mode_position_mode ON contract_user_position_mode (position_mode);
CREATE INDEX IF NOT EXISTS idx_contract_user_position_mode_create_time ON contract_user_position_mode (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_user_position_mode_update_time ON contract_user_position_mode (update_time);

-- Tạo các chỉ mục cho bảng contract_price_configuration
CREATE INDEX IF NOT EXISTS idx_contract_price_configuration_symbol ON contract_price_configuration (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_price_configuration_index_price_method ON contract_price_configuration (index_price_method);
CREATE INDEX IF NOT EXISTS idx_contract_price_configuration_mark_price_method ON contract_price_configuration (mark_price_method);
CREATE INDEX IF NOT EXISTS idx_contract_price_configuration_create_time ON contract_price_configuration (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_price_configuration_update_time ON contract_price_configuration (update_time);

-- Tạo các chỉ mục cho bảng contract_last_price
CREATE INDEX IF NOT EXISTS idx_contract_last_price_symbol ON contract_last_price (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_last_price_contract_id ON contract_last_price (contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_last_price_create_time ON contract_last_price (create_time);
CREATE INDEX IF NOT EXISTS idx_contract_last_price_update_time ON contract_last_price (update_time);

-- Tạo các chỉ mục cho bảng contract_index_price
CREATE INDEX IF NOT EXISTS idx_contract_index_price_symbol ON contract_index_price (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_index_price_create_time ON contract_index_price (create_time);
