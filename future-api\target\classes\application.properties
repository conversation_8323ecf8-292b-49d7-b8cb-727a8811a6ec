# Server Configuration
server.port=6010
server.servlet.context-path=/api

# Spring Configuration
spring.application.name=contract-perpetual-futures-api
spring.profiles.active=dev

# Netty Configuration
spring.netty.connection-timeout=3000
spring.netty.max-connections=1000
spring.netty.max-idle-time=10000

# Database Configuration
spring.datasource.url=************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Redis Configuration
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
spring.redis.timeout=5000

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=contract-perpetual-futures-api
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.acks=all
spring.kafka.producer.retries=3

# Kafka Topics
topic-kafka.contract.order-new=contract-order-new
topic-kafka.contract.order-cancel=contract-order-cancel
topic-kafka.contract.order-completed=contract-order-completed
topic-kafka.contract.trade=contract-trade
topic-kafka.contract.trade-plate=contract-trade-plate
topic-kafka.contract.position=contract-position
topic-kafka.contract.mark-price=contract-mark-price
topic-kafka.contract.index-price=contract-index-price
topic-kafka.contract.funding-rate=contract-funding-rate
topic-kafka.contract.liquidation=contract-liquidation

# Logging Configuration
logging.level.root=INFO
logging.level.com.icetea.lotus=DEBUG
logging.file.name=logs/contract-perpetual-futures-api.log
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Springdoc OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
