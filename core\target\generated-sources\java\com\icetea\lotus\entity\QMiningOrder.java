package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMiningOrder is a Querydsl query type for MiningOrder
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMiningOrder extends EntityPathBase<MiningOrder> {

    private static final long serialVersionUID = 1417697678L;

    public static final QMiningOrder miningOrder = new QMiningOrder("miningOrder");

    public final NumberPath<Long> activityId = createNumber("activityId", Long.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<java.math.BigDecimal> currentDaysprofit = createNumber("currentDaysprofit", java.math.BigDecimal.class);

    public final DateTimePath<java.util.Date> endTime = createDateTime("endTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final StringPath image = createString("image");

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<Integer> miningDays = createNumber("miningDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> miningDaysprofit = createNumber("miningDaysprofit", java.math.BigDecimal.class);

    public final NumberPath<Integer> miningedDays = createNumber("miningedDays", Integer.class);

    public final NumberPath<java.math.BigDecimal> miningInvite = createNumber("miningInvite", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> miningInvitelimit = createNumber("miningInvitelimit", java.math.BigDecimal.class);

    public final NumberPath<Integer> miningStatus = createNumber("miningStatus", Integer.class);

    public final StringPath miningUnit = createString("miningUnit");

    public final NumberPath<Integer> period = createNumber("period", Integer.class);

    public final StringPath title = createString("title");

    public final NumberPath<java.math.BigDecimal> totalProfit = createNumber("totalProfit", java.math.BigDecimal.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public QMiningOrder(String variable) {
        super(MiningOrder.class, forVariable(variable));
    }

    public QMiningOrder(Path<? extends MiningOrder> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMiningOrder(PathMetadata metadata) {
        super(MiningOrder.class, metadata);
    }

}

