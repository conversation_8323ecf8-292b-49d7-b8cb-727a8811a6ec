package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QRecharge is a Querydsl query type for Recharge
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QRecharge extends EntityPathBase<Recharge> {

    private static final long serialVersionUID = 1715483639L;

    public static final QRecharge recharge = new QRecharge("recharge");

    public final StringPath address = createString("address");

    public final NumberPath<Long> addtime = createNumber("addtime", Long.class);

    public final NumberPath<Integer> agreen = createNumber("agreen", Integer.class);

    public final NumberPath<Long> block = createNumber("block", Long.class);

    public final NumberPath<Long> coinid = createNumber("coinid", Long.class);

    public final StringPath coinname = createString("coinname");

    public final NumberPath<Integer> confirms = createNumber("confirms", Integer.class);

    public final StringPath hash = createString("hash");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final StringPath md5 = createString("md5");

    public final NumberPath<Integer> memberid = createNumber("memberid", Integer.class);

    public final NumberPath<Double> money = createNumber("money", Double.class);

    public final NumberPath<Integer> nconfirms = createNumber("nconfirms", Integer.class);

    public final NumberPath<Integer> protocol = createNumber("protocol", Integer.class);

    public final StringPath protocolname = createString("protocolname");

    public final StringPath send = createString("send");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public QRecharge(String variable) {
        super(Recharge.class, forVariable(variable));
    }

    public QRecharge(Path<? extends Recharge> path) {
        super(path.getType(), path.getMetadata());
    }

    public QRecharge(PathMetadata metadata) {
        super(Recharge.class, metadata);
    }

}

