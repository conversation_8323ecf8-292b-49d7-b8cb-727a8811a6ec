<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2340c8b7-5814-4a6a-87b1-f51e2f924a30" name="Changes" comment="fix: update Consul host and port configuration; increase contract pricing cache update interval" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="fix_longnt" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;longnt1&quot;,
      &quot;fullname&quot;: &quot;Nguyen Thanh Long&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;***************************:its-glyph_cex/cex_be/future-core.git&quot;,
    &quot;second&quot;: &quot;47ae26f5-309c-424e-a24d-c3b8ce174141&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="ProjectErrors" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2wFbRLF4SeswV2mTpFoPxrAv01p" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.generated-requests | #1.executor": "Run",
    "Maven.future-core [clean].executor": "Run",
    "Maven.future-core [compile].executor": "Run",
    "Maven.future-core [install].executor": "Run",
    "Maven.future-core [org.apache.maven.plugins:maven-clean-plugin:3.3.2:clean].executor": "Run",
    "Maven.future-core [package].executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "Database detector",
    "Notification.DisplayName-DoNotAsk-Lombok plugin": "Lombok integration problem",
    "Notification.DoNotAsk-Database detector": "true",
    "Notification.DoNotAsk-Lombok plugin": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.FuturesCoreApplication.executor": "Debug",
    "git-widget-placeholder": "cleanarchitechture",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Project/cex-be/core",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\Project\cex-be\future-core\document" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.FuturesCoreApplication">
    <configuration name="generated-requests | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="future-core" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="future-core" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <module name="future-core" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <module name="future-core" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="FuturesCoreApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="futures-core" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.FuturesCoreApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <module name="future-core" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.generated-requests | #1" />
        <item itemvalue="HTTP Request.generated-requests | #1" />
        <item itemvalue="HTTP Request.generated-requests | #1" />
        <item itemvalue="HTTP Request.generated-requests | #1" />
        <item itemvalue="HTTP Request.generated-requests | #1" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2340c8b7-5814-4a6a-87b1-f51e2f924a30" name="Changes" comment="" />
      <created>1745640259459</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745640259459</updated>
      <workItem from="1745640260699" duration="530000" />
      <workItem from="1745642590633" duration="19178000" />
      <workItem from="1745806304184" duration="13044000" />
      <workItem from="1745894524987" duration="15102000" />
      <workItem from="1745926466794" duration="19000" />
      <workItem from="1745926530188" duration="5000" />
      <workItem from="1746414758395" duration="1159000" />
      <workItem from="1746427477945" duration="3330000" />
      <workItem from="1746498478820" duration="18853000" />
      <workItem from="1746584942194" duration="25101000" />
      <workItem from="1746674028075" duration="17422000" />
      <workItem from="1746760728784" duration="1437000" />
      <workItem from="1746762194740" duration="21051000" />
      <workItem from="1747015644003" duration="12575000" />
      <workItem from="1747111193702" duration="8743000" />
      <workItem from="1747125034624" duration="1898000" />
      <workItem from="1747127035420" duration="5675000" />
      <workItem from="1747137511461" duration="7000" />
      <workItem from="1747189143494" duration="17176000" />
      <workItem from="1747275840865" duration="11313000" />
      <workItem from="1747302748348" duration="15478000" />
      <workItem from="1747364090640" duration="36072000" />
      <workItem from="1747447794515" duration="60765000" />
      <workItem from="1747879661083" duration="16621000" />
      <workItem from="1747900867462" duration="98000" />
      <workItem from="1747901129093" duration="11427000" />
      <workItem from="1747986556905" duration="1343000" />
      <workItem from="1748230875156" duration="6599000" />
      <workItem from="1748244454433" duration="6639000" />
      <workItem from="1748312778109" duration="3330000" />
      <workItem from="1748320821290" duration="10925000" />
      <workItem from="1748338735840" duration="2931000" />
    </task>
    <task id="LOCAL-00023" summary="refactor: update scheduler and database configurations, enhance error handling, and add new database tables and indexes">
      <option name="closed" value="true" />
      <created>1747219533172</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747219533172</updated>
    </task>
    <task id="LOCAL-00024" summary="feat: add SettlementPriceManagementService integration and WebSocket price updates">
      <option name="closed" value="true" />
      <created>1747226548494</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747226548494</updated>
    </task>
    <task id="LOCAL-00025" summary="feat: update SpotMarketDataListener and WebSocketSpotMarketClient to use K-line data for price updates">
      <option name="closed" value="true" />
      <created>1747282734666</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747282734666</updated>
    </task>
    <task id="LOCAL-00026" summary="feat: enhance WebSocket connection handling and symbol formatting in WebSocketSpotMarketClient">
      <option name="closed" value="true" />
      <created>1747290039634</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747290039634</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: update SpotMarketDataListener and WebSocketSpotMarketClient for improved logging, connection retry mechanism, and symbol format adjustment">
      <option name="closed" value="true" />
      <created>1747290716885</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747290716885</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: update WebSocketSpotMarketClient to use service discovery for dynamic WebSocket URL and adjust application configuration">
      <option name="closed" value="true" />
      <created>1747291235304</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747291235304</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: disable CORS configuration in application.yaml for Nginx handling and update BeanConfiguration to include new listener and websocket packages">
      <option name="closed" value="true" />
      <created>1747292678505</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1747292678505</updated>
    </task>
    <task id="LOCAL-00030" summary="feat: enhance symbol formatting in WebSocket handlers and update conversion logic">
      <option name="closed" value="true" />
      <created>1747294815448</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1747294815448</updated>
    </task>
    <task id="LOCAL-00031" summary="feat: enhance error handling and logging in price management and order processing">
      <option name="closed" value="true" />
      <created>1747314023203</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1747314023203</updated>
    </task>
    <task id="LOCAL-00032" summary="feat: improve symbol validation and formatting in ApiInputValidator and Symbol classes">
      <option name="closed" value="true" />
      <created>1747314982300</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1747314982300</updated>
    </task>
    <task id="LOCAL-00033" summary="feat: enhance caching logic and logging in OrderPersistenceAdapter and TradingController">
      <option name="closed" value="true" />
      <created>1747316477726</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1747316477726</updated>
    </task>
    <task id="LOCAL-00034" summary="feat: enhance order processing by adding contractId and improving symbol parsing logic">
      <option name="closed" value="true" />
      <created>1747318409684</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1747318409684</updated>
    </task>
    <task id="LOCAL-00035" summary="fix order">
      <option name="closed" value="true" />
      <created>1747364460704</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1747364460704</updated>
    </task>
    <task id="LOCAL-00036" summary="fix application">
      <option name="closed" value="true" />
      <created>1747364706137</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1747364706137</updated>
    </task>
    <task id="LOCAL-00037" summary="feat: update cache aspects to use @Qualifier for CacheManager injection">
      <option name="closed" value="true" />
      <created>1747365967598</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1747365967598</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: enhance order cancellation logic and transaction handling with improved margin calculations and coin extraction">
      <option name="closed" value="true" />
      <created>1747379601050</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1747379601050</updated>
    </task>
    <task id="LOCAL-00039" summary="feat: improve order placement error handling and wallet balance checks">
      <option name="closed" value="true" />
      <created>1747380840169</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1747380840169</updated>
    </task>
    <task id="LOCAL-00040" summary="feat: add logging for order command handling in Kafka listener">
      <option name="closed" value="true" />
      <created>1747383165541</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1747383165541</updated>
    </task>
    <task id="LOCAL-00041" summary="feat: update Kafka configuration for improved consumer settings and logging">
      <option name="closed" value="true" />
      <created>1747384016165</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1747384016165</updated>
    </task>
    <task id="LOCAL-00042" summary="feat: add order-commands and order-events topics to Kafka configuration">
      <option name="closed" value="true" />
      <created>1747384788077</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1747384788077</updated>
    </task>
    <task id="LOCAL-00043" summary="feat: update Kafka configuration to use JSON serializer and deserializer for order commands and events">
      <option name="closed" value="true" />
      <created>1747385547506</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1747385547506</updated>
    </task>
    <task id="LOCAL-00044" summary="feat: reduce replication factor for Kafka topics to improve resource efficiency">
      <option name="closed" value="true" />
      <created>1747386505617</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1747386505617</updated>
    </task>
    <task id="LOCAL-00045" summary="feat: enhance logging configuration and improve order handling logic">
      <option name="closed" value="true" />
      <created>1747391569653</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1747391569653</updated>
    </task>
    <task id="LOCAL-00046" summary="feat: enhance error handling and logging in order matching and snapshot processes">
      <option name="closed" value="true" />
      <created>1747422235747</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1747422235747</updated>
    </task>
    <task id="LOCAL-00047" summary="feat: refactor TimeInForce usage and enhance wallet service methods for fee and realized PnL updates">
      <option name="closed" value="true" />
      <created>1747481375353</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1747481375353</updated>
    </task>
    <task id="LOCAL-00048" summary="fix: update KLineManagementService injection to use @Lazy annotation">
      <option name="closed" value="true" />
      <created>1747809194088</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1747809194089</updated>
    </task>
    <task id="LOCAL-00049" summary="feat: convert volume between USDT and BTC in PlaceOrderService and update margin calculations">
      <option name="closed" value="true" />
      <created>1747823141675</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1747823141675</updated>
    </task>
    <task id="LOCAL-00050" summary="feat: add method to find positions by memberId, symbol, and status; update position retrieval logic">
      <option name="closed" value="true" />
      <created>1747824467823</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1747824467826</updated>
    </task>
    <task id="LOCAL-00051" summary="feat: implement findPositions method for paginated position retrieval by memberId, status, and time range">
      <option name="closed" value="true" />
      <created>1747826208854</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1747826208854</updated>
    </task>
    <task id="LOCAL-00052" summary="feat: enhance DistributedLockFreeMatchingEngine with Redis snapshot functionality and transaction tracking">
      <option name="closed" value="true" />
      <created>1747830500491</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1747830500492</updated>
    </task>
    <task id="LOCAL-00053" summary="feat: update Kafka listener to handle single records and enhance Redis storage with expiration">
      <option name="closed" value="true" />
      <created>1747885056440</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1747885056440</updated>
    </task>
    <task id="LOCAL-00054" summary="feat: refactor getAllPositions method to use current user context instead of memberId">
      <option name="closed" value="true" />
      <created>1747886575660</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1747886575660</updated>
    </task>
    <task id="LOCAL-00055" summary="feat: mark oauth2RestTemplate as primary bean in RestTemplate configuration">
      <option name="closed" value="true" />
      <created>1747888992261</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1747888992261</updated>
    </task>
    <task id="LOCAL-00056" summary="refactor: comment out log statements for performance optimization">
      <option name="closed" value="true" />
      <created>1747896371596</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1747896371596</updated>
    </task>
    <task id="LOCAL-00057" summary="feat: enhance order cancellation process with WebSocket orderbook update">
      <option name="closed" value="true" />
      <created>1747897186552</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1747897186552</updated>
    </task>
    <task id="LOCAL-00058" summary="fix: update default leverage to 1 and enhance order cancellation event handling">
      <option name="closed" value="true" />
      <created>1747903481673</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1747903481678</updated>
    </task>
    <task id="LOCAL-00059" summary="feat: enhance OrderBook event handling after order cancellation">
      <option name="closed" value="true" />
      <created>1747904397135</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1747904397135</updated>
    </task>
    <task id="LOCAL-00060" summary="fix: simplify order cancellation logic and remove redundant engine checks">
      <option name="closed" value="true" />
      <created>1747905369252</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1747905369252</updated>
    </task>
    <task id="LOCAL-00061" summary="fix: update comments to clarify trade saving logic in OrderMatchingEngineServiceImpl">
      <option name="closed" value="true" />
      <created>1747906055778</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1747906055778</updated>
    </task>
    <task id="LOCAL-00062" summary="feat: improve K-line creation logic by utilizing previous K-line closing prices">
      <option name="closed" value="true" />
      <created>1747906760042</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1747906760057</updated>
    </task>
    <task id="LOCAL-00063" summary="fix: update application configuration to remove localhost references">
      <option name="closed" value="true" />
      <created>1747912445468</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1747912445468</updated>
    </task>
    <task id="LOCAL-00064" summary="feat: add limits to financial calculations to prevent database overflow">
      <option name="closed" value="true" />
      <created>1747914557764</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1747914557764</updated>
    </task>
    <task id="LOCAL-00065" summary="fix: update LastPrice handling to prevent infinite loop and enhance local cache management">
      <option name="closed" value="true" />
      <created>1748248332812</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1748248332812</updated>
    </task>
    <task id="LOCAL-00066" summary="feat: enhance position calculations and mapping for Binance compatibility">
      <option name="closed" value="true" />
      <created>1748258695371</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1748258695371</updated>
    </task>
    <task id="LOCAL-00067" summary="fix Kline">
      <option name="closed" value="true" />
      <created>1748331781959</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1748331781964</updated>
    </task>
    <task id="LOCAL-00068" summary="feat: enhance position calculations by adding index price and break even price logic">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00069" summary="feat: add BigDecimal validation and scaling for financial calculations">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00070" summary="feat: implement BigDecimal validation for account and transaction mappings to prevent numeric overflow">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00071" summary="fix: update Consul host and port configuration; increase contract pricing cache update interval">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <option name="localTasksCounter" value="72" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:javax.persistence:javax.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="java:jakarta.persistence:jakarta.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.batch:spring-batch-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="b285531e-d311-46e4-9bf0-929d5609d56c" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="cleanarchitechture" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
        <entry key="b285531e-d311-46e4-9bf0-929d5609d56c">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: add ContractId value object to FundingRatePersistenceMapper and ManageFundingService" />
    <MESSAGE value="fix: update KLineManagementService injection to use @Lazy annotation" />
    <MESSAGE value="feat: convert volume between USDT and BTC in PlaceOrderService and update margin calculations" />
    <MESSAGE value="feat: add method to find positions by memberId, symbol, and status; update position retrieval logic" />
    <MESSAGE value="feat: implement findPositions method for paginated position retrieval by memberId, status, and time range" />
    <MESSAGE value="feat: enhance DistributedLockFreeMatchingEngine with Redis snapshot functionality and transaction tracking" />
    <MESSAGE value="feat: update Kafka listener to handle single records and enhance Redis storage with expiration" />
    <MESSAGE value="feat: refactor getAllPositions method to use current user context instead of memberId" />
    <MESSAGE value="feat: mark oauth2RestTemplate as primary bean in RestTemplate configuration" />
    <MESSAGE value="refactor: comment out log statements for performance optimization" />
    <MESSAGE value="feat: enhance order cancellation process with WebSocket orderbook update" />
    <MESSAGE value="fix: update default leverage to 1 and enhance order cancellation event handling" />
    <MESSAGE value="feat: enhance OrderBook event handling after order cancellation" />
    <MESSAGE value="fix: simplify order cancellation logic and remove redundant engine checks" />
    <MESSAGE value="fix: update comments to clarify trade saving logic in OrderMatchingEngineServiceImpl" />
    <MESSAGE value="feat: improve K-line creation logic by utilizing previous K-line closing prices" />
    <MESSAGE value="fix: update application configuration to remove localhost references" />
    <MESSAGE value="feat: add limits to financial calculations to prevent database overflow" />
    <MESSAGE value="fix: update LastPrice handling to prevent infinite loop and enhance local cache management" />
    <MESSAGE value="feat: enhance position calculations and mapping for Binance compatibility" />
    <MESSAGE value="fix Kline" />
    <MESSAGE value="feat: enhance position calculations by adding index price and break even price logic" />
    <MESSAGE value="feat: add BigDecimal validation and scaling for financial calculations" />
    <MESSAGE value="feat: implement BigDecimal validation for account and transaction mappings to prevent numeric overflow" />
    <MESSAGE value="fix: update Consul host and port configuration; increase contract pricing cache update interval" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: update Consul host and port configuration; increase contract pricing cache update interval" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java</url>
          <line>120</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/matching/distributed/DistributedLockFreeMatchingEngine.java</url>
          <line>311</line>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/matching/distributed/DistributedLockFreeMatchingEngine.java</url>
          <line>250</line>
          <option name="timeStamp" value="95" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/matching/distributed/DistributedLockFreeMatchingEngine.java</url>
          <line>781</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>