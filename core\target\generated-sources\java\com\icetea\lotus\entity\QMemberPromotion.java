package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberPromotion is a Querydsl query type for MemberPromotion
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberPromotion extends EntityPathBase<MemberPromotion> {

    private static final long serialVersionUID = -2120183847L;

    public static final QMemberPromotion memberPromotion = new QMemberPromotion("memberPromotion");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> inviteesId = createNumber("inviteesId", Long.class);

    public final NumberPath<Long> inviterId = createNumber("inviterId", Long.class);

    public final EnumPath<com.icetea.lotus.constant.PromotionLevel> level = createEnum("level", com.icetea.lotus.constant.PromotionLevel.class);

    public QMemberPromotion(String variable) {
        super(MemberPromotion.class, forVariable(variable));
    }

    public QMemberPromotion(Path<? extends MemberPromotion> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberPromotion(PathMetadata metadata) {
        super(MemberPromotion.class, metadata);
    }

}

