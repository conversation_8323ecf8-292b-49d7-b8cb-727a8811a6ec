package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QAddressext is a Querydsl query type for Addressext
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QAddressext extends EntityPathBase<Addressext> {

    private static final long serialVersionUID = -1985701123L;

    public static final QAddressext addressext = new QAddressext("addressext");

    public final StringPath address = createString("address");

    public final NumberPath<Integer> coinProtocol = createNumber("coinProtocol", Integer.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> memberId = createNumber("memberId", Integer.class);

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public QAddressext(String variable) {
        super(Addressext.class, forVariable(variable));
    }

    public QAddressext(Path<? extends Addressext> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAddressext(PathMetadata metadata) {
        super(Addressext.class, metadata);
    }

}

