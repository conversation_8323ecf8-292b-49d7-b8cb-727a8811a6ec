-- <PERSON><PERSON><PERSON> để thêm bảng contract_financial_record vào cơ sở dữ liệu

-- Tạo bảng contract_financial_record nếu chưa tồn tại
CREATE TABLE IF NOT EXISTS contract_financial_record (
    id VARCHAR(255) PRIMARY KEY,
    member_id VARCHAR(255) NOT NULL,
    position_id BIGINT,
    type VARCHAR(50) NOT NULL,
    amount DECIMAL(18,8),
    fee DECIMAL(18,8),
    profit DECIMAL(18,8),
    create_time TIMESTAMP NOT NULL,
    remark TEXT
);

-- Tạo chỉ mục cho bảng contract_financial_record
CREATE INDEX IF NOT EXISTS idx_financial_record_member_id ON contract_financial_record (member_id);
CREATE INDEX IF NOT EXISTS idx_financial_record_position_id ON contract_financial_record (position_id);
CREATE INDEX IF NOT EXISTS idx_financial_record_type ON contract_financial_record (type);
CREATE INDEX IF NOT EXISTS idx_financial_record_create_time ON contract_financial_record (create_time);

-- <PERSON><PERSON><PERSON> bảo extension uuid-ossp đã được cài đặt
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Thêm dữ liệu mẫu cho bảng contract_financial_record
INSERT INTO contract_financial_record (id, member_id, position_id, type, amount, fee, profit, create_time, remark)
VALUES
('FR-' || uuid_generate_v4(), '1', 1, 'LIQUIDATION', 100.00000000, 10.00000000, -90.00000000, NOW() - INTERVAL '2 HOUR', 'Liquidation at 50000.00000000'),
('FR-' || uuid_generate_v4(), '2', 2, 'LIQUIDATION', 200.00000000, 20.00000000, -180.00000000, NOW() - INTERVAL '3 HOUR', 'Liquidation at 3000.00000000'),
('FR-' || uuid_generate_v4(), '3', 3, 'TRADING_FEE', 0.00000000, 5.00000000, 0.00000000, NOW() - INTERVAL '4 HOUR', 'Trading fee for order ORD123456789'),
('FR-' || uuid_generate_v4(), '4', 4, 'TRADING_PROFIT', 50.00000000, 0.00000000, 50.00000000, NOW() - INTERVAL '5 HOUR', 'Profit from trade'),
('FR-' || uuid_generate_v4(), '5', 5, 'TRADING_LOSS', -30.00000000, 0.00000000, -30.00000000, NOW() - INTERVAL '6 HOUR', 'Loss from trade'),
('FR-' || uuid_generate_v4(), '1', 6, 'FUNDING_PAYMENT', 2.50000000, 0.00000000, 2.50000000, NOW() - INTERVAL '8 HOUR', 'Funding payment received'),
('FR-' || uuid_generate_v4(), '2', 7, 'FUNDING_PAYMENT', -1.80000000, 0.00000000, -1.80000000, NOW() - INTERVAL '8 HOUR', 'Funding payment paid');

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE contract_financial_record IS 'Bảng lưu trữ các bản ghi tài chính liên quan đến giao dịch hợp đồng tương lai';
COMMENT ON COLUMN contract_financial_record.id IS 'ID của bản ghi tài chính';
COMMENT ON COLUMN contract_financial_record.member_id IS 'ID của thành viên';
COMMENT ON COLUMN contract_financial_record.position_id IS 'ID của vị thế liên quan';
COMMENT ON COLUMN contract_financial_record.type IS 'Loại bản ghi tài chính: DEPOSIT, WITHDRAW, TRADING_FEE, TRADING_PROFIT, TRADING_LOSS, LIQUIDATION, FUNDING_PAYMENT';
COMMENT ON COLUMN contract_financial_record.amount IS 'Số tiền giao dịch';
COMMENT ON COLUMN contract_financial_record.fee IS 'Phí giao dịch';
COMMENT ON COLUMN contract_financial_record.profit IS 'Lợi nhuận';
COMMENT ON COLUMN contract_financial_record.create_time IS 'Thời gian tạo bản ghi';
COMMENT ON COLUMN contract_financial_record.remark IS 'Ghi chú';
