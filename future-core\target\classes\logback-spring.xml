<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>future-core</contextName>

    <!-- Thu<PERSON><PERSON> tính -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [traceId=%X{X-B3-TraceId}, spanId=%X{X-B3-SpanId}] - %msg%n" />
    <property name="LOG_FILE_PATH" value="${LOG_PATH:-./logs}" />
    <property name="LOG_FILE_NAME" value="future-core" />
    <property name="LOG_MAX_HISTORY" value="30" />
    <property name="LOG_MAX_FILE_SIZE" value="100MB" />
    <property name="LOG_TOTAL_SIZE_CAP" value="2GB" />

    <!-- Appender cho console -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Appender cho file -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_PATH}/${LOG_FILE_NAME}.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${LOG_MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${LOG_MAX_HISTORY}</maxHistory>
            <totalSizeCap>${LOG_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Appender cho file error -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_PATH}/${LOG_FILE_NAME}-error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${LOG_FILE_NAME}-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${LOG_MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${LOG_MAX_HISTORY}</maxHistory>
            <totalSizeCap>${LOG_TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Async appender cho hiệu suất tốt hơn -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE" />
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ERROR_FILE" />
        <queueSize>256</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- Cấu hình logger cho môi trường development -->
    <springProfile name="dev">
        <logger name="com.icetea.lotus" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate.SQL" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
        </logger>

        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
        </logger>

        <logger name="org.springframework.kafka" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.apache.kafka" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <!-- Root logger -->
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </root>
    </springProfile>

    <!-- Cấu hình logger cho môi trường test -->
    <springProfile name="test">
        <logger name="com.icetea.lotus" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <!-- Root logger -->
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </root>
    </springProfile>

    <!-- Cấu hình logger cho môi trường production -->
    <springProfile name="prod">
        <logger name="com.icetea.lotus" level="INFO" additivity="false">
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.springframework" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate" level="WARN" additivity="false">
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <!-- Root logger -->
        <root level="INFO">
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </root>
    </springProfile>

    <!-- Cấu hình mặc định nếu không có profile nào được chỉ định -->
    <springProfile name="default">
        <logger name="com.icetea.lotus" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.springframework" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.hibernate.SQL" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
        </logger>

        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
        </logger>

        <logger name="org.springframework.kafka" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <logger name="org.apache.kafka" level="INFO" additivity="false">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </logger>

        <!-- Root logger -->
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="ASYNC_FILE" />
            <appender-ref ref="ASYNC_ERROR_FILE" />
        </root>
    </springProfile>
</configuration>
