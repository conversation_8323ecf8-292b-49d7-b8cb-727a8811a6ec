package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberApplicationConfig is a Querydsl query type for MemberApplicationConfig
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberApplicationConfig extends EntityPathBase<MemberApplicationConfig> {

    private static final long serialVersionUID = 567490024L;

    public static final QMemberApplicationConfig memberApplicationConfig = new QMemberApplicationConfig("memberApplicationConfig");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> promotionOn = createEnum("promotionOn", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> rechargeCoinOn = createEnum("rechargeCoinOn", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> transactionOn = createEnum("transactionOn", com.icetea.lotus.constant.BooleanEnum.class);

    public final EnumPath<com.icetea.lotus.constant.BooleanEnum> withdrawCoinOn = createEnum("withdrawCoinOn", com.icetea.lotus.constant.BooleanEnum.class);

    public QMemberApplicationConfig(String variable) {
        super(MemberApplicationConfig.class, forVariable(variable));
    }

    public QMemberApplicationConfig(Path<? extends MemberApplicationConfig> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberApplicationConfig(PathMetadata metadata) {
        super(MemberApplicationConfig.class, metadata);
    }

}

