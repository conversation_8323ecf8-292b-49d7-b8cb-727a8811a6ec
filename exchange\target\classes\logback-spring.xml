<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>logback</contextName>
    <!--Output to console -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
<!--            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%X{X-B3-TraceId}/%X{X-B3-SpanId}] %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [traceId=%X{X-B3-TraceId}, spanId=%X{X-B3-SpanId}] - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
    </root>
</configuration>