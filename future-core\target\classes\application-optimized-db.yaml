spring:
  datasource:
    hikari:
      # Core pool settings - optimized for high throughput
      minimum-idle: 15
      maximum-pool-size: 50

      # Connection lifecycle settings
      idle-timeout: 180000 # 3 minutes
      max-lifetime: 600000 # 10 minutes

      # Connection acquisition settings
      connection-timeout: 30000 # 30 seconds (reduced from 60s to avoid timeouts)
      initialization-fail-timeout: 2000 # 2 seconds

      # Connection validation settings
      validation-timeout: 3000 # 3 seconds
      keepalive-time: 60000 # 1 minute

      # Connection quality settings
      auto-commit: false
      transaction-isolation: TRANSACTION_READ_COMMITTED

      # Monitoring and debugging
      leak-detection-threshold: 30000 # 30 seconds
      register-mbeans: true

      # Connection testing
      connection-test-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true

      # Connection initialization
      connection-init-sql: SET statement_timeout = 30000; SET lock_timeout = 10000;

      # PostgreSQL specific optimizations
      data-source-properties:
        # Statement caching
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 4096

        # Server-side statement preparation
        useServerPrepStmts: true

        # Batch processing
        rewriteBatchedStatements: true

        # Metadata caching
        cacheResultSetMetadata: true
        cacheServerConfiguration: true

        # Connection management
        useLocalSessionState: true
        elideSetAutoCommits: true
        maintainTimeStats: false

        # Network settings
        tcpKeepAlive: true
        socketTimeout: 30
        connectTimeout: 10

        # Application name for monitoring
        ApplicationName: futures-core

  # JPA configuration optimized for connection stability
  jpa:
    open-in-view: false
    properties:
      hibernate:
        # JDBC settings
        jdbc:
          batch_size: 100
          batch_versioned_data: true
          time_zone: UTC
          statement_timeout: 30000

        # Connection management - optimized for stability
        connection:
          provider_disables_autocommit: false
          handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
          autoReconnect: true
          autoReconnectForPools: true
          is-connection-validation-required: true
          # Aggressive timeout checking
          checkout_timeout: 10000
          # Retry failed connections
          retry_attempts: 3
          retry_delay: 1000

        # Batch processing
        order_inserts: true
        order_updates: true
        default_batch_fetch_size: 200

        # Query optimization
        query:
          in_clause_parameter_padding: true
          plan_cache_max_size: 4096
          plan_parameter_metadata_max_size: 256

        # Performance settings
        generate_statistics: false

        # Fetch optimization
        max_fetch_depth: 3
