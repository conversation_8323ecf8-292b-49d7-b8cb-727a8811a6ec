package com.icetea.lotus.application.mapper;

import com.icetea.lotus.application.dto.KLineDto;
import com.icetea.lotus.core.domain.entity.KLine;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-05-27T17:32:40+0700",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Oracle Corporation)"
)
@Component
public class KLineMapperImpl implements KLineMapper {

    @Override
    public KLineDto toDto(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }

        KLineDto.KLineDtoBuilder kLineDto = KLineDto.builder();

        kLineDto.symbol( kLineSymbolValue( kLine ) );
        kLineDto.openPrice( kLineOpenPriceValue( kLine ) );
        kLineDto.highestPrice( kLineHighestPriceValue( kLine ) );
        kLineDto.lowestPrice( kLineLowestPriceValue( kLine ) );
        kLineDto.closePrice( kLineClosePriceValue( kLine ) );
        kLineDto.period( kLine.getPeriod() );
        kLineDto.time( kLine.getTime() );
        kLineDto.count( kLine.getCount() );
        kLineDto.volume( kLine.getVolume() );
        kLineDto.turnover( kLine.getTurnover() );
        kLineDto.createTime( kLine.getCreateTime() );
        kLineDto.updateTime( kLine.getUpdateTime() );

        return kLineDto.build();
    }

    @Override
    public List<KLineDto> toDtoList(List<KLine> kLines) {
        if ( kLines == null ) {
            return null;
        }

        List<KLineDto> list = new ArrayList<KLineDto>( kLines.size() );
        for ( KLine kLine : kLines ) {
            list.add( toDto( kLine ) );
        }

        return list;
    }

    private String kLineSymbolValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Symbol symbol = kLine.getSymbol();
        if ( symbol == null ) {
            return null;
        }
        String value = symbol.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineOpenPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money openPrice = kLine.getOpenPrice();
        if ( openPrice == null ) {
            return null;
        }
        BigDecimal value = openPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineHighestPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money highestPrice = kLine.getHighestPrice();
        if ( highestPrice == null ) {
            return null;
        }
        BigDecimal value = highestPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineLowestPriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money lowestPrice = kLine.getLowestPrice();
        if ( lowestPrice == null ) {
            return null;
        }
        BigDecimal value = lowestPrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }

    private BigDecimal kLineClosePriceValue(KLine kLine) {
        if ( kLine == null ) {
            return null;
        }
        Money closePrice = kLine.getClosePrice();
        if ( closePrice == null ) {
            return null;
        }
        BigDecimal value = closePrice.getValue();
        if ( value == null ) {
            return null;
        }
        return value;
    }
}
