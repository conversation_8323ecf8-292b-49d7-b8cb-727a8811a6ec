package com.icetea.lotus.core.domain.service.impl;

import com.icetea.lotus.core.common.LogMessages;
import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.MatchingAlgorithm;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.OrderDirection;
import com.icetea.lotus.core.domain.entity.OrderStatus;
import com.icetea.lotus.core.domain.entity.OrderType;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.repository.ContractRepository;
import com.icetea.lotus.core.domain.repository.OrderRepository;
import com.icetea.lotus.core.domain.repository.TradeRepository;
import com.icetea.lotus.core.domain.service.ImpliedMatchingService;
import com.icetea.lotus.core.domain.service.KLineManagementService;
import com.icetea.lotus.core.domain.service.LastPriceService;
import com.icetea.lotus.core.domain.service.MarketDataService;
import com.icetea.lotus.core.domain.service.OrderMatchingEngineService;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TradeId;
import com.icetea.lotus.infrastructure.config.MatchingEngineConfig;
import com.icetea.lotus.infrastructure.matching.DistributedLockingMatchingEngine;
import com.icetea.lotus.infrastructure.matching.LockFreeMatchingEngine;
import com.icetea.lotus.infrastructure.matching.OptimizedMatchingEngine;
import com.icetea.lotus.infrastructure.service.OrderVolumeBasedAlgorithmSelector;
import com.icetea.lotus.infrastructure.websocket.OrderBookHandler;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

/**
 * Implementation của domain service OrderMatchingEngineService
 * Thay thế cho ContractCoinMatch, ContractCoinMatchFactory và ContractCoinMatchExtension
 * Hợp nhất từ OrderMatchingEngineService, OrderMatchingExtensionService và OrderMatchingService
 */
@Slf4j
@Service
public class OrderMatchingEngineServiceImpl implements OrderMatchingEngineService {

    private final ContractRepository contractRepository;
    private final OrderRepository orderRepository;
    private final TradeRepository tradeRepository;
    private final ImpliedMatchingService impliedMatchingService;
    private final OptimizedMatchingEngine optimizedMatchingEngine;
    private final DistributedLockingMatchingEngine distributedLockingMatchingEngine;
    private final MatchingEngineConfig matchingEngineConfig;
    private final OrderBookHandler orderBookHandler;
    private final MarketDataService marketDataService;
    private final KLineManagementService kLineManagementService;
    private final LastPriceService lastPriceService;
    private final OrderVolumeBasedAlgorithmSelector algorithmSelector;

    // Lock cho việc đồng bộ hóa
    private final ReadWriteLock lock = new ReentrantReadWriteLock();

    // Thread pool cho việc xử lý đồng thời
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    // Scheduled executor cho việc kiểm tra định kỳ
    private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(2);

    // Cache cho các giá trị thường xuyên truy cập
    private final Map<String, Money> markPriceCache = new ConcurrentHashMap<>();
    private final Map<String, Money> indexPriceCache = new ConcurrentHashMap<>();
    // Cache cho giá cuối cùng đã được chuyển sang LastPriceService

    // Cache cho các vị thế cần kiểm tra thanh lý
    private final Map<PositionId, Position> positionsToCheck = new ConcurrentHashMap<>();

    // Cache cho các matching engine
    private final Map<String, MatchingEngine> matchingEngines = new ConcurrentHashMap<>();
    private final Map<String, OptimizedMatchingEngine> optimizedMatchingEngines = new ConcurrentHashMap<>();
    private final Map<String, LockFreeMatchingEngine> lockFreeMatchingEngines = new ConcurrentHashMap<>();

    // Maps cho các tính năng mở rộng
    private final Map<String, List<Order>> triggerOrdersMap = new ConcurrentHashMap<>();
    private final Map<String, Map<Long, Position>> positionsToCheckMap = new ConcurrentHashMap<>();
    private final Map<String, Money> fundingRateMap = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> nextFundingTimeMap = new ConcurrentHashMap<>();

    // Maps cho OrderMatchingService
    private final Map<Symbol, com.icetea.lotus.core.domain.service.OrderBook> orderBooks = new ConcurrentHashMap<>();

    /**
     * Constructor
     * Khởi tạo các scheduled tasks
     */
    public OrderMatchingEngineServiceImpl(
            ContractRepository contractRepository,
            @Qualifier("orderPersistenceAdapter") OrderRepository orderRepository,
            TradeRepository tradeRepository,
            ImpliedMatchingService impliedMatchingService,
            DistributedLockingMatchingEngine distributedLockingMatchingEngine,
            @Lazy OptimizedMatchingEngine optimizedMatchingEngine,
            MatchingEngineConfig matchingEngineConfig,
            OrderBookHandler orderBookHandler,
            @Lazy MarketDataService marketDataService,
            KLineManagementService kLineManagementService,
            LastPriceService lastPriceService,
            OrderVolumeBasedAlgorithmSelector algorithmSelector) {
        this.contractRepository = contractRepository;
        this.orderRepository = orderRepository;
        this.tradeRepository = tradeRepository;
        this.impliedMatchingService = impliedMatchingService;
        this.optimizedMatchingEngine = optimizedMatchingEngine;
        this.distributedLockingMatchingEngine = distributedLockingMatchingEngine;
        this.matchingEngineConfig = matchingEngineConfig;
        this.orderBookHandler = orderBookHandler;
        this.marketDataService = marketDataService;
        this.kLineManagementService = kLineManagementService;
        this.lastPriceService = lastPriceService;
        this.algorithmSelector = algorithmSelector;

        // Khởi tạo scheduled tasks
        initializeScheduledTasks();
    }

    /**
     * Khởi tạo các matching engine khi ứng dụng khởi động
     */
    @PostConstruct
    public void initializeMatchingEngines() {
        log.info("Khởi tạo các matching engine khi ứng dụng khởi động...");
        synchronizeWithContracts();
    }

    /**
     * Khởi tạo các scheduled tasks
     */
    private void initializeScheduledTasks() {
        // Đồng bộ hóa matching engine với hợp đồng mỗi 5 phút
        scheduledExecutorService.scheduleAtFixedRate(
                this::synchronizeWithContracts,
                0,
                5,
                TimeUnit.MINUTES
        );

        // Kiểm tra lệnh chờ mỗi 1 giây
        scheduledExecutorService.scheduleAtFixedRate(
                () -> {
                    for (String symbolStr : matchingEngines.keySet()) {
                        try {
                            checkTriggerOrders(Symbol.of(symbolStr));
                        } catch (Exception e) {
                            log.error(LogMessages.OrderMatching.ERROR_CHECK_TRIGGER_ORDERS(), symbolStr, e);
                        }
                    }
                },
                0,
                1,
                TimeUnit.SECONDS
        );

        // Kiểm tra thanh lý mỗi 1 giây
        scheduledExecutorService.scheduleAtFixedRate(
                () -> {
                    for (String symbolStr : matchingEngines.keySet()) {
                        try {
                            checkLiquidations(Symbol.of(symbolStr));
                        } catch (Exception e) {
                            log.error(LogMessages.OrderMatching.ERROR_CHECK_LIQUIDATIONS(), symbolStr, e);
                        }
                    }
                },
                0,
                1,
                TimeUnit.SECONDS
        );
    }

    /**
     * Đặt lệnh mới
     * @param order Lệnh giao dịch
     * @return List<Trade>
     */
    @Override
    public List<Trade> placeOrder(Order order) {
        if (order == null || order.getSymbol() == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        Symbol symbol = order.getSymbol();

        // Lưu lệnh vào cơ sở dữ liệu
        Order savedOrder = orderRepository.save(order);

        try {
            // Chọn thuật toán khớp lệnh dựa trên khối lượng lệnh
            MatchingAlgorithm originalAlgorithm = algorithmSelector.selectAlgorithm(savedOrder);
            log.debug("Đã chọn thuật toán khớp lệnh {} cho lệnh {}, symbol: {}, volume: {}",
                    originalAlgorithm, savedOrder.getOrderId(), symbol.getValue(), savedOrder.getVolume());

            // Xử lý lệnh với DistributedLockingMatchingEngine
            List<Trade> trades = distributedLockingMatchingEngine.processOrder(savedOrder);

            // Khôi phục thuật toán khớp lệnh về thuật toán ban đầu
            algorithmSelector.restoreAlgorithm(symbol, originalAlgorithm);
            log.debug("Đã khôi phục thuật toán khớp lệnh về {} cho symbol: {}", originalAlgorithm, symbol.getValue());

            // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
            if (trades != null && !trades.isEmpty()) {
                // Cập nhật giá giao dịch cuối cùng trong MarketDataService
                Trade lastTrade = trades.get(trades.size() - 1);
                marketDataService.updateLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Lưu giá cuối cùng vào cơ sở dữ liệu và đồng bộ giữa các instance
                lastPriceService.saveLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Cập nhật K-line với các giao dịch mới
                for (Trade trade : trades) {
                    try {
                        kLineManagementService.updateKLineWithTrade(trade);
                        log.debug("Đã cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}, price = {}, volume = {}",
                                symbol, trade.getId(), trade.getPrice(), trade.getVolume());
                    } catch (Exception e) {
                        log.error("Lỗi khi cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}",
                                symbol, trade.getId(), e);
                    }
                }

                log.debug("Đã cập nhật giá giao dịch cuối cùng cho symbol = {}, price = {}, volume = {}",
                        symbol, lastTrade.getPrice(), lastTrade.getVolume());
            }

            return trades != null ? trades : Collections.emptyList();
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_PLACE_ORDER(), order.getId(), symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Xử lý lệnh mới
     * @param order Lệnh cần xử lý
     * @return Danh sách các giao dịch được tạo ra
     */
    @Override
    public List<Trade> processOrder(Order order) {
        if (order == null || order.getSymbol() == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        Symbol symbol = order.getSymbol();

        try {
            // Chọn thuật toán khớp lệnh dựa trên khối lượng lệnh
            MatchingAlgorithm originalAlgorithm = algorithmSelector.selectAlgorithm(order);
            log.debug("Đã chọn thuật toán khớp lệnh {} cho lệnh {}, symbol: {}, volume: {}",
                    originalAlgorithm, order.getOrderId(), symbol.getValue(), order.getVolume());

            // Xử lý lệnh với DistributedLockingMatchingEngine
            List<Trade> trades = distributedLockingMatchingEngine.processOrder(order);

            // Khôi phục thuật toán khớp lệnh về thuật toán ban đầu
            algorithmSelector.restoreAlgorithm(symbol, originalAlgorithm);
            log.debug("Đã khôi phục thuật toán khớp lệnh về {} cho symbol: {}", originalAlgorithm, symbol.getValue());

            // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
            if (trades != null && !trades.isEmpty()) {
                // Cập nhật giá giao dịch cuối cùng trong MarketDataService
                Trade lastTrade = trades.get(trades.size() - 1);
                marketDataService.updateLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Lưu giá cuối cùng vào cơ sở dữ liệu và đồng bộ giữa các instance
                lastPriceService.saveLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Cập nhật K-line với các giao dịch mới
                for (Trade trade : trades) {
                    try {
                        kLineManagementService.updateKLineWithTrade(trade);
                        log.debug("Đã cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}, price = {}, volume = {}",
                                symbol, trade.getId(), trade.getPrice(), trade.getVolume());
                    } catch (Exception e) {
                        log.error("Lỗi khi cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}",
                                symbol, trade.getId(), e);
                    }
                }

                log.debug("Đã cập nhật giá giao dịch cuối cùng cho symbol = {}, price = {}, volume = {}",
                        symbol, lastTrade.getPrice(), lastTrade.getVolume());
            }

            return trades != null ? trades : Collections.emptyList();
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_PROCESS_ORDER(), order.getId(), symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Hủy lệnh
     * @param order Lệnh giao dịch
     * @return boolean
     */
    @Override
    public boolean cancelOrder(Order order) {
        if (order == null || order.getSymbol() == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return false;
        }

        Symbol symbol = order.getSymbol();

        try {
            // Cập nhật trạng thái lệnh
            Order canceledOrder = order.toBuilder()
                    .status(OrderStatus.CANCELED)
                    .canceledTime(LocalDateTime.now())
                    .build();

            // Lưu lệnh vào cơ sở dữ liệu
            orderRepository.save(canceledOrder);

            // Hủy lệnh trong matching engine
            // Matching engine sẽ tự động phát ra sự kiện OrderBookChangedEvent nếu hủy lệnh thành công
            return distributedLockingMatchingEngine.cancelOrder(canceledOrder.getOrderId(), order.getSymbol());
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_CANCEL_ORDER(), order.getId(), symbol, e);
            return false;
        }
    }

    /**
     * Hủy lệnh theo ID
     * @param orderId ID của lệnh
     * @param symbol Symbol của hợp đồng
     * @return boolean
     */
    @Override
    public boolean cancelOrder(OrderId orderId, Symbol symbol) {
        if (orderId == null || symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return false;
        }

        try {
            // Tìm lệnh trong cơ sở dữ liệu
            // Phương thức cancelOrder(Order) sẽ gọi engine.cancelOrder(order)
            // Matching engine sẽ tự động phát ra sự kiện OrderBookChangedEvent nếu hủy lệnh thành công
            return orderRepository.findById(orderId)
                    .map(this::cancelOrder)
                    .orElse(false);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_CANCEL_ORDER(), orderId, symbol, e);
            return false;
        }
    }

    /**
     * Tìm lệnh theo ID
     * @param orderId ID của lệnh
     * @return Optional<Order>
     */
    @Override
    public java.util.Optional<Order> findOrderById(OrderId orderId) {
        if (orderId == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return java.util.Optional.empty();
        }

        try {
            return orderRepository.findById(orderId);
        } catch (Exception e) {
            log.error("Lỗi khi tìm lệnh theo ID: {}", orderId, e);
            return java.util.Optional.empty();
        }
    }

    /**
     * Hủy tất cả lệnh của một thành viên
     * @param memberId ID của thành viên
     * @param symbol Symbol của hợp đồng
     * @return List<Order>
     */
    @Override
    public List<Order> cancelAllOrders(Long memberId, Symbol symbol) {
        if (memberId == null || symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Collections.emptyList();
        }

        try {
            // Tìm tất cả lệnh đang mở của thành viên
            List<Order> activeOrders = orderRepository.findByMemberIdAndSymbolAndStatus(
                    memberId, symbol, OrderStatus.NEW);

            List<Order> canceledOrders = new ArrayList<>();

            // Hủy từng lệnh
            for (Order order : activeOrders) {
                if (cancelOrder(order)) {
                    canceledOrders.add(order);
                }
            }

            return canceledOrders;
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_CANCEL_ALL_ORDERS(), memberId, symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Tạm dừng giao dịch
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void pauseTrading(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Tạm dừng giao dịch trên DistributedLockingMatchingEngine
            distributedLockingMatchingEngine.setTradingEnabled(symbol, false);
            log.info(LogMessages.OrderMatching.INFO_TRADING_PAUSED(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_TRADING_PAUSED(), symbol, e);
        }
    }

    /**
     * Tiếp tục giao dịch
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void resumeTrading(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Tiếp tục giao dịch trên DistributedLockingMatchingEngine
            distributedLockingMatchingEngine.setTradingEnabled(symbol, true);
            log.info(LogMessages.OrderMatching.INFO_TRADING_RESUMED(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_TRADING_RESUMED(), symbol, e);
        }
    }

    /**
     * Cập nhật giá đánh dấu
     * @param symbol Symbol của hợp đồng
     * @param markPrice Giá đánh dấu
     */
    @Override
    public void updateMarkPrice(Symbol symbol, Money markPrice) {
        if (symbol == null || markPrice == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Cập nhật cache
            markPriceCache.put(symbol.getValue(), markPrice);

            // Cập nhật giá đánh dấu trên DistributedLockingMatchingEngine
            distributedLockingMatchingEngine.updateMarkPrice(symbol, markPrice);

            log.debug(LogMessages.OrderMatching.DEBUG_MARK_PRICE_UPDATED(), symbol, markPrice);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_UPDATE_MARK_PRICE(), symbol, markPrice, e);
        }
    }

    /**
     * Cập nhật giá chỉ số
     * @param symbol Symbol của hợp đồng
     * @param indexPrice Giá chỉ số
     */
    @Override
    public void updateIndexPrice(Symbol symbol, Money indexPrice) {
        if (symbol == null || indexPrice == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Cập nhật cache
            indexPriceCache.put(symbol.getValue(), indexPrice);

            // Cập nhật giá chỉ số trên DistributedLockingMatchingEngine
            distributedLockingMatchingEngine.updateIndexPrice(symbol, indexPrice);

            log.debug(LogMessages.OrderMatching.DEBUG_INDEX_PRICE_UPDATED(), symbol, indexPrice);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_UPDATE_INDEX_PRICE(), symbol, indexPrice, e);
        }
    }

    /**
     * Lấy giá đánh dấu
     * @param symbol Symbol của hợp đồng
     * @return Money
     */
    @Override
    public Money getMarkPrice(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Money.ZERO;
        }

        String symbolStr = symbol.getValue();

        // Kiểm tra cache
        Money cachedPrice = markPriceCache.get(symbolStr);
        if (cachedPrice != null) {
            return cachedPrice;
        }

        // Lấy giá đánh dấu từ cache
        Money markPrice = markPriceCache.get(symbolStr);

        // Cập nhật cache
        if (markPrice != null) {
            markPriceCache.put(symbolStr, markPrice);
        }

        return markPrice != null ? markPrice : Money.ZERO;
    }

    /**
     * Lấy giá chỉ số
     * @param symbol Symbol của hợp đồng
     * @return Money
     */
    @Override
    public Money getIndexPrice(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Money.ZERO;
        }

        String symbolStr = symbol.getValue();

        // Kiểm tra cache
        Money cachedPrice = indexPriceCache.get(symbolStr);
        if (cachedPrice != null) {
            return cachedPrice;
        }

        // Lấy giá chỉ số từ cache
        Money indexPrice = indexPriceCache.get(symbolStr);

        // Cập nhật cache
        if (indexPrice != null) {
            indexPriceCache.put(symbolStr, indexPrice);
        }

        return indexPrice != null ? indexPrice : Money.ZERO;
    }

    /**
     * Lấy giá giao dịch cuối cùng
     * @param symbol Symbol của hợp đồng
     * @return Money
     */
    @Override
    public Money getLastPrice(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Money.ZERO;
        }

        // Lấy giá cuối cùng từ LastPriceService
        return lastPriceService.getLastPrice(symbol);
    }

    /**
     * Kiểm tra lệnh chờ
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void checkTriggerOrders(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Không cần kiểm tra lệnh chờ trên DistributedLockingMatchingEngine vì nó đã được xử lý tự động
            log.debug(LogMessages.OrderMatching.DEBUG_TRIGGER_ORDERS_CHECKED(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_CHECK_TRIGGER_ORDERS(), symbol, e);
        }
    }

    /**
     * Kiểm tra thanh lý
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void checkLiquidations(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Không cần kiểm tra thanh lý trên DistributedLockingMatchingEngine vì nó đã được xử lý tự động
            log.debug(LogMessages.OrderMatching.DEBUG_LIQUIDATIONS_CHECKED(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_CHECK_LIQUIDATIONS(), symbol, e);
        }
    }

    /**
     * Thanh lý vị thế
     * @param position Vị thế cần thanh lý
     * @return List<Trade>
     */
    @Override
    public List<Trade> liquidatePosition(Position position) {
        if (position == null || position.getSymbol() == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        Symbol symbol = position.getSymbol();

        try {
            // Tạo lệnh thanh lý
            OrderDirection direction = position.getDirection() == PositionDirection.LONG ?
                    OrderDirection.SELL : OrderDirection.BUY;

            Order liquidationOrder = Order.builder()
                    .orderId(OrderId.of(System.currentTimeMillis()))
                    .memberId(position.getMemberId())
                    .symbol(position.getSymbol())
                    .direction(direction)
                    .type(OrderType.MARKET)
                    .price(optimizedMatchingEngine.getMarkPrice(symbol))
                    .volume(position.getVolume())
                    .status(OrderStatus.NEW)
                    .createTime(LocalDateTime.now())
                    .build();

            // Lưu lệnh vào cơ sở dữ liệu
            Order savedOrder = orderRepository.save(liquidationOrder);

            // Chọn thuật toán khớp lệnh dựa trên khối lượng lệnh
            MatchingAlgorithm originalAlgorithm = algorithmSelector.selectAlgorithm(savedOrder);
            log.debug("Đã chọn thuật toán khớp lệnh {} cho lệnh thanh lý {}, symbol: {}, volume: {}",
                    originalAlgorithm, savedOrder.getOrderId(), symbol.getValue(), savedOrder.getVolume());

            // Khớp lệnh
            List<Trade> trades = distributedLockingMatchingEngine.processOrder(savedOrder);

            // Khôi phục thuật toán khớp lệnh về thuật toán ban đầu
            algorithmSelector.restoreAlgorithm(symbol, originalAlgorithm);
            log.debug("Đã khôi phục thuật toán khớp lệnh về {} cho symbol: {}", originalAlgorithm, symbol.getValue());

            // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
            if (trades != null && !trades.isEmpty()) {
                // Cập nhật giá giao dịch cuối cùng trong MarketDataService
                Trade lastTrade = trades.get(trades.size() - 1);
                marketDataService.updateLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Lưu giá cuối cùng vào cơ sở dữ liệu và đồng bộ giữa các instance
                lastPriceService.saveLastPrice(symbol, lastTrade.getPrice(), lastTrade.getVolume());

                // Cập nhật K-line với các giao dịch mới
                for (Trade trade : trades) {
                    try {
                        kLineManagementService.updateKLineWithTrade(trade);
                        log.debug("Đã cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}, price = {}, volume = {}",
                                symbol, trade.getId(), trade.getPrice(), trade.getVolume());
                    } catch (Exception e) {
                        log.error("Lỗi khi cập nhật K-line với giao dịch mới, symbol = {}, tradeId = {}",
                                symbol, trade.getId(), e);
                    }
                }

                log.debug("Đã cập nhật giá giao dịch cuối cùng cho symbol = {}, price = {}, volume = {}",
                        symbol, lastTrade.getPrice(), lastTrade.getVolume());
            }

            return trades != null ? trades : Collections.emptyList();
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_LIQUIDATE_POSITION(), position.getId(), symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Đồng bộ hóa matching engine với hợp đồng
     */
    @Override
    public void synchronizeWithContracts() {
        log.info(LogMessages.OrderMatching.INFO_SYNC_STARTED());

        try {
            // Lấy danh sách hợp đồng đã kích hoạt
            List<Contract> enabledContracts = contractRepository.findAllEnabled();

            // Kiểm tra null
            if (enabledContracts == null) {
                log.warn(LogMessages.OrderMatching.WARN_ENABLED_CONTRACTS_NOT_FOUND());
                return;
            }

            // Tạo map để tìm kiếm nhanh hơn
            Map<String, Contract> enabledContractMap = new HashMap<>();
            for (Contract contract : enabledContracts) {
                if (contract != null && contract.getSymbol() != null) {
                    enabledContractMap.put(contract.getSymbol().getValue(), contract);
                }
            }

            // Khóa để đọc và ghi
            lock.writeLock().lock();

            try {
                // Xóa các matching engine không còn được kích hoạt
                List<String> symbolsToRemove = new ArrayList<>();
                for (String symbolStr : matchingEngines.keySet()) {
                    if (!enabledContractMap.containsKey(symbolStr)) {
                        symbolsToRemove.add(symbolStr);
                    }
                }

                // Xóa các matching engine
                for (String symbolStr : symbolsToRemove) {
                    shutdown(Symbol.of(symbolStr));
                }

                // Thêm các matching engine mới cho các hợp đồng đã được kích hoạt
                List<String> symbolsToInitialize = new ArrayList<>();
                for (String symbolStr : enabledContractMap.keySet()) {
                    if (!matchingEngines.containsKey(symbolStr)) {
                        symbolsToInitialize.add(symbolStr);
                    }
                }

                // Khởi tạo các matching engine mới bằng thread pool
                for (String symbolStr : symbolsToInitialize) {
                    final Symbol symbol = Symbol.of(symbolStr);
                    executorService.submit(() -> {
                        try {
                            initialize(symbol);
                        } catch (Exception e) {
                            log.error("Lỗi khi khởi tạo matching engine, symbol = {}", symbol, e);
                        }
                    });
                }

                log.info(LogMessages.OrderMatching.INFO_SYNC_COMPLETED(), matchingEngines.size());
            } finally {
                // Giải phóng lock
                lock.writeLock().unlock();
            }
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_SYNC_CONTRACTS(), e);
        }
    }

    /**
     * Khởi tạo matching engine
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void initialize(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        String symbolStr = symbol.getValue();

        // Kiểm tra xem matching engine đã tồn tại chưa
        if (matchingEngines.containsKey(symbolStr)) {
            log.info("Matching engine đã tồn tại cho symbol {}", symbol);
            return;
        }

        try {
            // Lấy thông tin hợp đồng
            Contract contract = contractRepository.findBySymbol(symbol);
            if (contract == null) {
                throw new IllegalArgumentException("Hợp đồng không tồn tại hoặc không được kích hoạt");
            }

            // Tạo matching engine mới
            MatchingEngine engine = new MatchingEngine(symbol, contract);

            // Lưu vào map
            matchingEngines.put(symbolStr, engine);

            log.info(LogMessages.OrderMatching.INFO_ENGINE_INITIALIZED(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_INIT_ENGINE(), symbol, e);
        }
    }

    /**
     * Dừng và giải phóng tài nguyên
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void shutdown(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        String symbolStr = symbol.getValue();
        MatchingEngine engine = matchingEngines.remove(symbolStr);

        if (engine == null) {
            log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
            return;
        }

        try {
            // Dừng giao dịch
            engine.setTradingHalt(true);

            // Giải phóng tài nguyên
            engine.shutdown();

            // Xóa cache
            markPriceCache.remove(symbolStr);
            indexPriceCache.remove(symbolStr);

            log.info(LogMessages.OrderMatching.INFO_ENGINE_SHUTDOWN(), symbol);
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_INIT_ENGINE(), symbol, e);
        }
    }

    /**
     * Giải phóng tài nguyên khi bean bị hủy
     */
    @PreDestroy
    public void destroy() {
        log.info("Đang giải phóng tài nguyên của OrderMatchingEngineService");

        try {
            // Dừng các scheduled tasks
            scheduledExecutorService.shutdown();
            if (!scheduledExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduledExecutorService.shutdownNow();
            }

            // Dừng thread pool
            executorService.shutdown();
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }

            // Dừng tất cả matching engine
            for (String symbolStr : new ArrayList<>(matchingEngines.keySet())) {
                shutdown(Symbol.of(symbolStr));
            }

            // Xóa tất cả cache
            markPriceCache.clear();
            indexPriceCache.clear();

            log.info(LogMessages.OrderMatching.INFO_SHUTDOWN_COMPLETED());
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_SHUTDOWN(), e);
        }
    }

    /**
     * Lấy sổ lệnh
     * @param symbol Symbol của hợp đồng
     * @return OrderBook
     */
    @Override
    public com.icetea.lotus.core.domain.service.OrderBook getOrderBook(Symbol symbol) {
//        log.info("getOrderBook: {}", symbol);
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return null;
        }

        try {
            // Ưu tiên sử dụng DistributedLockingMatchingEngine nếu có
            if (matchingEngineConfig.isUseDistributed()) {
                try {
//                    log.info("Lấy orderbook từ DistributedLockingMatchingEngine");
                    // Lấy orderbook từ DistributedLockingMatchingEngine
                    com.icetea.lotus.core.domain.service.OrderBook orderBook = distributedLockingMatchingEngine.getOrderBook(symbol);
//                    log.info("getOrderBook orderBook: {}", orderBook != null ? orderBook.toString() : "null");
                    if (orderBook != null) {
                        // Gửi orderbook qua WebSocket
                        orderBookHandler.handleOrderBook(symbol.getValue(), orderBook);
                        return orderBook;
                    }
                } catch (Exception e) {
                    log.warn("Không thể lấy orderbook từ DistributedLockingMatchingEngine, symbol = {}", symbol, e);
                }
            }

            // Nếu không có DistributedLockingMatchingEngine, thử sử dụng LockFreeMatchingEngine
            if (matchingEngineConfig.isUseLockFree()) {
                LockFreeMatchingEngine lockFreeEngine = getLockFreeMatchingEngine(symbol);
                if (lockFreeEngine != null) {
                    com.icetea.lotus.core.domain.service.OrderBook orderBook = lockFreeEngine.getOrderBook();
                    if (orderBook != null) {
                        // Gửi orderbook qua WebSocket
                        orderBookHandler.handleOrderBook(symbol.getValue(), orderBook);
                        return orderBook;
                    }
                }
            }

            // Fallback: Sử dụng matching engine cũ
            MatchingEngine engine = getMatchingEngine(symbol);
            if (engine == null) {
                log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
                return null;
            }

            // Lấy thông tin từ matching engine để tạo OrderBook
            Money lastPrice = engine.lastPrice;
            Money markPrice = engine.markPrice;
            Money indexPrice = engine.indexPrice;

            // Tạo OrderBook mới từ thông tin của matching engine
            com.icetea.lotus.core.domain.service.OrderBook.OrderBookBuilder builder =
                    com.icetea.lotus.core.domain.service.OrderBook.builder()
                    .symbol(symbol)
                    .lastPrice(lastPrice)
                    .markPrice(markPrice)
                    .indexPrice(indexPrice);

            // Lấy thông tin bids và asks từ matching engine
            Map<OrderDirection, Map<Money, List<Order>>> ordersByPrice = engine.ordersByPrice;

            // Chuyển đổi bids
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> bidLevels = new ArrayList<>();
            Map<Money, List<Order>> bids = ordersByPrice.get(OrderDirection.BUY);
            if (bids != null) {
                for (Map.Entry<Money, List<Order>> entry : bids.entrySet()) {
                    Money price = entry.getKey();
                    List<Order> orders = entry.getValue();
                    BigDecimal totalVolume = BigDecimal.ZERO;

                    // Tính tổng khối lượng
                    for (Order order : orders) {
                        BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());
                        totalVolume = totalVolume.add(remainingVolume);
                    }

                    bidLevels.add(
                        com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                            .price(price)
                            .volume(totalVolume)
                            .orders(new ArrayList<>(orders)) // Sao chép danh sách lệnh
                            .build()
                    );
                }
            }
            builder.bids(bidLevels);

            // Chuyển đổi asks
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> askLevels = new ArrayList<>();
            Map<Money, List<Order>> asks = ordersByPrice.get(OrderDirection.SELL);
            if (asks != null) {
                for (Map.Entry<Money, List<Order>> entry : asks.entrySet()) {
                    Money price = entry.getKey();
                    List<Order> orders = entry.getValue();
                    BigDecimal totalVolume = BigDecimal.ZERO;

                    // Tính tổng khối lượng
                    for (Order order : orders) {
                        BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());
                        totalVolume = totalVolume.add(remainingVolume);
                    }

                    askLevels.add(
                        com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                            .price(price)
                            .volume(totalVolume)
                            .orders(new ArrayList<>(orders)) // Sao chép danh sách lệnh
                            .build()
                    );
                }
            }
            builder.asks(askLevels);

            com.icetea.lotus.core.domain.service.OrderBook orderBook = builder.build();

            // Gửi orderbook qua WebSocket
            orderBookHandler.handleOrderBook(symbol.getValue(), orderBook);

            return orderBook;
        } catch (Exception e) {
            log.error("Lỗi khi lấy sổ lệnh cho symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Lấy sổ lệnh với độ sâu giới hạn
     * @param symbol Symbol của hợp đồng
     * @param depth Độ sâu của sổ lệnh (số lượng mức giá)
     * @return OrderBook
     */
    public com.icetea.lotus.core.domain.service.OrderBook getOrderBook(Symbol symbol, int depth) {
        try {
            com.icetea.lotus.core.domain.service.OrderBook fullOrderBook = getOrderBook(symbol);

            if (fullOrderBook == null) {
                return null;
            }

            // Giới hạn số lượng mức giá
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> limitedBids = fullOrderBook.getBids().stream()
                    .limit(depth)
                    .collect(Collectors.toList());

            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> limitedAsks = fullOrderBook.getAsks().stream()
                    .limit(depth)
                    .collect(Collectors.toList());

            return com.icetea.lotus.core.domain.service.OrderBook.builder()
                    .symbol(fullOrderBook.getSymbol())
                    .bids(limitedBids)
                    .asks(limitedAsks)
                    .lastPrice(fullOrderBook.getLastPrice())
                    .markPrice(fullOrderBook.getMarkPrice())
                    .indexPrice(fullOrderBook.getIndexPrice())
                    .build();
        } catch (Exception e) {
            log.error("Lỗi khi lấy sổ lệnh với độ sâu giới hạn cho symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Thêm route trực tiếp cho Implied Matching
     * @param sourceSymbol Symbol nguồn
     * @param targetSymbol Symbol đích
     * @param invertDirection Có đảo ngược hướng không
     * @param priceRatio Tỷ lệ giá
     */
    @Override
    public void addImpliedRoute(Symbol sourceSymbol, Symbol targetSymbol, boolean invertDirection, BigDecimal priceRatio) {
        if (sourceSymbol == null || targetSymbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        if (priceRatio == null) {
            log.warn("Price ratio không hợp lệ");
            return;
        }

        try {
            impliedMatchingService.addImpliedRoute(sourceSymbol, targetSymbol, invertDirection, priceRatio);
            log.info("Đã thêm route trực tiếp: {} -> {}", sourceSymbol, targetSymbol);
        } catch (Exception e) {
            log.error("Lỗi khi thêm route trực tiếp, sourceSymbol = {}, targetSymbol = {}", sourceSymbol, targetSymbol, e);
        }
    }

    /**
     * Thêm route phức hợp cho Implied Matching
     * @param sourceSymbol Symbol nguồn
     * @param intermediateSymbol Symbol trung gian
     * @param targetSymbol Symbol đích
     */
    @Override
    public void addComplexImpliedRoute(Symbol sourceSymbol, Symbol intermediateSymbol, Symbol targetSymbol) {
        if (sourceSymbol == null || intermediateSymbol == null || targetSymbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            impliedMatchingService.addComplexImpliedRoute(sourceSymbol, intermediateSymbol, targetSymbol);
            log.info("Đã thêm route phức hợp: {} -> {} -> {}", sourceSymbol, intermediateSymbol, targetSymbol);
        } catch (Exception e) {
            log.error("Lỗi khi thêm route phức hợp, sourceSymbol = {}, intermediateSymbol = {}, targetSymbol = {}",
                    sourceSymbol, intermediateSymbol, targetSymbol, e);
        }
    }

    /**
     * Xóa tất cả các route cho một symbol
     * @param symbol Symbol cần xóa route
     */
    @Override
    public void clearImpliedRoutes(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            impliedMatchingService.clearImpliedRoutes(symbol);
            log.info("Đã xóa tất cả các route cho symbol {}", symbol);
        } catch (Exception e) {
            log.error("Lỗi khi xóa route, symbol = {}", symbol, e);
        }
    }

    /**
     * Thiết lập thuật toán khớp lệnh cho một symbol
     * Đồng bộ thuật toán giữa tất cả các loại matching engine
     * @param symbol Symbol của hợp đồng
     * @param algorithm Thuật toán khớp lệnh
     */
    @Override
    public void setMatchingAlgorithm(Symbol symbol, MatchingAlgorithm algorithm) {
        if (symbol == null || algorithm == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        try {
            // Đồng bộ thuật toán khớp lệnh giữa tất cả các loại matching engine
            boolean success = true;

            // 1. Thiết lập cho DistributedLockingMatchingEngine nếu được cấu hình
            if (matchingEngineConfig.isUseDistributed()) {
                try {
                    distributedLockingMatchingEngine.setMatchingAlgorithm(symbol, algorithm);
                    log.debug("Đã thiết lập thuật toán khớp lệnh {} cho DistributedLockingMatchingEngine, symbol {}", algorithm, symbol);
                } catch (Exception e) {
                    log.error("Lỗi khi thiết lập thuật toán khớp lệnh cho DistributedLockingMatchingEngine, symbol = {}, algorithm = {}", symbol, algorithm, e);
                    success = false;
                }
            }

            // 2. Thiết lập cho LockFreeMatchingEngine nếu được cấu hình
            if (matchingEngineConfig.isUseLockFree()) {
                LockFreeMatchingEngine lockFreeEngine = getLockFreeMatchingEngine(symbol);
                if (lockFreeEngine != null) {
                    try {
                        lockFreeEngine.setMatchingAlgorithm(algorithm);
                        log.debug("Đã thiết lập thuật toán khớp lệnh {} cho LockFreeMatchingEngine, symbol {}", algorithm, symbol);
                    } catch (Exception e) {
                        log.error("Lỗi khi thiết lập thuật toán khớp lệnh cho LockFreeMatchingEngine, symbol = {}, algorithm = {}", symbol, algorithm, e);
                        success = false;
                    }
                }
            }

            // 4. Thiết lập cho MatchingEngine nội bộ
            MatchingEngine defaultEngine = getMatchingEngine(symbol);
            if (defaultEngine != null) {
                try {
                    defaultEngine.setMatchingAlgorithm(algorithm);
                    log.debug("Đã thiết lập thuật toán khớp lệnh {} cho MatchingEngine nội bộ, symbol {}", algorithm, symbol);
                } catch (Exception e) {
                    log.error("Lỗi khi thiết lập thuật toán khớp lệnh cho MatchingEngine nội bộ, symbol = {}, algorithm = {}", symbol, algorithm, e);
                    success = false;
                }
            }

            if (success) {
                log.info("Đã thiết lập thuật toán khớp lệnh {} cho tất cả matching engine, symbol {}", algorithm, symbol);
            } else {
                log.warn("Có lỗi xảy ra khi thiết lập thuật toán khớp lệnh {} cho một số matching engine, symbol {}", algorithm, symbol);
            }
        } catch (Exception e) {
            log.error("Lỗi khi thiết lập thuật toán khớp lệnh, symbol = {}, algorithm = {}", symbol, algorithm, e);
        }
    }

    /**
     * Lấy thuật toán khớp lệnh hiện tại của một symbol
     * @param symbol Symbol của hợp đồng
     * @return MatchingAlgorithm
     */
    @Override
    public MatchingAlgorithm getMatchingAlgorithm(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return MatchingAlgorithm.FIFO; // Mặc định là FIFO
        }

        try {
            // Lấy thuật toán khớp lệnh từ engine được cấu hình là active
            MatchingAlgorithm algorithm = null;

            // Ưu tiên lấy từ engine được cấu hình là active
            if (matchingEngineConfig.isUseDistributed()) {
                // Không có phương thức getMatchingAlgorithm trong DistributedLockingMatchingEngine
                // Nên không thể lấy thuật toán từ đây
                log.debug("Không thể lấy thuật toán khớp lệnh từ DistributedLockingMatchingEngine");
            }

            if (matchingEngineConfig.isUseLockFree()) {
                LockFreeMatchingEngine lockFreeEngine = getLockFreeMatchingEngine(symbol);
                if (lockFreeEngine != null) {
                    algorithm = lockFreeEngine.getMatchingAlgorithm();
                    log.debug("Lấy thuật toán khớp lệnh từ LockFreeMatchingEngine: {}", algorithm);
                    return algorithm;
                }
            }

            if (matchingEngineConfig.isUseDistributed()) {
                // Không có phương thức getMatchingAlgorithm trong DistributedLockingMatchingEngine
                // Nên không thể lấy thuật toán từ đây
                log.debug("Không thể lấy thuật toán khớp lệnh từ DistributedLockingMatchingEngine");
            }

            // Fallback về MatchingEngine nội bộ
            MatchingEngine defaultEngine = getMatchingEngine(symbol);
            if (defaultEngine != null) {
                algorithm = defaultEngine.getMatchingAlgorithm();
                log.debug("Lấy thuật toán khớp lệnh từ MatchingEngine nội bộ: {}", algorithm);
                return algorithm;
            }

            log.warn("Không tìm thấy matching engine nào cho symbol {}, trả về thuật toán mặc định FIFO", symbol);
            return MatchingAlgorithm.FIFO; // Mặc định là FIFO
        } catch (Exception e) {
            log.error("Lỗi khi lấy thuật toán khớp lệnh, symbol = {}", symbol, e);
            return MatchingAlgorithm.FIFO; // Mặc định là FIFO
        }
    }

    /**
     * Tạo lệnh thanh lý
     * @param position Vị thế cần thanh lý
     * @param liquidationPrice Giá thanh lý
     * @return List<Trade>
     */
    @Override
    public List<Trade> createLiquidationOrder(Position position, Money liquidationPrice) {
        if (position == null || position.getSymbol() == null || liquidationPrice == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        Symbol symbol = position.getSymbol();
        MatchingEngine engine = getMatchingEngine(symbol);

        if (engine == null) {
            log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
            return Collections.emptyList();
        }

        try {
            // Tạo lệnh thanh lý
            OrderDirection direction = position.getDirection() == PositionDirection.LONG ?
                    OrderDirection.SELL : OrderDirection.BUY;

            Order liquidationOrder = Order.builder()
                    .orderId(OrderId.of(System.currentTimeMillis()))
                    .memberId(position.getMemberId())
                    .symbol(symbol)
                    .direction(direction)
                    .type(OrderType.MARKET)
                    .price(liquidationPrice)
                    .volume(position.getVolume())
                    .status(OrderStatus.NEW)
                    .createTime(LocalDateTime.now())
                    .build();

            // Lưu lệnh vào cơ sở dữ liệu
            Order savedOrder = orderRepository.save(liquidationOrder);

            // Khớp lệnh
            List<Trade> trades = engine.matchOrder(savedOrder);

            // Lưu các giao dịch vào cơ sở dữ liệu
            if (!trades.isEmpty()) {
                for (Trade trade : trades) {
                    tradeRepository.save(trade);
                }
            }

            log.info("Đã tạo lệnh thanh lý cho vị thế {}, orderId = {}", position.getId(), savedOrder.getId());

            return trades;
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_LIQUIDATE_POSITION(), position.getId(), symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Tạo lệnh ADL
     * @param position Vị thế cần ADL
     * @param volume Khối lượng cần đóng
     * @return List<Trade>
     */
    @Override
    public List<Trade> createADLOrder(Position position, Money volume) {
        if (position == null || position.getSymbol() == null || volume == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        Symbol symbol = position.getSymbol();
        MatchingEngine engine = getMatchingEngine(symbol);

        if (engine == null) {
            log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
            return Collections.emptyList();
        }

        try {
            // Tạo lệnh ADL
            OrderDirection direction = position.getDirection() == PositionDirection.LONG ?
                    OrderDirection.SELL : OrderDirection.BUY;

            Order adlOrder = Order.builder()
                    .orderId(OrderId.of(System.currentTimeMillis()))
                    .memberId(position.getMemberId())
                    .symbol(symbol)
                    .direction(direction)
                    .type(OrderType.MARKET)
                    .price(engine.getMarkPrice())
                    .volume(volume.getValue())
                    .status(OrderStatus.NEW)
                    .createTime(LocalDateTime.now())
                    .build();

            // Lưu lệnh vào cơ sở dữ liệu
            Order savedOrder = orderRepository.save(adlOrder);

            // Chọn thuật toán khớp lệnh dựa trên khối lượng lệnh
            MatchingAlgorithm originalAlgorithm = algorithmSelector.selectAlgorithm(savedOrder);
            log.debug("Đã chọn thuật toán khớp lệnh {} cho lệnh ADL {}, symbol: {}, volume: {}",
                    originalAlgorithm, savedOrder.getOrderId(), symbol.getValue(), savedOrder.getVolume());

            // Khớp lệnh
            List<Trade> trades = engine.matchOrder(savedOrder);

            // Khôi phục thuật toán khớp lệnh về thuật toán ban đầu
            algorithmSelector.restoreAlgorithm(symbol, originalAlgorithm);
            log.debug("Đã khôi phục thuật toán khớp lệnh về {} cho symbol: {}", originalAlgorithm, symbol.getValue());

            // Không lưu giao dịch ở đây, sẽ được lưu trong OrderEventConsumer

            log.info("Đã tạo lệnh ADL cho vị thế {}, orderId = {}", position.getId(), savedOrder.getId());

            return trades;
        } catch (Exception e) {
            log.error(LogMessages.OrderMatching.ERROR_LIQUIDATE_POSITION(), position.getId(), symbol, e);
            return Collections.emptyList();
        }
    }

    /**
     * Lấy matching engine theo symbol
     * @param symbol Symbol của hợp đồng
     * @return MatchingEngine
     */
    private MatchingEngine getMatchingEngine(Symbol symbol) {
        if (symbol == null) {
            return null;
        }

        String symbolStr = symbol.getValue();
        MatchingEngine engine = matchingEngines.get(symbolStr);

        if (engine == null) {
            // Khởi tạo matching engine nếu chưa tồn tại
            initialize(symbol);
            engine = matchingEngines.get(symbolStr);
        }

        return engine;
    }

    /**
     * Inner class cho matching engine
     */
    private class MatchingEngine {
        private final Symbol symbol;
        private final Contract contract;

        private boolean tradingHalt = false;
        private Money markPrice = Money.ZERO;
        private Money indexPrice = Money.ZERO;
        private Money lastPrice = Money.ZERO;
        private MatchingAlgorithm matchingAlgorithm = MatchingAlgorithm.FIFO;

        // Sổ lệnh
        private final Map<OrderId, Order> orders = new ConcurrentHashMap<>();
        private final Map<OrderDirection, Map<Money, List<Order>>> ordersByPrice = new EnumMap<>(OrderDirection.class);

        // Lệnh chờ
        private final List<Order> triggerOrders = new ArrayList<>();

        // Vị thế cần kiểm tra thanh lý
        private final Map<PositionId, Position> positionsToCheck = new ConcurrentHashMap<>();

        // Lock cho việc đồng bộ hóa
        private final ReadWriteLock orderBookLock = new ReentrantReadWriteLock();

        /**
         * Constructor
         * @param symbol Symbol của hợp đồng
         * @param contract Thông tin hợp đồng
         */
        public MatchingEngine(Symbol symbol, Contract contract) {
            this.symbol = symbol;
            this.contract = contract;

            // Khởi tạo sổ lệnh
            ordersByPrice.put(OrderDirection.BUY, new HashMap<>());
            ordersByPrice.put(OrderDirection.SELL, new HashMap<>());
        }

        /**
         * Khớp lệnh
         * @param order Lệnh giao dịch
         * @return List<Trade>
         */
        public List<Trade> matchOrder(Order order) {
            // Triển khai logic khớp lệnh dựa trên thuật toán đã chọn
            switch (matchingAlgorithm) {
                case FIFO:
                    return matchOrderFIFO(order);
                case PRO_RATA:
                    return matchOrderProRata(order);
                case HYBRID:
                    return matchOrderHybrid(order);
                case TWAP:
                    return matchOrderTWAP(order);
                default:
                    return matchOrderFIFO(order);
            }
        }

        /**
         * Khớp lệnh theo thuật toán TWAP (Time-Weighted Average Price)
         * @param order Lệnh giao dịch
         * @return List<Trade>
         */
        private List<Trade> matchOrderTWAP(Order order) {
            if (order == null) {
                return Collections.emptyList();
            }

            // TWAP chia nhỏ lệnh lớn thành nhiều lệnh nhỏ và thực hiện theo thời gian
            // Trong trường hợp này, chúng ta sẽ mô phỏng bằng cách khớp lệnh theo FIFO
            // nhưng với tốc độ chậm hơn để giảm tác động thị trường

            log.debug("Khớp lệnh theo thuật toán TWAP cho lệnh {}, symbol: {}, volume: {}",
                    order.getOrderId(), symbol.getValue(), order.getVolume());

            // Trong môi trường thực tế, chúng ta sẽ chia nhỏ lệnh và thực hiện theo thời gian
            // Nhưng trong môi trường mô phỏng này, chúng ta sẽ sử dụng FIFO
            return matchOrderFIFO(order);
        }

        /**
         * Khớp lệnh theo thuật toán FIFO
         * @param order Lệnh giao dịch
         * @return List<Trade>
         */
        private List<Trade> matchOrderFIFO(Order order) {
            if (order == null) {
                return Collections.emptyList();
            }

            List<Trade> trades = new ArrayList<>();

            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.writeLock().lock();

                // Xác định hướng đối ứng
                OrderDirection oppositeDirection = order.getDirection() == OrderDirection.BUY ?
                        OrderDirection.SELL : OrderDirection.BUY;

                // Lấy danh sách lệnh đối ứng theo giá
                Map<Money, List<Order>> oppositeOrdersByPrice = ordersByPrice.get(oppositeDirection);

                // Sắp xếp giá theo hướng đối ứng
                List<Money> sortedPrices = new ArrayList<>(oppositeOrdersByPrice.keySet());
                if (oppositeDirection == OrderDirection.SELL) {
                    // Nếu đối ứng là bán, sắp xếp giá tăng dần
                    sortedPrices.sort(Money::compareTo);
                } else {
                    // Nếu đối ứng là mua, sắp xếp giá giảm dần
                    sortedPrices.sort((p1, p2) -> p2.compareTo(p1));
                }

                // Khối lượng còn lại cần khớp
                BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());

                // Duyệt qua các mức giá
                for (Money price : sortedPrices) {
                    // Kiểm tra điều kiện giá
                    if (order.getType() == OrderType.LIMIT) {
                        if (order.getDirection() == OrderDirection.BUY && price.compareTo(order.getPrice()) > 0) {
                            // Nếu là lệnh mua giới hạn và giá bán > giá mua, bỏ qua
                            continue;
                        } else if (order.getDirection() == OrderDirection.SELL && price.compareTo(order.getPrice()) < 0) {
                            // Nếu là lệnh bán giới hạn và giá mua < giá bán, bỏ qua
                            continue;
                        }
                    }

                    // Lấy danh sách lệnh ở mức giá này
                    List<Order> ordersAtPrice = oppositeOrdersByPrice.get(price);

                    // Duyệt qua các lệnh theo thứ tự thời gian (FIFO)
                    Iterator<Order> iterator = ordersAtPrice.iterator();
                    while (iterator.hasNext() && remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
                        Order matchingOrder = iterator.next();

                        // Khối lượng còn lại của lệnh đối ứng
                        BigDecimal matchingRemainingVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());

                        // Khối lượng khớp
                        BigDecimal matchedVolume = remainingVolume.min(matchingRemainingVolume);

                        // Tạo giao dịch
                        Trade trade = createTrade(order, matchingOrder, matchedVolume, price);
                        trades.add(trade);

                        // Cập nhật khối lượng đã khớp
                        remainingVolume = remainingVolume.subtract(matchedVolume);

                        // Cập nhật lệnh đối ứng
                        Order updatedMatchingOrder = matchingOrder.toBuilder()
                                .dealVolume(matchingOrder.getDealVolume().add(matchedVolume))
                                .status(matchingOrder.getDealVolume().add(matchedVolume).compareTo(matchingOrder.getVolume()) >= 0 ?
                                        OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                                .build();

                        // Cập nhật trong sổ lệnh
                        orders.put(updatedMatchingOrder.getOrderId(), updatedMatchingOrder);

                        // Nếu lệnh đối ứng đã khớp hết, xóa khỏi danh sách
                        if (updatedMatchingOrder.getStatus() == OrderStatus.FILLED) {
                            iterator.remove();
                        }

                        // Lưu lệnh đối ứng vào cơ sở dữ liệu
                        orderRepository.save(updatedMatchingOrder);
                    }

                    // Nếu không còn lệnh ở mức giá này, xóa mức giá
                    if (ordersAtPrice.isEmpty()) {
                        oppositeOrdersByPrice.remove(price);
                    }

                    // Nếu đã khớp hết khối lượng, dừng lại
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }

                // Cập nhật lệnh hiện tại
                Order updatedOrder = order.toBuilder()
                        .dealVolume(order.getVolume().subtract(remainingVolume))
                        .status(remainingVolume.compareTo(BigDecimal.ZERO) <= 0 ?
                                OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                        .build();

                // Nếu lệnh chưa khớp hết và là lệnh giới hạn, thêm vào sổ lệnh
                if (updatedOrder.getStatus() == OrderStatus.PARTIALLY_FILLED && updatedOrder.getType() == OrderType.LIMIT) {
                    addOrderToOrderBook(updatedOrder);
                }

                // Lưu lệnh hiện tại vào cơ sở dữ liệu
                orderRepository.save(updatedOrder);

                // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
                if (!trades.isEmpty()) {
                    Trade lastTrade = trades.get(trades.size() - 1);
                    setLastPrice(lastTrade.getPrice());
                }

                return trades;
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_MATCH_ORDER_FIFO(), order.getId(), order.getSymbol(), e);
                return Collections.emptyList();
            } finally {
                orderBookLock.writeLock().unlock();
            }
        }

        /**
         * Tạo giao dịch từ hai lệnh
         * @param takerOrder Lệnh chủ động
         * @param makerOrder Lệnh bị động
         * @param matchedVolume Khối lượng khớp
         * @param matchPrice Giá khớp
         * @return Trade
         */
        private Trade createTrade(Order takerOrder, Order makerOrder, BigDecimal matchedVolume, Money matchPrice) {
            // Xác định lệnh mua và lệnh bán
            Order buyOrder = takerOrder.getDirection() == OrderDirection.BUY ? takerOrder : makerOrder;
            Order sellOrder = takerOrder.getDirection() == OrderDirection.SELL ? takerOrder : makerOrder;

            // Tính phí giao dịch
            Money buyFee = contract.calculateTakerFee(matchedVolume, matchPrice.getValue());
            Money sellFee = contract.calculateMakerFee(matchedVolume, matchPrice.getValue());

            // Nếu maker là lệnh mua, đổi phí
            if (makerOrder.getDirection() == OrderDirection.BUY) {
                Money temp = buyFee;
                buyFee = sellFee;
                sellFee = temp;
            }

            return Trade.builder()
                    .id(TradeId.of(System.currentTimeMillis()))
                    .symbol(takerOrder.getSymbol())
                    .buyOrderId(OrderId.of(buyOrder.getId()))
                    .sellOrderId(OrderId.of(sellOrder.getId()))
                    .buyMemberId(buyOrder.getMemberId())
                    .sellMemberId(sellOrder.getMemberId())
                    .price(matchPrice)
                    .volume(matchedVolume)
                    .buyFee(buyFee)
                    .sellFee(sellFee)
                    .tradeTime(LocalDateTime.now())
                    .buyOrderType(buyOrder.getType())
                    .sellOrderType(sellOrder.getType())
                    .leverage(takerOrder.getLeverage()) // ✅ FIX: Thêm leverage từ taker order
                    .build();
        }

        /**
         * Thêm lệnh vào sổ lệnh
         * @param order Lệnh giao dịch
         */
        private void addOrderToOrderBook(Order order) {
            // Thêm vào map orders
            orders.put(order.getOrderId(), order);

            // Thêm vào map ordersByPrice
            Map<Money, List<Order>> directionOrdersByPrice = ordersByPrice.get(order.getDirection());
            List<Order> ordersAtPrice = directionOrdersByPrice.computeIfAbsent(order.getPrice(), k -> new ArrayList<>());
            ordersAtPrice.add(order);
        }

        /**
         * Khớp lệnh theo thuật toán Pro-rata
         * @param order Lệnh giao dịch
         * @return List<Trade>
         */
        private List<Trade> matchOrderProRata(Order order) {
            if (order == null) {
                return Collections.emptyList();
            }

            List<Trade> trades = new ArrayList<>();

            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.writeLock().lock();

                // Xác định hướng đối ứng
                OrderDirection oppositeDirection = order.getDirection() == OrderDirection.BUY ?
                        OrderDirection.SELL : OrderDirection.BUY;

                // Lấy danh sách lệnh đối ứng theo giá
                Map<Money, List<Order>> oppositeOrdersByPrice = ordersByPrice.get(oppositeDirection);

                // Sắp xếp giá theo hướng đối ứng
                List<Money> sortedPrices = new ArrayList<>(oppositeOrdersByPrice.keySet());
                if (oppositeDirection == OrderDirection.SELL) {
                    // Nếu đối ứng là bán, sắp xếp giá tăng dần
                    sortedPrices.sort(Money::compareTo);
                } else {
                    // Nếu đối ứng là mua, sắp xếp giá giảm dần
                    sortedPrices.sort((p1, p2) -> p2.compareTo(p1));
                }

                // Khối lượng còn lại cần khớp
                BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());

                // Duyệt qua các mức giá
                for (Money price : sortedPrices) {
                    // Kiểm tra điều kiện giá
                    if (order.getType() == OrderType.LIMIT) {
                        if ((order.getDirection() == OrderDirection.BUY && price.compareTo(order.getPrice()) > 0) ||
                            (order.getDirection() == OrderDirection.SELL && price.compareTo(order.getPrice()) < 0)) {
                            // Nếu giá không phù hợp, bỏ qua
                            continue;
                        }
                    }

                    // Lấy danh sách lệnh ở mức giá này
                    List<Order> ordersAtPrice = oppositeOrdersByPrice.get(price);

                    // Tính tổng khối lượng có sẵn ở mức giá này
                    BigDecimal totalAvailableVolume = BigDecimal.ZERO;
                    for (Order matchingOrder : ordersAtPrice) {
                        BigDecimal availableVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());
                        totalAvailableVolume = totalAvailableVolume.add(availableVolume);
                    }

                    // Nếu không có khối lượng có sẵn, bỏ qua
                    if (totalAvailableVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }

                    // Khối lượng cần khớp ở mức giá này
                    BigDecimal volumeToMatch = remainingVolume.min(totalAvailableVolume);

                    // Danh sách lệnh đã cập nhật
                    List<Order> updatedOrders = new ArrayList<>();

                    // Phân bổ khối lượng khớp lệnh theo tỷ lệ
                    for (Order matchingOrder : ordersAtPrice) {
                        BigDecimal availableVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());

                        // Tính tỷ lệ khối lượng của lệnh này so với tổng khối lượng
                        BigDecimal ratio = availableVolume.divide(totalAvailableVolume, 8, RoundingMode.HALF_UP);

                        // Tính khối lượng khớp cho lệnh này
                        BigDecimal matchedVolume = volumeToMatch.multiply(ratio).setScale(8, RoundingMode.HALF_UP);

                        // Nếu khối lượng khớp quá nhỏ, bỏ qua
                        if (matchedVolume.compareTo(BigDecimal.valueOf(0.00000001)) <= 0) {
                            continue;
                        }

                        // Tạo giao dịch
                        Trade trade = createTrade(order, matchingOrder, matchedVolume, price);
                        trades.add(trade);

                        // Cập nhật lệnh đối ứng
                        Order updatedMatchingOrder = matchingOrder.toBuilder()
                                .dealVolume(matchingOrder.getDealVolume().add(matchedVolume))
                                .status(matchingOrder.getDealVolume().add(matchedVolume).compareTo(matchingOrder.getVolume()) >= 0 ?
                                        OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                                .build();

                        // Cập nhật trong sổ lệnh
                        orders.put(updatedMatchingOrder.getOrderId(), updatedMatchingOrder);

                        // Thêm vào danh sách lệnh đã cập nhật
                        updatedOrders.add(updatedMatchingOrder);

                        // Lưu lệnh đối ứng vào cơ sở dữ liệu
                        orderRepository.save(updatedMatchingOrder);
                    }

                    // Cập nhật khối lượng còn lại
                    remainingVolume = remainingVolume.subtract(volumeToMatch);

                    // Cập nhật danh sách lệnh ở mức giá này
                    ordersAtPrice.clear();
                    for (Order updatedOrder : updatedOrders) {
                        if (updatedOrder.getStatus() != OrderStatus.FILLED) {
                            ordersAtPrice.add(updatedOrder);
                        }
                    }

                    // Nếu không còn lệnh ở mức giá này, xóa mức giá
                    if (ordersAtPrice.isEmpty()) {
                        oppositeOrdersByPrice.remove(price);
                    }

                    // Nếu đã khớp hết khối lượng, dừng lại
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }

                // Cập nhật lệnh hiện tại
                Order updatedOrder = order.toBuilder()
                        .dealVolume(order.getVolume().subtract(remainingVolume))
                        .status(remainingVolume.compareTo(BigDecimal.ZERO) <= 0 ?
                                OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                        .build();

                // Nếu lệnh chưa khớp hết và là lệnh giới hạn, thêm vào sổ lệnh
                if (updatedOrder.getStatus() == OrderStatus.PARTIALLY_FILLED && updatedOrder.getType() == OrderType.LIMIT) {
                    addOrderToOrderBook(updatedOrder);
                }

                // Lưu lệnh hiện tại vào cơ sở dữ liệu
                orderRepository.save(updatedOrder);

                // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
                if (!trades.isEmpty()) {
                    Trade lastTrade = trades.get(trades.size() - 1);
                    setLastPrice(lastTrade.getPrice());
                }

                return trades;
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_MATCH_ORDER_PRO_RATA(), order.getId(), order.getSymbol(), e);
                return Collections.emptyList();
            } finally {
                orderBookLock.writeLock().unlock();
            }
        }

        /**
         * Khớp lệnh theo thuật toán Hybrid
         * @param order Lệnh giao dịch
         * @return List<Trade>
         */
        private List<Trade> matchOrderHybrid(Order order) {
            if (order == null) {
                return Collections.emptyList();
            }

            // Thuật toán Hybrid kết hợp FIFO và Pro-rata
            // Đầu tiên, khớp một phần theo FIFO (ví dụ: 50% khối lượng)
            // Sau đó, khớp phần còn lại theo Pro-rata

            List<Trade> trades = new ArrayList<>();

            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.writeLock().lock();

                // Xác định hướng đối ứng
                OrderDirection oppositeDirection = order.getDirection() == OrderDirection.BUY ?
                        OrderDirection.SELL : OrderDirection.BUY;

                // Lấy danh sách lệnh đối ứng theo giá
                Map<Money, List<Order>> oppositeOrdersByPrice = ordersByPrice.get(oppositeDirection);

                // Sắp xếp giá theo hướng đối ứng
                List<Money> sortedPrices = new ArrayList<>(oppositeOrdersByPrice.keySet());
                if (oppositeDirection == OrderDirection.SELL) {
                    // Nếu đối ứng là bán, sắp xếp giá tăng dần
                    sortedPrices.sort(Money::compareTo);
                } else {
                    // Nếu đối ứng là mua, sắp xếp giá giảm dần
                    sortedPrices.sort((p1, p2) -> p2.compareTo(p1));
                }

                // Khối lượng còn lại cần khớp
                BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());

                // Tỷ lệ FIFO (50%)
                BigDecimal fifoRatio = new BigDecimal("0.5");

                // Khối lượng cần khớp theo FIFO
                BigDecimal fifoVolume = remainingVolume.multiply(fifoRatio);

                // Khớp lệnh theo FIFO cho phần đầu tiên
                for (Money price : sortedPrices) {
                    // Kiểm tra điều kiện giá
                    if (order.getType() == OrderType.LIMIT) {
                        if ((order.getDirection() == OrderDirection.BUY && price.compareTo(order.getPrice()) > 0) ||
                            (order.getDirection() == OrderDirection.SELL && price.compareTo(order.getPrice()) < 0)) {
                            // Nếu giá không phù hợp, bỏ qua
                            continue;
                        }
                    }

                    // Lấy danh sách lệnh ở mức giá này
                    List<Order> ordersAtPrice = oppositeOrdersByPrice.get(price);

                    // Duyệt qua các lệnh theo thứ tự thời gian (FIFO)
                    Iterator<Order> iterator = ordersAtPrice.iterator();
                    while (iterator.hasNext() && fifoVolume.compareTo(BigDecimal.ZERO) > 0) {
                        Order matchingOrder = iterator.next();

                        // Khối lượng còn lại của lệnh đối ứng
                        BigDecimal matchingRemainingVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());

                        // Khối lượng khớp
                        BigDecimal matchedVolume = fifoVolume.min(matchingRemainingVolume);

                        // Tạo giao dịch
                        Trade trade = createTrade(order, matchingOrder, matchedVolume, price);
                        trades.add(trade);

                        // Cập nhật khối lượng đã khớp
                        fifoVolume = fifoVolume.subtract(matchedVolume);
                        remainingVolume = remainingVolume.subtract(matchedVolume);

                        // Cập nhật lệnh đối ứng
                        Order updatedMatchingOrder = matchingOrder.toBuilder()
                                .dealVolume(matchingOrder.getDealVolume().add(matchedVolume))
                                .status(matchingOrder.getDealVolume().add(matchedVolume).compareTo(matchingOrder.getVolume()) >= 0 ?
                                        OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                                .build();

                        // Cập nhật trong sổ lệnh
                        orders.put(updatedMatchingOrder.getOrderId(), updatedMatchingOrder);

                        // Nếu lệnh đối ứng đã khớp hết, xóa khỏi danh sách
                        if (updatedMatchingOrder.getStatus() == OrderStatus.FILLED) {
                            iterator.remove();
                        }

                        // Lưu lệnh đối ứng vào cơ sở dữ liệu
                        orderRepository.save(updatedMatchingOrder);
                    }

                    // Nếu không còn lệnh ở mức giá này, xóa mức giá
                    if (ordersAtPrice.isEmpty()) {
                        oppositeOrdersByPrice.remove(price);
                    }

                    // Nếu đã khớp hết khối lượng, dừng lại
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }

                // Nếu còn khối lượng cần khớp, tiếp tục khớp theo Pro-rata
                if (remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
                    // Khớp lệnh theo Pro-rata cho phần còn lại
                    for (Money price : sortedPrices) {
                        // Kiểm tra điều kiện giá
                        if (order.getType() == OrderType.LIMIT) {
                            if ((order.getDirection() == OrderDirection.BUY && price.compareTo(order.getPrice()) > 0) ||
                                (order.getDirection() == OrderDirection.SELL && price.compareTo(order.getPrice()) < 0)) {
                                // Nếu giá không phù hợp, bỏ qua
                                continue;
                            }
                        }

                        // Lấy danh sách lệnh ở mức giá này
                        List<Order> ordersAtPrice = oppositeOrdersByPrice.get(price);

                        // Tính tổng khối lượng có sẵn ở mức giá này
                        BigDecimal totalAvailableVolume = BigDecimal.ZERO;
                        for (Order matchingOrder : ordersAtPrice) {
                            BigDecimal availableVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());
                            totalAvailableVolume = totalAvailableVolume.add(availableVolume);
                        }

                        // Nếu không có khối lượng có sẵn, bỏ qua
                        if (totalAvailableVolume.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        // Khối lượng cần khớp ở mức giá này
                        BigDecimal volumeToMatch = remainingVolume.min(totalAvailableVolume);

                        // Danh sách lệnh đã cập nhật
                        List<Order> updatedOrders = new ArrayList<>();

                        // Phân bổ khối lượng khớp lệnh theo tỷ lệ
                        for (Order matchingOrder : ordersAtPrice) {
                            BigDecimal availableVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());

                            // Tính tỷ lệ khối lượng của lệnh này so với tổng khối lượng
                            BigDecimal ratio = availableVolume.divide(totalAvailableVolume, 8, RoundingMode.HALF_UP);

                            // Tính khối lượng khớp cho lệnh này
                            BigDecimal matchedVolume = volumeToMatch.multiply(ratio).setScale(8, RoundingMode.HALF_UP);

                            // Nếu khối lượng khớp quá nhỏ, bỏ qua
                            if (matchedVolume.compareTo(BigDecimal.valueOf(0.00000001)) <= 0) {
                                continue;
                            }

                            // Tạo giao dịch
                            Trade trade = createTrade(order, matchingOrder, matchedVolume, price);
                            trades.add(trade);

                            // Cập nhật lệnh đối ứng
                            Order updatedMatchingOrder = matchingOrder.toBuilder()
                                    .dealVolume(matchingOrder.getDealVolume().add(matchedVolume))
                                    .status(matchingOrder.getDealVolume().add(matchedVolume).compareTo(matchingOrder.getVolume()) >= 0 ?
                                            OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                                    .build();

                            // Cập nhật trong sổ lệnh
                            orders.put(updatedMatchingOrder.getOrderId(), updatedMatchingOrder);

                            // Thêm vào danh sách lệnh đã cập nhật
                            updatedOrders.add(updatedMatchingOrder);

                            // Lưu lệnh đối ứng vào cơ sở dữ liệu
                            orderRepository.save(updatedMatchingOrder);

                            // Cập nhật khối lượng còn lại
                            remainingVolume = remainingVolume.subtract(matchedVolume);
                        }

                        // Cập nhật danh sách lệnh ở mức giá này
                        ordersAtPrice.clear();
                        for (Order updatedOrder : updatedOrders) {
                            if (updatedOrder.getStatus() != OrderStatus.FILLED) {
                                ordersAtPrice.add(updatedOrder);
                            }
                        }

                        // Nếu không còn lệnh ở mức giá này, xóa mức giá
                        if (ordersAtPrice.isEmpty()) {
                            oppositeOrdersByPrice.remove(price);
                        }

                        // Nếu đã khớp hết khối lượng, dừng lại
                        if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                }

                // Cập nhật lệnh hiện tại
                Order updatedOrder = order.toBuilder()
                        .dealVolume(order.getVolume().subtract(remainingVolume))
                        .status(remainingVolume.compareTo(BigDecimal.ZERO) <= 0 ?
                                OrderStatus.FILLED : OrderStatus.PARTIALLY_FILLED)
                        .build();

                // Nếu lệnh chưa khớp hết và là lệnh giới hạn, thêm vào sổ lệnh
                if (updatedOrder.getStatus() == OrderStatus.PARTIALLY_FILLED && updatedOrder.getType() == OrderType.LIMIT) {
                    addOrderToOrderBook(updatedOrder);
                }

                // Lưu lệnh hiện tại vào cơ sở dữ liệu
                orderRepository.save(updatedOrder);

                // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
                if (!trades.isEmpty()) {
                    Trade lastTrade = trades.get(trades.size() - 1);
                    setLastPrice(lastTrade.getPrice());
                }

                return trades;
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_MATCH_ORDER_HYBRID(), order.getId(), order.getSymbol(), e);
                return Collections.emptyList();
            } finally {
                orderBookLock.writeLock().unlock();
            }
        }

        /**
         * Hủy lệnh
         * @param order Lệnh giao dịch
         * @return boolean
         */
        public boolean cancelOrder(Order order) {
            if (order == null || !order.getSymbol().equals(symbol)) {
                log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
                return false;
            }

            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.writeLock().lock();

                // Xóa lệnh khỏi sổ lệnh
                Order existingOrder = orders.remove(order.getOrderId());
                if (existingOrder == null) {
                    // Lệnh không tồn tại trong sổ lệnh
                    return false;
                }

                // Xóa lệnh khỏi danh sách lệnh theo giá
                Map<Money, List<Order>> directionOrdersByPrice = ordersByPrice.get(existingOrder.getDirection());
                List<Order> ordersAtPrice = directionOrdersByPrice.get(existingOrder.getPrice());
                if (ordersAtPrice != null) {
                    ordersAtPrice.remove(existingOrder);

                    // Nếu không còn lệnh ở mức giá này, xóa mức giá
                    if (ordersAtPrice.isEmpty()) {
                        directionOrdersByPrice.remove(existingOrder.getPrice());
                    }
                }

                // Xóa lệnh khỏi danh sách lệnh chờ nếu là lệnh chờ
                triggerOrders.remove(existingOrder);

                log.info(LogMessages.OrderMatching.INFO_ORDER_CANCELED(), order.getOrderId(), symbol);

                return true;
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_CANCEL_ORDER(), order.getOrderId(), symbol, e);
                return false;
            } finally {
                orderBookLock.writeLock().unlock();
            }
        }

        /**
         * Kiểm tra lệnh chờ
         */
        public void checkTriggerOrders() {
            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.readLock().lock();

                // Lấy giá hiện tại
                Money currentPrice = getLastPrice();
                if (currentPrice == null) {
                    return;
                }

                // Danh sách lệnh cần kích hoạt
                List<Order> ordersToTrigger = new ArrayList<>();

                // Kiểm tra các lệnh chờ
                for (Order order : triggerOrders) {
                    // Kiểm tra điều kiện kích hoạt
                    boolean shouldTrigger = false;

                    if (order.getTriggerPrice() != null) {
                        if (order.getDirection() == OrderDirection.BUY) {
                            // Lệnh mua: kích hoạt khi giá <= giá kích hoạt
                            shouldTrigger = lastPrice.compareTo(order.getTriggerPrice()) <= 0;
                        } else {
                            // Lệnh bán: kích hoạt khi giá >= giá kích hoạt
                            shouldTrigger = lastPrice.compareTo(order.getTriggerPrice()) >= 0;
                        }
                    }

                    // Nếu cần kích hoạt, thêm vào danh sách
                    if (shouldTrigger) {
                        ordersToTrigger.add(order);
                    }
                }

                // Xóa các lệnh đã kích hoạt khỏi danh sách lệnh chờ
                triggerOrders.removeAll(ordersToTrigger);

                // Giải phóng khóa để tránh deadlock
                orderBookLock.readLock().unlock();

                // Kích hoạt các lệnh
                for (Order order : ordersToTrigger) {
                    // Tạo lệnh mới từ lệnh chờ
                    Order newOrder = order.toBuilder()
                            .status(OrderStatus.NEW)
                            .executeTime(LocalDateTime.now())
                            .build();

                    // Lưu lệnh mới vào cơ sở dữ liệu
                    orderRepository.save(newOrder);

                    // Khớp lệnh
                    matchOrder(newOrder);
                }
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_CHECK_TRIGGER_ORDERS(), symbol, e);
            } finally {
                // Đảm bảo giải phóng khóa
                orderBookLock.readLock().unlock();
            }
        }

        /**
         * Kiểm tra thanh lý
         */
        public void checkLiquidations() {
            try {
                // Lấy khóa đọc-ghi cho sổ lệnh
                orderBookLock.readLock().lock();

                // Lấy giá hiện tại
                Money currentPrice = getLastPrice();
                if (currentPrice == null) {
                    return;
                }

                // Danh sách vị thế cần thanh lý
                List<Position> positionsToLiquidate = new ArrayList<>();

                // Kiểm tra các vị thế
                for (Position position : positionsToCheck.values()) {
                    // Tính toán giá thanh lý
                    Money liquidationPrice = calculateLiquidationPrice(position);

                    // Kiểm tra điều kiện thanh lý
                    boolean shouldLiquidate = false;

                    if (position.getDirection() == PositionDirection.LONG) {
                        // Vị thế mua: thanh lý khi giá <= giá thanh lý
                        shouldLiquidate = lastPrice.compareTo(liquidationPrice) <= 0;
                    } else {
                        // Vị thế bán: thanh lý khi giá >= giá thanh lý
                        shouldLiquidate = lastPrice.compareTo(liquidationPrice) >= 0;
                    }

                    // Nếu cần thanh lý, thêm vào danh sách
                    if (shouldLiquidate) {
                        positionsToLiquidate.add(position);
                    }
                }

                // Xóa các vị thế đã thanh lý khỏi danh sách vị thế cần kiểm tra
                for (Position position : positionsToLiquidate) {
                    positionsToCheck.remove(position.getMemberId());
                }

                // Giải phóng khóa để tránh deadlock
                orderBookLock.readLock().unlock();

                // Thanh lý các vị thế
                for (Position position : positionsToLiquidate) {
                    // Tạo lệnh thanh lý
                    Order liquidationOrder = createLiquidationOrder(position, lastPrice);

                    // Lưu lệnh thanh lý vào cơ sở dữ liệu
                    orderRepository.save(liquidationOrder);

                    // Khớp lệnh
                    matchOrder(liquidationOrder);
                }
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_CHECK_LIQUIDATIONS(), symbol, e);
            } finally {
                // Đảm bảo giải phóng khóa
                orderBookLock.readLock().unlock();
            }
        }

        /**
         * Tính toán giá thanh lý
         * @param position Vị thế
         * @return Giá thanh lý
         */
        private Money calculateLiquidationPrice(Position position) {
            // Sử dụng phương thức có sẵn trong Position để tính toán giá thanh lý
            return position.calculateLiquidationPrice(contract.getMaintenanceMarginRate().getValue());
        }

        /**
         * Tạo lệnh thanh lý
         * @param position Vị thế
         * @param lastPrice Giá hiện tại
         * @return Lệnh thanh lý
         */
        private Order createLiquidationOrder(Position position, Money lastPrice) {
            // Hướng đối ứng
            OrderDirection direction = position.getDirection() == PositionDirection.LONG ?
                    OrderDirection.SELL : OrderDirection.BUY;

            // Tạo lệnh thanh lý
            return Order.builder()
                    .orderId(OrderId.of("LIQ" + System.currentTimeMillis()))
                    .memberId(position.getMemberId())
                    .contractId(contract.getId())
                    .symbol(position.getSymbol())
                    .coinSymbol(contract.getBaseCoin())
                    .baseSymbol(contract.getQuoteCoin())
                    .direction(direction)
                    .type(OrderType.MARKET)
                    .price(lastPrice)
                    .volume(position.getVolume())
                    .dealVolume(BigDecimal.ZERO)
                    .dealMoney(Money.ZERO)
                    .fee(Money.ZERO)
                    .status(OrderStatus.NEW)
                    .createTime(LocalDateTime.now())
                    .executeTime(LocalDateTime.now())
                    .timeInForce(com.icetea.lotus.core.domain.valueobject.TimeInForce.IOC)
                    .leverage(position.getLeverage())
                    .reduceOnly(true)
                    .liquidation(true)
                    .adl(false)
                    .implied(false)
                    .build();
        }

        /**
         * Thanh lý vị thế
         * @param position Vị thế cần thanh lý
         * @return List<Trade>
         */
        public List<Trade> liquidatePosition(Position position) {
            // Kiểm tra xem vị thế có thuộc về symbol này không
            if (position == null || !position.getSymbol().equals(symbol)) {
                log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
                return Collections.emptyList();
            }

            try {
                // Tạo lệnh thanh lý
                Order liquidationOrder = createLiquidationOrder(position, lastPrice);

                // Lưu lệnh thanh lý vào cơ sở dữ liệu
                Order savedOrder = orderRepository.save(liquidationOrder);

                // Khớp lệnh
                List<Trade> trades = matchOrder(savedOrder);

                // Lưu các giao dịch vào cơ sở dữ liệu
                if (!trades.isEmpty()) {
                    tradeRepository.saveAll(trades);
                }

                log.info(LogMessages.OrderMatching.INFO_POSITION_LIQUIDATED(), position.getId(), symbol);

                return trades;
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_LIQUIDATE_POSITION(), position.getId(), symbol, e);
                return Collections.emptyList();
            }
        }

        /**
         * Dừng và giải phóng tài nguyên
         */
        public void shutdown() {
            log.info(LogMessages.OrderMatching.INFO_ENGINE_SHUTDOWN(), symbol);

            try {
                // Dừng giao dịch
                tradingHalt = true;

                // Xóa tất cả lệnh
                orders.clear();
                ordersByPrice.get(OrderDirection.BUY).clear();
                ordersByPrice.get(OrderDirection.SELL).clear();
                triggerOrders.clear();
                positionsToCheck.clear();

                log.info(LogMessages.OrderMatching.INFO_ENGINE_SHUTDOWN(), symbol);
            } catch (Exception e) {
                log.error(LogMessages.OrderMatching.ERROR_INIT_ENGINE(), symbol, e);
            }
        }

        /**
         * Lấy sổ lệnh
         * @return OrderBook
         */
        public com.icetea.lotus.core.domain.service.OrderBook getOrderBook() {
            // Tạo danh sách các mức giá mua (bids)
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> bids = new ArrayList<>();
            Map<Money, List<Order>> buyOrders = ordersByPrice.get(OrderDirection.BUY);
            for (Map.Entry<Money, List<Order>> entry : buyOrders.entrySet()) {
                Money price = entry.getKey();
                List<Order> ordersAtPrice = entry.getValue();

                // Tính tổng khối lượng ở mức giá này
                BigDecimal totalVolume = BigDecimal.ZERO;
                for (Order order : ordersAtPrice) {
                    BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());
                    totalVolume = totalVolume.add(remainingVolume);
                }

                // Tạo entry cho mức giá này
                com.icetea.lotus.core.domain.service.OrderBook.PriceLevel priceLevel =
                    com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                        .price(price)
                        .volume(totalVolume)
                        .orders(new ArrayList<>())
                        .build();

                bids.add(priceLevel);
            }

            // Tạo danh sách các mức giá bán (asks)
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> asks = new ArrayList<>();
            Map<Money, List<Order>> sellOrders = ordersByPrice.get(OrderDirection.SELL);
            for (Map.Entry<Money, List<Order>> entry : sellOrders.entrySet()) {
                Money price = entry.getKey();
                List<Order> ordersAtPrice = entry.getValue();

                // Tính tổng khối lượng ở mức giá này
                BigDecimal totalVolume = BigDecimal.ZERO;
                for (Order order : ordersAtPrice) {
                    BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());
                    totalVolume = totalVolume.add(remainingVolume);
                }

                // Tạo entry cho mức giá này
                com.icetea.lotus.core.domain.service.OrderBook.PriceLevel priceLevel =
                    com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                        .price(price)
                        .volume(totalVolume)
                        .orders(new ArrayList<>())
                        .build();

                asks.add(priceLevel);
            }

            // Tạo sổ lệnh
            return com.icetea.lotus.core.domain.service.OrderBook.builder()
                    .symbol(symbol)
                    .bids(bids)
                    .asks(asks)
                    .lastPrice(lastPrice)
                    .markPrice(markPrice)
                    .indexPrice(indexPrice)
                    .build();
        }

        // Getters and setters

        public boolean isTradingHalt() {
            return tradingHalt;
        }

        public void setTradingHalt(boolean tradingHalt) {
            this.tradingHalt = tradingHalt;
        }

        public Money getMarkPrice() {
            return markPrice;
        }

        public void setMarkPrice(Money markPrice) {
            this.markPrice = markPrice;
        }

        public Money getIndexPrice() {
            return indexPrice;
        }

        public void setIndexPrice(Money indexPrice) {
            this.indexPrice = indexPrice;
        }

        public Money getLastPrice() {
            return lastPrice;
        }

        public void setLastPrice(Money lastPrice) {
            this.lastPrice = lastPrice;
        }

        public MatchingAlgorithm getMatchingAlgorithm() {
            return matchingAlgorithm;
        }

        public void setMatchingAlgorithm(MatchingAlgorithm matchingAlgorithm) {
            this.matchingAlgorithm = matchingAlgorithm;
        }

        /**
         * Thêm vị thế vào danh sách kiểm tra thanh lý
         * @param position Vị thế cần thêm
         */
        public void addPositionToCheck(Position position) {
            if (position == null || position.getId() == null) {
                return;
            }

            positionsToCheck.put(position.getId(), position);
            log.debug("Đã thêm vị thế vào danh sách kiểm tra thanh lý trong engine, positionId = {}, symbol = {}",
                    position.getId(), symbol);
        }

        /**
         * Xóa vị thế khỏi danh sách kiểm tra thanh lý
         * @param positionId ID của vị thế cần xóa
         */
        public void removePositionToCheck(PositionId positionId) {
            if (positionId == null) {
                return;
            }

            positionsToCheck.remove(positionId);
            log.debug("Đã xóa vị thế khỏi danh sách kiểm tra thanh lý trong engine, positionId = {}, symbol = {}",
                    positionId, symbol);
        }
    }

    /**
     * Thêm vị thế vào danh sách kiểm tra thanh lý
     * @param position Vị thế cần thêm
     */
    @Override
    public void addPositionToCheck(Position position) {
        if (position == null || position.getSymbol() == null) {
            log.warn("Vị thế không hợp lệ");
            return;
        }

        try {
            Symbol symbol = position.getSymbol();

            // Thêm vị thế vào danh sách kiểm tra thanh lý
            positionsToCheck.put(position.getId(), position);

            // Thêm vị thế vào danh sách theo symbol
            addPositionToCheck(symbol, position);

            // Thêm vị thế vào matching engine
            MatchingEngine engine = getMatchingEngine(symbol);
            if (engine != null) {
                engine.addPositionToCheck(position);
                log.debug("Đã thêm vị thế vào danh sách kiểm tra thanh lý, positionId = {}, symbol = {}",
                        position.getId(), symbol);
            } else {
                log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
            }
        } catch (Exception e) {
            log.error("Lỗi khi thêm vị thế vào danh sách kiểm tra thanh lý, positionId = {}, symbol = {}",
                    position.getId(), position.getSymbol(), e);
        }
    }

    /**
     * Xóa vị thế khỏi danh sách kiểm tra thanh lý
     * @param positionId ID của vị thế cần xóa
     */
    @Override
    public void removePositionToCheck(PositionId positionId) {
        if (positionId == null) {
            return;
        }

        try {
            // Xóa vị thế khỏi danh sách kiểm tra thanh lý
            Position position = positionsToCheck.remove(positionId);

            if (position != null) {
                // Xóa vị thế khỏi danh sách theo symbol
                removePositionToCheck(position.getSymbol(), position);

                // Xóa vị thế khỏi matching engine
                Symbol symbol = position.getSymbol();
                MatchingEngine engine = getMatchingEngine(symbol);
                if (engine != null) {
                    engine.removePositionToCheck(positionId);
                }
            } else {
                // Nếu không tìm thấy vị thế trong cache, xóa khỏi tất cả các engine
                for (MatchingEngine engine : matchingEngines.values()) {
                    engine.removePositionToCheck(positionId);
                }
            }

            log.debug("Đã xóa vị thế khỏi danh sách kiểm tra thanh lý, positionId = {}", positionId);
        } catch (Exception e) {
            log.error("Lỗi khi xóa vị thế khỏi danh sách kiểm tra thanh lý, positionId = {}", positionId, e);
        }
    }

    /**
     * Lấy optimized matching engine cho một symbol
     * @param symbol Symbol của hợp đồng
     * @return OptimizedMatchingEngine
     */
//    private OptimizedMatchingEngine getOptimizedMatchingEngine(Symbol symbol) {
//        if (symbol == null) {
//            return null;
//        }
//
//        String symbolStr = symbol.getValue();
//        OptimizedMatchingEngine engine = optimizedMatchingEngines.get(symbolStr);
//
//        if (engine == null) {
//            try {
//                lock.writeLock().lock();
//
//                // Kiểm tra lại sau khi lấy lock
//                engine = optimizedMatchingEngines.get(symbolStr);
//                if (engine == null) {
//                    // Tìm hợp đồng
//                    Contract contract = contractRepository.findBySymbol(symbol);
//
//                    if (contract == null) {
//                        log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
//                        return null;
//                    }
//
//                    // Sử dụng optimizedMatchingEngine đã được inject
//                    engine = optimizedMatchingEngine;
//                    optimizedMatchingEngines.put(symbolStr, engine);
//
//                    log.info(LogMessages.OrderMatching.INFO_ENGINE_INITIALIZED(), symbol);
//                }
//            } finally {
//                lock.writeLock().unlock();
//            }
//        }
//
//        return engine;
//    }

    /**
     * Lấy lock-free matching engine cho một symbol
     * @param symbol Symbol của hợp đồng
     * @return LockFreeMatchingEngine
     */
    private LockFreeMatchingEngine getLockFreeMatchingEngine(Symbol symbol) {
        if (symbol == null) {
            return null;
        }

        String symbolStr = symbol.getValue();
//        log.info("getLockFreeMatchingEngine: {}", symbolStr);
        LockFreeMatchingEngine engine = lockFreeMatchingEngines.get(symbolStr);
        if (engine == null) {
            log.info("getLockFreeMatchingEngine engine IS NULL");
            try {
                lock.writeLock().lock();

                // Kiểm tra lại sau khi lấy lock
                engine = lockFreeMatchingEngines.get(symbolStr);
                if (engine == null) {
                    // Tìm hợp đồng
                    Contract contract = contractRepository.findBySymbol(symbol);
                    log.info("getLockFreeMatchingEngine contract: {}", contract.toString());
                    if (contract == null) {
                        log.warn(LogMessages.OrderMatching.WARN_ENGINE_NOT_FOUND(), symbol);
                        return null;
                    }

                    // Tạo lock-free matching engine mới
                    engine = new LockFreeMatchingEngine(symbol, contract);
                    log.info("getLockFreeMatchingEngine new engine: {}", engine.toString());
                    lockFreeMatchingEngines.put(symbolStr, engine);

                    log.info(LogMessages.OrderMatching.INFO_ENGINE_INITIALIZED(), symbol);
                }
            }catch (Exception e){
                log.error("getLockFreeMatchingEngine ERROR", e.getMessage());
            }
            finally {
                lock.writeLock().unlock();
            }
        }

        return engine;
    }

    // Các phương thức từ OrderMatchingExtensionService

    /**
     * Cập nhật tỷ lệ tài trợ
     * @param symbol Symbol của hợp đồng
     * @param fundingRate Tỷ lệ tài trợ
     */
    @Override
    public void updateFundingRate(Symbol symbol, Money fundingRate) {
        if (symbol == null || fundingRate == null) {
            log.warn("Symbol hoặc fundingRate không hợp lệ");
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Cập nhật tỷ lệ tài trợ
            fundingRateMap.put(symbolStr, fundingRate);

            // Xử lý các tác động của việc thay đổi tỷ lệ tài trợ
            processFundingRateUpdate(symbol);

            log.debug("Đã cập nhật tỷ lệ tài trợ cho symbol {}, fundingRate = {}", symbol, fundingRate);
        } catch (Exception e) {
            log.error("Lỗi khi cập nhật tỷ lệ tài trợ, symbol = {}, fundingRate = {}", symbol, fundingRate, e);
        }
    }

    /**
     * Xử lý các tác động của việc thay đổi tỷ lệ tài trợ
     * @param symbol Symbol của hợp đồng
     */
    private void processFundingRateUpdate(Symbol symbol) {
        // Triển khai logic xử lý tác động của việc thay đổi tỷ lệ tài trợ
        // Ví dụ: Tính toán lại giá đánh dấu, kiểm tra thanh lý, v.v.
    }

    /**
     * Cập nhật thời gian tài trợ tiếp theo
     * @param symbol Symbol của hợp đồng
     * @param nextFundingTime Thời gian tài trợ tiếp theo
     */
    @Override
    public void updateNextFundingTime(Symbol symbol, LocalDateTime nextFundingTime) {
        if (symbol == null || nextFundingTime == null) {
            log.warn("Symbol hoặc nextFundingTime không hợp lệ");
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Cập nhật thời gian tài trợ tiếp theo
            nextFundingTimeMap.put(symbolStr, nextFundingTime);

            log.debug("Đã cập nhật thời gian tài trợ tiếp theo cho symbol {}, nextFundingTime = {}", symbol, nextFundingTime);
        } catch (Exception e) {
            log.error("Lỗi khi cập nhật thời gian tài trợ tiếp theo, symbol = {}, nextFundingTime = {}", symbol, nextFundingTime, e);
        }
    }

    /**
     * Lấy tỷ lệ tài trợ
     * @param symbol Symbol của hợp đồng
     * @return Tỷ lệ tài trợ
     */
    @Override
    public Money getFundingRate(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Money.ZERO;
        }

        String symbolStr = symbol.getValue();
        return fundingRateMap.getOrDefault(symbolStr, Money.ZERO);
    }

    /**
     * Lấy thời gian tài trợ tiếp theo
     * @param symbol Symbol của hợp đồng
     * @return Thời gian tài trợ tiếp theo
     */
    @Override
    public LocalDateTime getNextFundingTime(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return LocalDateTime.now();
        }

        String symbolStr = symbol.getValue();
        return nextFundingTimeMap.getOrDefault(symbolStr, LocalDateTime.now());
    }

    /**
     * Thêm lệnh chờ
     * @param symbol Symbol của hợp đồng
     * @param order Lệnh chờ
     */
    @Override
    public void addTriggerOrder(Symbol symbol, Order order) {
        if (symbol == null || order == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Lấy danh sách lệnh chờ hiện tại
            List<Order> triggerOrders = triggerOrdersMap.computeIfAbsent(symbolStr, k -> new ArrayList<>());

            // Thêm lệnh chờ vào danh sách
            synchronized (triggerOrders) {
                triggerOrders.add(order);
            }

            log.debug("Đã thêm lệnh chờ cho symbol {}, order = {}", symbol, order.getId());
        } catch (Exception e) {
            log.error("Lỗi khi thêm lệnh chờ, symbol = {}, order = {}", symbol, order.getId(), e);
        }
    }

    /**
     * Xóa lệnh chờ
     * @param symbol Symbol của hợp đồng
     * @param order Lệnh chờ
     */
    @Override
    public void removeTriggerOrder(Symbol symbol, Order order) {
        if (symbol == null || order == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Lấy danh sách lệnh chờ hiện tại
            List<Order> triggerOrders = triggerOrdersMap.get(symbolStr);
            if (triggerOrders == null) {
                return;
            }

            // Xóa lệnh chờ khỏi danh sách
            synchronized (triggerOrders) {
                Iterator<Order> iterator = triggerOrders.iterator();
                while (iterator.hasNext()) {
                    Order triggerOrder = iterator.next();
                    if (triggerOrder.getId().equals(order.getId())) {
                        iterator.remove();
                        log.debug("Đã xóa lệnh chờ cho symbol {}, order = {}", symbol, order.getId());
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Lỗi khi xóa lệnh chờ, symbol = {}, order = {}", symbol, order.getId(), e);
        }
    }

    /**
     * Lấy danh sách lệnh chờ
     * @param symbol Symbol của hợp đồng
     * @return Danh sách lệnh chờ
     */
    @Override
    public List<Order> getTriggerOrders(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Collections.emptyList();
        }

        String symbolStr = symbol.getValue();
        List<Order> triggerOrders = triggerOrdersMap.getOrDefault(symbolStr, Collections.emptyList());

        synchronized (triggerOrders) {
            return new ArrayList<>(triggerOrders);
        }
    }

    /**
     * Thêm vị thế cần kiểm tra thanh lý
     * @param symbol Symbol của hợp đồng
     * @param position Vị thế
     */
    @Override
    public void addPositionToCheck(Symbol symbol, Position position) {
        if (symbol == null || position == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_POSITION());
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Lấy map vị thế cần kiểm tra hiện tại
            Map<Long, Position> positions = positionsToCheckMap.computeIfAbsent(symbolStr, k -> new ConcurrentHashMap<>());

            // Thêm vị thế vào map
            positions.put(position.getMemberId(), position);

            log.debug("Đã thêm vị thế cần kiểm tra thanh lý cho symbol {}, position = {}", symbol, position.getId());
        } catch (Exception e) {
            log.error("Lỗi khi thêm vị thế cần kiểm tra thanh lý, symbol = {}, position = {}", symbol, position.getId(), e);
        }
    }

    /**
     * Xóa vị thế khỏi danh sách kiểm tra thanh lý
     * @param symbol Symbol của hợp đồng
     * @param position Vị thế
     */
    @Override
    public void removePositionToCheck(Symbol symbol, Position position) {
        if (symbol == null || position == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_POSITION());
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            // Lấy map vị thế cần kiểm tra hiện tại
            Map<Long, Position> positions = positionsToCheckMap.get(symbolStr);
            if (positions == null) {
                return;
            }

            // Xóa vị thế khỏi map
            positions.remove(position.getMemberId());

            log.debug("Đã xóa vị thế khỏi danh sách kiểm tra thanh lý cho symbol {}, position = {}", symbol, position.getId());
        } catch (Exception e) {
            log.error("Lỗi khi xóa vị thế khỏi danh sách kiểm tra thanh lý, symbol = {}, position = {}", symbol, position.getId(), e);
        }
    }

    /**
     * Lấy danh sách vị thế cần kiểm tra thanh lý
     * @param symbol Symbol của hợp đồng
     * @return Danh sách vị thế
     */
    @Override
    public List<Position> getPositionsToCheck(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return Collections.emptyList();
        }

        String symbolStr = symbol.getValue();
        Map<Long, Position> positions = positionsToCheckMap.getOrDefault(symbolStr, Collections.emptyMap());

        return new ArrayList<>(positions.values());
    }

    /**
     * Xóa tất cả dữ liệu cho một symbol
     * @param symbol Symbol của hợp đồng
     */
    @Override
    public void clear(Symbol symbol) {
        if (symbol == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_SYMBOL());
            return;
        }

        String symbolStr = symbol.getValue();

        try {
            markPriceCache.remove(symbolStr);
            indexPriceCache.remove(symbolStr);
            fundingRateMap.remove(symbolStr);
            nextFundingTimeMap.remove(symbolStr);
            triggerOrdersMap.remove(symbolStr);
            positionsToCheckMap.remove(symbolStr);
            matchingEngines.remove(symbolStr);
            optimizedMatchingEngines.remove(symbolStr);
            lockFreeMatchingEngines.remove(symbolStr);
            orderBooks.remove(symbol);

            log.info("Đã xóa tất cả dữ liệu cho symbol {}", symbol);
        } catch (Exception e) {
            log.error("Lỗi khi xóa tất cả dữ liệu cho symbol {}", symbol, e);
        }
    }

    // Các phương thức từ OrderMatchingService

    /**
     * Khớp lệnh mới với các lệnh hiện có
     * @param newOrder Lệnh mới
     * @param existingOrders Danh sách các lệnh hiện có
     * @param contract Hợp đồng
     * @return Danh sách các lệnh đã được khớp
     */
    @Override
    public List<Order> matchOrder(Order newOrder, List<Order> existingOrders, Contract contract) {
        if (newOrder == null || existingOrders == null || contract == null) {
            log.warn(LogMessages.OrderMatching.WARN_INVALID_ORDER());
            return Collections.emptyList();
        }

        try {
            // Nếu lệnh mới là lệnh thị trường, khớp ngay lập tức
            if (newOrder.getType() == OrderType.MARKET) {
                return matchMarketOrder(newOrder, existingOrders, contract);
            }

            // Nếu lệnh mới là lệnh giới hạn, khớp với các lệnh đối ứng
            List<Order> matchingOrders = getMatchingOrders(newOrder, existingOrders);
            List<Order> matchedOrders = new ArrayList<>();

            BigDecimal remainingVolume = newOrder.getVolume().subtract(newOrder.getDealVolume());

            log.debug("Tìm thấy {} lệnh đối ứng, remainingVolume = {}", matchingOrders.size(), remainingVolume);

            for (Order matchingOrder : matchingOrders) {
                if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                // Kiểm tra xem hai lệnh có thể khớp nhau không
                if (!canMatch(newOrder, matchingOrder)) {
                    continue;
                }

                // Tính toán khối lượng khớp lệnh
                BigDecimal matchVolume = calculateMatchVolume(newOrder, matchingOrder);
                if (matchVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // Tính toán giá khớp lệnh (sẽ được sử dụng trong phiên bản sau)

                // Cập nhật khối lượng đã khớp
                newOrder = newOrder.toBuilder()
                        .dealVolume(newOrder.getDealVolume().add(matchVolume))
                        .build();

                matchingOrder = matchingOrder.toBuilder()
                        .dealVolume(matchingOrder.getDealVolume().add(matchVolume))
                        .build();

                // Cập nhật trạng thái lệnh
                if (matchingOrder.getDealVolume().compareTo(matchingOrder.getVolume()) >= 0) {
                    matchingOrder = matchingOrder.toBuilder()
                            .status(OrderStatus.FILLED)
                            .build();
                } else {
                    matchingOrder = matchingOrder.toBuilder()
                            .status(OrderStatus.PARTIALLY_FILLED)
                            .build();
                }

                // Thêm vào danh sách lệnh đã khớp
                matchedOrders.add(matchingOrder);

                // Cập nhật khối lượng còn lại
                remainingVolume = newOrder.getVolume().subtract(newOrder.getDealVolume());
            }

            // Cập nhật trạng thái lệnh mới
            if (newOrder.getDealVolume().compareTo(newOrder.getVolume()) >= 0) {
                newOrder = newOrder.toBuilder()
                        .status(OrderStatus.FILLED)
                        .build();
            } else if (newOrder.getDealVolume().compareTo(BigDecimal.ZERO) > 0) {
                newOrder = newOrder.toBuilder()
                        .status(OrderStatus.PARTIALLY_FILLED)
                        .build();
            }

            // Thêm lệnh mới vào danh sách lệnh đã khớp
            matchedOrders.add(newOrder);

            return matchedOrders;
        } catch (Exception e) {
            log.error("Lỗi khi khớp lệnh, newOrder = {}", newOrder.getId(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Khớp lệnh thị trường
     * @param newOrder Lệnh mới
     * @param existingOrders Danh sách các lệnh hiện có
     * @param contract Hợp đồng
     * @return Danh sách các lệnh đã được khớp
     */
    private List<Order> matchMarketOrder(Order newOrder, List<Order> existingOrders, Contract contract) {
        // Triển khai logic khớp lệnh thị trường
        // Tương tự như matchOrder nhưng ưu tiên khớp ngay lập tức với giá thị trường
        return matchOrder(newOrder, existingOrders, contract);
    }

    /**
     * Tính toán giá khớp lệnh
     * @param buyOrder Lệnh mua
     * @param sellOrder Lệnh bán
     * @param contract Hợp đồng
     * @return Giá khớp lệnh
     */
    @Override
    public Money calculateMatchPrice(Order buyOrder, Order sellOrder, Contract contract) {
        if (buyOrder == null || sellOrder == null || contract == null) {
            return Money.ZERO;
        }

        // Xác định lệnh mua và lệnh bán
        Order buy = buyOrder.getDirection() == OrderDirection.BUY ? buyOrder : sellOrder;
        Order sell = buyOrder.getDirection() == OrderDirection.SELL ? buyOrder : sellOrder;

        // Nếu một trong hai lệnh là lệnh thị trường, sử dụng giá của lệnh còn lại
        if (buy.getType() == OrderType.MARKET) {
            return sell.getPrice();
        } else if (sell.getType() == OrderType.MARKET) {
            return buy.getPrice();
        }

        // Nếu cả hai lệnh đều là lệnh giới hạn, sử dụng giá của lệnh đến trước
        if (buy.getCreateTime().isBefore(sell.getCreateTime())) {
            return buy.getPrice();
        } else {
            return sell.getPrice();
        }
    }

    /**
     * Tính toán khối lượng khớp lệnh
     * @param buyOrder Lệnh mua
     * @param sellOrder Lệnh bán
     * @return Khối lượng khớp lệnh
     */
    @Override
    public BigDecimal calculateMatchVolume(Order buyOrder, Order sellOrder) {
        if (buyOrder == null || sellOrder == null) {
            return BigDecimal.ZERO;
        }

        // Tính toán khối lượng còn lại của mỗi lệnh
        BigDecimal buyRemainingVolume = buyOrder.getVolume().subtract(buyOrder.getDealVolume());
        BigDecimal sellRemainingVolume = sellOrder.getVolume().subtract(sellOrder.getDealVolume());

        // Khối lượng khớp lệnh là giá trị nhỏ hơn giữa khối lượng còn lại của hai lệnh
        return buyRemainingVolume.min(sellRemainingVolume);
    }

    /**
     * Kiểm tra xem hai lệnh có thể khớp nhau không
     * @param order1 Lệnh thứ nhất
     * @param order2 Lệnh thứ hai
     * @return true nếu hai lệnh có thể khớp nhau
     */
    @Override
    public boolean canMatch(Order order1, Order order2) {
        if (order1 == null || order2 == null) {
            return false;
        }

        // Hai lệnh phải có hướng ngược nhau
        if (order1.getDirection() == order2.getDirection()) {
            return false;
        }

        // Hai lệnh phải cùng symbol
        if (!order1.getSymbol().equals(order2.getSymbol())) {
            return false;
        }

        // Hai lệnh phải có trạng thái hợp lệ
        if (order1.getStatus() != OrderStatus.NEW && order1.getStatus() != OrderStatus.PARTIALLY_FILLED) {
            return false;
        }
        if (order2.getStatus() != OrderStatus.NEW && order2.getStatus() != OrderStatus.PARTIALLY_FILLED) {
            return false;
        }

        // Xác định lệnh mua và lệnh bán
        Order buyOrder = order1.getDirection() == OrderDirection.BUY ? order1 : order2;
        Order sellOrder = order1.getDirection() == OrderDirection.SELL ? order1 : order2;

        // Nếu cả hai lệnh đều là lệnh giới hạn, giá mua phải >= giá bán
        if (buyOrder.getType() == OrderType.LIMIT && sellOrder.getType() == OrderType.LIMIT) {
            return buyOrder.getPrice().getValue().compareTo(sellOrder.getPrice().getValue()) >= 0;
        }

        // Nếu một trong hai lệnh là lệnh thị trường, luôn có thể khớp
        return true;
    }

    /**
     * Lấy danh sách các lệnh đối ứng
     * @param order Lệnh
     * @param existingOrders Danh sách các lệnh hiện có
     * @return Danh sách các lệnh đối ứng
     */
    @Override
    public List<Order> getMatchingOrders(Order order, List<Order> existingOrders) {
        if (order == null || existingOrders == null) {
            return Collections.emptyList();
        }

        OrderDirection oppositeDirection = order.getDirection() == OrderDirection.BUY ? OrderDirection.SELL : OrderDirection.BUY;

        // Lọc các lệnh có hướng ngược lại và cùng symbol
        List<Order> matchingOrders = existingOrders.stream()
                .filter(o -> o.getDirection() == oppositeDirection)
                .filter(o -> o.getSymbol().equals(order.getSymbol()))
                .filter(o -> o.getStatus() == OrderStatus.NEW || o.getStatus() == OrderStatus.PARTIALLY_FILLED)
                .collect(Collectors.toList());

        // Sắp xếp các lệnh theo thuật toán khớp lệnh
        return sortOrders(matchingOrders, oppositeDirection, MatchingAlgorithm.FIFO);
    }

    /**
     * Sắp xếp các lệnh theo thuật toán khớp lệnh
     * @param orders Danh sách các lệnh
     * @param direction Hướng của lệnh
     * @param algorithm Thuật toán khớp lệnh
     * @return Danh sách các lệnh đã được sắp xếp
     */
    @Override
    public List<Order> sortOrders(List<Order> orders, OrderDirection direction, MatchingAlgorithm algorithm) {
        if (orders == null || orders.isEmpty()) {
            return Collections.emptyList();
        }

        switch (algorithm) {
            case FIFO:
                return sortOrdersFIFO(orders, direction);
            case PRO_RATA:
                return sortOrdersProRata(orders, direction);
            case HYBRID:
                return sortOrdersHybrid(orders, direction);
            default:
                return sortOrdersFIFO(orders, direction);
        }
    }

    /**
     * Sắp xếp các lệnh theo thuật toán FIFO
     * @param orders Danh sách các lệnh
     * @param direction Hướng của lệnh
     * @return Danh sách các lệnh đã được sắp xếp
     */
    private List<Order> sortOrdersFIFO(List<Order> orders, OrderDirection direction) {
        if (orders == null || orders.isEmpty()) {
            return Collections.emptyList();
        }

        // Sắp xếp theo giá (tốt nhất lên đầu) và thời gian (sớm nhất lên đầu)
        return orders.stream()
                .sorted((o1, o2) -> {
                    // Sắp xếp theo giá
                    int priceCompare;
                    if (direction == OrderDirection.BUY) {
                        // Đối với lệnh mua, giá cao nhất lên đầu
                        priceCompare = o2.getPrice().getValue().compareTo(o1.getPrice().getValue());
                    } else {
                        // Đối với lệnh bán, giá thấp nhất lên đầu
                        priceCompare = o1.getPrice().getValue().compareTo(o2.getPrice().getValue());
                    }

                    // Nếu giá bằng nhau, sắp xếp theo thời gian
                    if (priceCompare == 0) {
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }

                    return priceCompare;
                })
                .collect(Collectors.toList());
    }

    /**
     * Sắp xếp các lệnh theo thuật toán Pro-Rata
     * @param orders Danh sách các lệnh
     * @param direction Hướng của lệnh
     * @return Danh sách các lệnh đã được sắp xếp
     */
    private List<Order> sortOrdersProRata(List<Order> orders, OrderDirection direction) {
        // Triển khai thuật toán Pro-Rata
        // Ưu tiên theo giá, sau đó phân bổ theo tỷ lệ khối lượng
        return sortOrdersFIFO(orders, direction); // Tạm thời sử dụng FIFO
    }

    /**
     * Sắp xếp các lệnh theo thuật toán Hybrid
     * @param orders Danh sách các lệnh
     * @param direction Hướng của lệnh
     * @return Danh sách các lệnh đã được sắp xếp
     */
    private List<Order> sortOrdersHybrid(List<Order> orders, OrderDirection direction) {
        // Triển khai thuật toán Hybrid
        // Kết hợp FIFO và Pro-Rata
        return sortOrdersFIFO(orders, direction); // Tạm thời sử dụng FIFO
    }

}
