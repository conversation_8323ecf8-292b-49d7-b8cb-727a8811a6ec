package com.icetea.lotus.infrastructure.matching;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.MatchingAlgorithm;
import com.icetea.lotus.core.domain.entity.MutableOrder;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.service.OrderBook;
import com.icetea.lotus.core.domain.util.OrderDirectionConverter;
import com.icetea.lotus.core.domain.util.OrderStatusConverter;
import com.icetea.lotus.core.domain.util.OrderTypeConverter;
import com.icetea.lotus.core.domain.valueobject.*;
import com.icetea.lotus.infrastructure.config.MatchingEngineConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.icetea.lotus.core.domain.valueobject.OrderType.*;

/**
 * Matching Engine cho một symbol cụ thể
 */
@Slf4j
public class SymbolMatchingEngine {

    private final Symbol symbol;
    private final Contract contract;

    @Getter
    private MatchingAlgorithm matchingAlgorithm;

    @Getter
    @Setter
    private boolean tradingEnabled;

    @Getter
    @Setter
    private Money markPrice;

    @Getter
    @Setter
    private Money indexPrice;

    @Getter
    private Money lastPrice;

    // Sổ lệnh
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    // Lệnh chờ
    private final List<Order> stopOrders = new ArrayList<>();

    // Vị thế
    private final List<Position> positions = new ArrayList<>();

    /**
     * Khởi tạo SymbolMatchingEngine
     * @param symbol Symbol của hợp đồng
     * @param contract Hợp đồng
     */
    public SymbolMatchingEngine(Symbol symbol, Contract contract) {
        this.symbol = symbol;
        this.contract = contract;
        this.matchingAlgorithm = MatchingAlgorithm.FIFO;
        this.tradingEnabled = true;
        this.markPrice = Money.ZERO;
        this.indexPrice = Money.ZERO;
        this.lastPrice = Money.ZERO;
    }

    /**
     * Xử lý lệnh chờ
     * @param order Lệnh chờ
     * @return Danh sách giao dịch
     */
    public List<Trade> handleStopOrder(Order order) {
        // Kiểm tra xem lệnh có phải là lệnh chờ không
        if (isStopOrder(order)) {
            // Nếu lệnh chờ có thể kích hoạt ngay, xử lý như lệnh thường
            if (canTriggerNow(order)) {
                // Tạo lệnh mới từ lệnh chờ
                Order newOrder = order.toBuilder()
                        .type(OrderTypeConverter.toEntity(OrderType.LIMIT))
                        .triggerPrice(null)
                        .status(OrderStatusConverter.toEntity(OrderStatus.NEW))
                        .build();

                // Khớp lệnh mới
                return matchOrder(newOrder);
            } else {
                // Nếu không thể kích hoạt ngay, thêm vào danh sách lệnh chờ
                stopOrders.add(order);
                return Collections.emptyList();
            }
        } else {
            // Nếu không phải lệnh chờ, xử lý như lệnh thường
            return matchOrder(order);
        }
    }

    /**
     * Kiểm tra xem lệnh có phải là lệnh chờ không
     * @param order Lệnh cần kiểm tra
     * @return true nếu lệnh là lệnh chờ
     */
    private boolean isStopOrder(Order order) {
        OrderType orderType = OrderTypeConverter.toValueObject(order.getType());
        return orderType == STOP_MARKET ||
               orderType == STOP_LIMIT ||
               orderType == TAKE_PROFIT_MARKET ||
               orderType == TAKE_PROFIT_LIMIT;
    }

    /**
     * Khớp lệnh
     * @param order Lệnh giao dịch
     * @return List<Trade>
     */
    public List<Trade> matchOrder(Order order) {
        // Xử lý lệnh theo cơ chế lock-free
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Danh sách giao dịch được tạo ra
            List<Trade> trades = new ArrayList<>();

            // Khớp lệnh theo thuật toán được chọn
            switch (matchingAlgorithm) {
                case FIFO:
                    matchOrderFIFO(order, newSnapshot, trades);
                    break;
                case PRO_RATA:
                    matchOrderProRata(order, newSnapshot, trades);
                    break;
                case HYBRID:
                    matchOrderHybrid(order, newSnapshot, trades);
                    break;
                case TWAP:
                    matchOrderTWAP(order, newSnapshot, trades);
                    break;
                default:
                    matchOrderFIFO(order, newSnapshot, trades);
                    break;
            }

            // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
            if (!trades.isEmpty()) {
                lastPrice = trades.get(trades.size() - 1).getPrice();
            }

            // Thử cập nhật snapshot
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return trades;
            }
        }
    }

    /**
     * Khớp lệnh theo thuật toán FIFO
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderFIFO(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        MutableOrder mutableOrder = MutableOrder.fromOrder(order);

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = mutableOrder.getVolume().subtract(mutableOrder.getDealVolume());

        // Khớp lệnh với các lệnh đối ứng
        remainingVolume = matchWithOppositeOrders(mutableOrder, remainingVolume, snapshot, trades);

        // Cập nhật lệnh mới
        updateOrderStatus(mutableOrder, remainingVolume, snapshot);
    }

    /**
     * Khớp lệnh với các lệnh đối ứng
     * @param mutableOrder Lệnh giao dịch
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchWithOppositeOrders(MutableOrder mutableOrder, BigDecimal remainingVolume,
                                              OrderBookSnapshot snapshot, List<Trade> trades) {
        // Lấy danh sách lệnh đối ứng
        boolean isBuyOrder = mutableOrder.getDirection() == OrderDirection.BUY;

        // Khớp lệnh
        while (remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
            // Lấy mức giá tốt nhất
            Money bestPrice = isBuyOrder ? snapshot.getLowestSellPrice() : snapshot.getHighestBuyPrice();
            if (bestPrice == null) {
                break; // Không có lệnh đối ứng
            }

            List<MutableOrder> ordersAtBestPrice = isBuyOrder ?
                snapshot.getSellOrdersAtPrice(bestPrice) : snapshot.getBuyOrdersAtPrice(bestPrice);

            // Kiểm tra điều kiện giá
            if (!isPriceAcceptable(mutableOrder, bestPrice)) {
                break;
            }

            // Khớp lệnh với các lệnh ở mức giá tốt nhất
            remainingVolume = matchOrdersAtPrice(mutableOrder, ordersAtBestPrice, bestPrice, remainingVolume, trades);

            // Nếu không còn lệnh ở mức giá này, xóa mức giá
            if (ordersAtBestPrice.isEmpty()) {
                if (isBuyOrder) {
                    snapshot.removeSellPrice(bestPrice);
                } else {
                    snapshot.removeBuyPrice(bestPrice);
                }
            }
        }

        return remainingVolume;
    }

    /**
     * Kiểm tra xem giá có phù hợp không
     * @param mutableOrder Lệnh giao dịch
     * @param bestPrice Giá tốt nhất
     * @return true nếu giá phù hợp
     */
    private boolean isPriceAcceptable(MutableOrder mutableOrder, Money bestPrice) {
        com.icetea.lotus.core.domain.valueobject.OrderType orderType = OrderTypeConverter.toValueObject(mutableOrder.getType());
        return !(com.icetea.lotus.core.domain.valueobject.OrderType.LIMIT.equals(orderType) &&
            ((OrderDirection.BUY.equals(mutableOrder.getDirection()) && bestPrice.compareTo(mutableOrder.getPrice()) > 0) ||
             (OrderDirection.SELL.equals(mutableOrder.getDirection()) && bestPrice.compareTo(mutableOrder.getPrice()) < 0)));
    }

    /**
     * Khớp lệnh với các lệnh ở mức giá tốt nhất
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtBestPrice Danh sách lệnh ở mức giá tốt nhất
     * @param bestPrice Giá tốt nhất
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchOrdersAtPrice(MutableOrder mutableOrder, List<MutableOrder> ordersAtBestPrice,
                                         Money bestPrice, BigDecimal remainingVolume, List<Trade> trades) {
        for (int i = 0; i < ordersAtBestPrice.size() && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
            MutableOrder matchingOrder = ordersAtBestPrice.get(i);

            // Tính toán khối lượng khớp
            BigDecimal matchingOrderRemainingVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());
            BigDecimal matchVolume = remainingVolume.min(matchingOrderRemainingVolume);

            // Tạo giao dịch
            Trade trade = createTrade(mutableOrder, matchingOrder, bestPrice, matchVolume);
            trades.add(trade);

            // Cập nhật khối lượng đã khớp
            remainingVolume = remainingVolume.subtract(matchVolume);

            // Cập nhật lệnh đối ứng
            updateMatchingOrderStatus(matchingOrder, matchVolume);

            // Nếu lệnh đối ứng đã khớp hết, xóa khỏi danh sách
            if (matchingOrder.getDealVolume().compareTo(matchingOrder.getVolume()) >= 0) {
                ordersAtBestPrice.remove(i);
                i--; // Điều chỉnh chỉ số
            }
        }

        return remainingVolume;
    }

    /**
     * Cập nhật trạng thái lệnh đối ứng
     * @param matchingOrder Lệnh đối ứng
     * @param matchVolume Khối lượng khớp
     */
    private void updateMatchingOrderStatus(MutableOrder matchingOrder, BigDecimal matchVolume) {
        // Cập nhật lệnh đối ứng
        BigDecimal newDealVolume = matchingOrder.getDealVolume().add(matchVolume);
        matchingOrder.setDealVolume(newDealVolume);

        // Cập nhật trạng thái lệnh đối ứng
        if (newDealVolume.compareTo(matchingOrder.getVolume()) >= 0) {
            matchingOrder.setStatus(OrderStatus.FILLED);
        } else {
            matchingOrder.setStatus(OrderStatus.PARTIALLY_FILLED);
        }
    }

    /**
     * Cập nhật trạng thái lệnh
     * @param mutableOrder Lệnh giao dịch
     * @param remainingVolume Khối lượng còn lại
     * @param snapshot Snapshot của order book
     */
    private void updateOrderStatus(MutableOrder mutableOrder, BigDecimal remainingVolume, OrderBookSnapshot snapshot) {
        // Cập nhật lệnh mới
        BigDecimal newDealVolume = mutableOrder.getVolume().subtract(remainingVolume);
        mutableOrder.setDealVolume(newDealVolume);

        // Cập nhật trạng thái lệnh mới
        if (newDealVolume.compareTo(mutableOrder.getVolume()) >= 0) {
            mutableOrder.setStatus(OrderStatus.FILLED);
        } else if (newDealVolume.compareTo(BigDecimal.ZERO) > 0) {
            mutableOrder.setStatus(OrderStatus.PARTIALLY_FILLED);

            // Thêm lệnh vào sổ lệnh nếu còn khối lượng chưa khớp
            com.icetea.lotus.core.domain.valueobject.OrderType orderType = OrderTypeConverter.toValueObject(mutableOrder.getType());
            if (com.icetea.lotus.core.domain.valueobject.OrderType.LIMIT.equals(orderType)) {
                addToOrderBook(mutableOrder, snapshot);
            }
        } else {
            // Nếu không khớp được và là lệnh giới hạn, thêm vào sổ lệnh
            com.icetea.lotus.core.domain.valueobject.OrderType orderType = OrderTypeConverter.toValueObject(mutableOrder.getType());
            if (com.icetea.lotus.core.domain.valueobject.OrderType.LIMIT.equals(orderType)) {
                mutableOrder.setStatus(OrderStatus.NEW);
                addToOrderBook(mutableOrder, snapshot);
            } else {
                mutableOrder.setStatus(OrderStatus.REJECTED);
            }
        }
    }

    /**
     * Thêm lệnh vào sổ lệnh
     * @param order Lệnh giao dịch
     * @param snapshot Snapshot của order book
     */
    private void addToOrderBook(MutableOrder order, OrderBookSnapshot snapshot) {
        // Thêm lệnh vào sổ lệnh
        if (order.getDirection() == OrderDirection.BUY) {
            snapshot.addBuyOrder(order.getPrice(), order);
        } else {
            snapshot.addSellOrder(order.getPrice(), order);
        }
    }

    /**
     * Tạo giao dịch
     * @param order Lệnh giao dịch
     * @param matchingOrder Lệnh đối ứng
     * @param price Giá khớp
     * @param volume Khối lượng khớp
     * @return Trade
     */
    private Trade createTrade(MutableOrder order, MutableOrder matchingOrder, Money price, BigDecimal volume) {
        OrderDirection orderDirection = order.getDirection();

        // Xác định loại lệnh mua và bán
        com.icetea.lotus.core.domain.valueobject.OrderType buyOrderType = OrderDirection.BUY.equals(orderDirection) ?
                order.getType() : matchingOrder.getType();
        com.icetea.lotus.core.domain.valueobject.OrderType sellOrderType = OrderDirection.SELL.equals(orderDirection) ?
                order.getType() : matchingOrder.getType();

        return Trade.builder()
            .id(TradeId.of(System.currentTimeMillis()))
            .symbol(symbol)
            .price(price)
            .volume(volume)
            .buyOrderId(OrderDirection.BUY.equals(orderDirection) ? order.getOrderId() : matchingOrder.getOrderId())
            .sellOrderId(OrderDirection.SELL.equals(orderDirection) ? order.getOrderId() : matchingOrder.getOrderId())
            .buyMemberId(OrderDirection.BUY.equals(orderDirection) ? order.getMemberId() : matchingOrder.getMemberId())
            .sellMemberId(OrderDirection.SELL.equals(orderDirection) ? order.getMemberId() : matchingOrder.getMemberId())
            .tradeTime(LocalDateTime.now())
            .buyFee(Money.ZERO)
            .sellFee(Money.ZERO)
            .buyOrderType(OrderTypeConverter.toEntity(buyOrderType))
            .sellOrderType(OrderTypeConverter.toEntity(sellOrderType))
            .leverage(order.getLeverage()) // ✅ FIX: Thêm leverage từ order
            .build();
    }

    /**
     * Khớp lệnh theo thuật toán Pro-Rata
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderProRata(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        MutableOrder mutableOrder = MutableOrder.fromOrder(order);

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = mutableOrder.getVolume().subtract(mutableOrder.getDealVolume());

        // Khớp lệnh với các lệnh đối ứng theo thuật toán Pro-Rata
        remainingVolume = matchWithOppositeOrdersProRata(mutableOrder, remainingVolume, snapshot, trades);

        // Cập nhật lệnh mới
        updateOrderStatus(mutableOrder, remainingVolume, snapshot);
    }

    /**
     * Khớp lệnh với các lệnh đối ứng theo thuật toán Pro-Rata
     * @param mutableOrder Lệnh giao dịch
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchWithOppositeOrdersProRata(MutableOrder mutableOrder, BigDecimal remainingVolume,
                                                    OrderBookSnapshot snapshot, List<Trade> trades) {
        try {
            // Lấy danh sách lệnh đối ứng
            boolean isBuyOrder = mutableOrder.getDirection() == OrderDirection.BUY;

            // Khớp lệnh
            boolean shouldContinue = true;
            while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 && shouldContinue) {
                // Lấy mức giá tốt nhất
                Money bestPrice = isBuyOrder ? snapshot.getLowestSellPrice() : snapshot.getHighestBuyPrice();
                if (bestPrice == null) {
                    break; // Không có lệnh đối ứng
                }

                List<MutableOrder> ordersAtBestPrice = isBuyOrder ?
                    snapshot.getSellOrdersAtPrice(bestPrice) : snapshot.getBuyOrdersAtPrice(bestPrice);

                // Kiểm tra điều kiện giá
                if (!isPriceAcceptable(mutableOrder, bestPrice)) {
                    shouldContinue = false;
                    continue;
                }

                // Tính toán thông tin khớp lệnh Pro-Rata
                ProRataMatchInfo matchInfo = calculateProRataMatchInfo(ordersAtBestPrice);

                // Khớp lệnh dựa trên thông tin đã tính toán
                if (matchInfo.getTotalAvailableVolume().compareTo(remainingVolume) <= 0) {
                    // Nếu tổng khối lượng có thể khớp nhỏ hơn hoặc bằng khối lượng cần khớp,
                    // khớp toàn bộ lệnh ở mức giá này
                    remainingVolume = matchAllOrdersProRata(mutableOrder, ordersAtBestPrice, matchInfo,
                                                          bestPrice, remainingVolume, trades);

                    // Xóa mức giá này
                    if (isBuyOrder) {
                        snapshot.removeSellPrice(bestPrice);
                    } else {
                        snapshot.removeBuyPrice(bestPrice);
                    }
                } else {
                    // Nếu tổng khối lượng có thể khớp lớn hơn khối lượng cần khớp,
                    // phân bổ khối lượng cần khớp theo tỷ lệ
                    remainingVolume = matchOrdersProRataByRatio(mutableOrder, ordersAtBestPrice, matchInfo,
                                                              bestPrice, remainingVolume, trades);

                    // Nếu đã khớp hết khối lượng cần khớp, dừng lại
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        shouldContinue = false;
                    }
                }
            }

            return remainingVolume;
        } catch (Exception e) {
            log.error("Lỗi khi khớp lệnh Pro-Rata: {}, symbol: {}", mutableOrder.getOrderId(), symbol, e);
            return remainingVolume;
        }
    }

    /**
     * Tính toán thông tin khớp lệnh Pro-Rata
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @return Thông tin khớp lệnh Pro-Rata
     */
    private ProRataMatchInfo calculateProRataMatchInfo(List<MutableOrder> ordersAtPrice) {
        BigDecimal totalAvailableVolume = BigDecimal.ZERO;
        Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap = new HashMap<>();

        for (MutableOrder matchingOrder : ordersAtPrice) {
            BigDecimal matchingOrderRemainingVolume = matchingOrder.getVolume().subtract(matchingOrder.getDealVolume());
            orderToRemainingVolumeMap.put(matchingOrder, matchingOrderRemainingVolume);
            totalAvailableVolume = totalAvailableVolume.add(matchingOrderRemainingVolume);
        }

        return new ProRataMatchInfo(totalAvailableVolume, orderToRemainingVolumeMap);
    }

    /**
     * Khớp toàn bộ lệnh ở mức giá theo thuật toán Pro-Rata
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param matchInfo Thông tin khớp lệnh Pro-Rata
     * @param price Giá khớp
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchAllOrdersProRata(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                          ProRataMatchInfo matchInfo, Money price,
                                          BigDecimal remainingVolume, List<Trade> trades) {
        Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap = matchInfo.getOrderToRemainingVolumeMap();

        for (MutableOrder matchingOrder : ordersAtPrice) {
            BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);

            // Tạo giao dịch
            Trade trade = createTrade(mutableOrder, matchingOrder, price, matchingOrderRemainingVolume);
            trades.add(trade);

            // Cập nhật khối lượng đã khớp
            remainingVolume = remainingVolume.subtract(matchingOrderRemainingVolume);

            // Cập nhật lệnh đối ứng
            updateMatchingOrderStatus(matchingOrder, matchingOrderRemainingVolume);
        }

        return remainingVolume;
    }

    /**
     * Khớp lệnh theo tỷ lệ với thuật toán Pro-Rata
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param matchInfo Thông tin khớp lệnh Pro-Rata
     * @param price Giá khớp
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchOrdersProRataByRatio(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                              ProRataMatchInfo matchInfo, Money price,
                                              BigDecimal remainingVolume, List<Trade> trades) {
        BigDecimal totalAvailableVolume = matchInfo.getTotalAvailableVolume();
        Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap = matchInfo.getOrderToRemainingVolumeMap();

        for (MutableOrder matchingOrder : ordersAtPrice) {
            BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);

            // Tính khối lượng khớp theo tỷ lệ
            BigDecimal ratio = matchingOrderRemainingVolume.divide(totalAvailableVolume, 8, RoundingMode.DOWN);
            BigDecimal matchVolume = remainingVolume.multiply(ratio).setScale(8, RoundingMode.DOWN);

            // Đảm bảo khối lượng khớp không vượt quá khối lượng còn lại của lệnh đối ứng
            matchVolume = matchVolume.min(matchingOrderRemainingVolume);

            if (matchVolume.compareTo(BigDecimal.ZERO) > 0) {
                // Tạo giao dịch
                Trade trade = createTrade(mutableOrder, matchingOrder, price, matchVolume);
                trades.add(trade);

                // Cập nhật khối lượng đã khớp
                remainingVolume = remainingVolume.subtract(matchVolume);

                // Cập nhật lệnh đối ứng
                updateMatchingOrderStatus(matchingOrder, matchVolume);
            }
        }

        return remainingVolume;
    }

    /**
     * Lớp chứa thông tin khớp lệnh Pro-Rata
     */
    private static class ProRataMatchInfo {
        private final BigDecimal totalAvailableVolume;
        private final Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap;

        public ProRataMatchInfo(BigDecimal totalAvailableVolume, Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap) {
            this.totalAvailableVolume = totalAvailableVolume;
            this.orderToRemainingVolumeMap = orderToRemainingVolumeMap;
        }

        public BigDecimal getTotalAvailableVolume() {
            return totalAvailableVolume;
        }

        public Map<MutableOrder, BigDecimal> getOrderToRemainingVolumeMap() {
            return orderToRemainingVolumeMap;
        }
    }

    /**
     * Khớp lệnh theo thuật toán Hybrid (kết hợp FIFO và Pro-Rata)
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderHybrid(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        MutableOrder mutableOrder = MutableOrder.fromOrder(order);

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = mutableOrder.getVolume().subtract(mutableOrder.getDealVolume());

        // Khớp lệnh với các lệnh đối ứng theo thuật toán Hybrid
        remainingVolume = matchWithOppositeOrdersHybrid(mutableOrder, remainingVolume, snapshot, trades);

        // Cập nhật lệnh mới
        updateOrderStatus(mutableOrder, remainingVolume, snapshot);
    }

    /**
     * Khớp lệnh với các lệnh đối ứng theo thuật toán Hybrid
     * @param mutableOrder Lệnh giao dịch
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchWithOppositeOrdersHybrid(MutableOrder mutableOrder, BigDecimal remainingVolume,
                                                   OrderBookSnapshot snapshot, List<Trade> trades) {
        try {
            // Lấy danh sách lệnh đối ứng
            boolean isBuyOrder = mutableOrder.getDirection() == OrderDirection.BUY;

            // Tỷ lệ phân bổ cho FIFO (20% cho FIFO, 80% cho Pro-Rata)
            final BigDecimal fifoRatio = new BigDecimal("0.2");

            // Khớp lệnh
            boolean shouldContinue = true;
            while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 && shouldContinue) {
                // Lấy mức giá tốt nhất
                Money bestPrice = isBuyOrder ? snapshot.getLowestSellPrice() : snapshot.getHighestBuyPrice();
                if (bestPrice == null) {
                    break; // Không có lệnh đối ứng
                }

                List<MutableOrder> ordersAtBestPrice = isBuyOrder ?
                    snapshot.getSellOrdersAtPrice(bestPrice) : snapshot.getBuyOrdersAtPrice(bestPrice);

                // Kiểm tra điều kiện giá
                if (!isPriceAcceptable(mutableOrder, bestPrice)) {
                    shouldContinue = false;
                    continue;
                }

                // Tính tổng khối lượng có thể khớp ở mức giá tốt nhất
                Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap = calculateRemainingVolumes(ordersAtBestPrice);
                BigDecimal totalAvailableVolume = calculateTotalAvailableVolume(orderToRemainingVolumeMap);

                // Nếu tổng khối lượng có thể khớp nhỏ hơn hoặc bằng khối lượng cần khớp,
                // khớp toàn bộ lệnh ở mức giá này
                if (totalAvailableVolume.compareTo(remainingVolume) <= 0) {
                    remainingVolume = matchAllOrdersAtPrice(mutableOrder, ordersAtBestPrice, orderToRemainingVolumeMap,
                                                          bestPrice, remainingVolume, trades);

                    // Xóa mức giá này
                    if (isBuyOrder) {
                        snapshot.removeSellPrice(bestPrice);
                    } else {
                        snapshot.removeBuyPrice(bestPrice);
                    }
                } else {
                    // Nếu tổng khối lượng có thể khớp lớn hơn khối lượng cần khớp,
                    // phân bổ khối lượng cần khớp theo thuật toán Hybrid
                    remainingVolume = matchOrdersWithHybridAlgorithm(mutableOrder, ordersAtBestPrice,
                                                                   orderToRemainingVolumeMap, bestPrice,
                                                                   remainingVolume, fifoRatio, trades);

                    // Nếu đã khớp hết khối lượng cần khớp, dừng lại
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        shouldContinue = false;
                    }
                }
            }

            return remainingVolume;
        } catch (Exception e) {
            log.error("Lỗi khi khớp lệnh Hybrid: {}, symbol: {}", mutableOrder.getOrderId(), symbol, e);
            return remainingVolume;
        }
    }

    /**
     * Tính toán khối lượng còn lại của các lệnh
     * @param orders Danh sách lệnh
     * @return Map chứa lệnh và khối lượng còn lại
     */
    private Map<MutableOrder, BigDecimal> calculateRemainingVolumes(List<MutableOrder> orders) {
        Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap = new HashMap<>();

        for (MutableOrder order : orders) {
            BigDecimal remainingVolume = order.getVolume().subtract(order.getDealVolume());
            orderToRemainingVolumeMap.put(order, remainingVolume);
        }

        return orderToRemainingVolumeMap;
    }

    /**
     * Tính tổng khối lượng có thể khớp
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @return Tổng khối lượng có thể khớp
     */
    private BigDecimal calculateTotalAvailableVolume(Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap) {
        BigDecimal totalAvailableVolume = BigDecimal.ZERO;

        for (BigDecimal volume : orderToRemainingVolumeMap.values()) {
            totalAvailableVolume = totalAvailableVolume.add(volume);
        }

        return totalAvailableVolume;
    }

    /**
     * Khớp toàn bộ lệnh ở mức giá
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @param price Giá khớp
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchAllOrdersAtPrice(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                          Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap,
                                          Money price, BigDecimal remainingVolume, List<Trade> trades) {
        for (MutableOrder matchingOrder : ordersAtPrice) {
            BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);

            // Tạo giao dịch
            Trade trade = createTrade(mutableOrder, matchingOrder, price, matchingOrderRemainingVolume);
            trades.add(trade);

            // Cập nhật khối lượng đã khớp
            remainingVolume = remainingVolume.subtract(matchingOrderRemainingVolume);

            // Cập nhật lệnh đối ứng
            updateMatchingOrderStatus(matchingOrder, matchingOrderRemainingVolume);
        }

        return remainingVolume;
    }

    /**
     * Khớp lệnh với thuật toán Hybrid
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @param price Giá khớp
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param fifoRatio Tỷ lệ phân bổ cho FIFO
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchOrdersWithHybridAlgorithm(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                                   Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap,
                                                   Money price, BigDecimal remainingVolume,
                                                   BigDecimal fifoRatio, List<Trade> trades) {
        // Phân bổ khối lượng cho FIFO và Pro-Rata
        BigDecimal fifoVolume = remainingVolume.multiply(fifoRatio).setScale(8, RoundingMode.DOWN);
        BigDecimal proRataVolume = remainingVolume.subtract(fifoVolume);

        // Khớp lệnh theo FIFO
        HybridMatchingResult fifoResult = matchOrdersFIFOPart(mutableOrder, ordersAtPrice,
                                                            orderToRemainingVolumeMap, price,
                                                            fifoVolume, remainingVolume, trades);
        remainingVolume = fifoResult.getRemainingVolume();

        // Khớp lệnh theo Pro-Rata với các lệnh còn lại
        if (proRataVolume.compareTo(BigDecimal.ZERO) > 0 && remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
            remainingVolume = matchOrdersProRataPart(mutableOrder, ordersAtPrice,
                                                   orderToRemainingVolumeMap, price,
                                                   proRataVolume, remainingVolume,
                                                   fifoResult.getProcessedOrders(), trades);
        }

        return remainingVolume;
    }

    /**
     * Khớp lệnh theo FIFO
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @param price Giá khớp
     * @param fifoVolume Khối lượng dành cho FIFO
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param trades Danh sách giao dịch
     * @return Kết quả khớp lệnh FIFO
     */
    private HybridMatchingResult matchOrdersFIFOPart(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                                  Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap,
                                                  Money price, BigDecimal fifoVolume, BigDecimal remainingVolume,
                                                  List<Trade> trades) {
        BigDecimal remainingFifoVolume = fifoVolume;
        List<MutableOrder> processedFifoOrders = new ArrayList<>();

        for (int i = 0; i < ordersAtPrice.size() && remainingFifoVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
            MutableOrder matchingOrder = ordersAtPrice.get(i);
            processedFifoOrders.add(matchingOrder);

            BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);
            BigDecimal matchVolume = remainingFifoVolume.min(matchingOrderRemainingVolume);

            if (matchVolume.compareTo(BigDecimal.ZERO) > 0) {
                // Tạo giao dịch
                Trade trade = createTrade(mutableOrder, matchingOrder, price, matchVolume);
                trades.add(trade);

                // Cập nhật khối lượng đã khớp
                remainingFifoVolume = remainingFifoVolume.subtract(matchVolume);
                remainingVolume = remainingVolume.subtract(matchVolume);

                // Cập nhật lệnh đối ứng
                updateMatchingOrder(matchingOrder, matchVolume, matchingOrderRemainingVolume, orderToRemainingVolumeMap);
            }
        }

        return new HybridMatchingResult(remainingVolume, processedFifoOrders);
    }

    /**
     * Khớp lệnh theo Pro-Rata
     * @param mutableOrder Lệnh giao dịch
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @param price Giá khớp
     * @param proRataVolume Khối lượng dành cho Pro-Rata
     * @param remainingVolume Khối lượng còn lại cần khớp
     * @param processedFifoOrders Danh sách lệnh đã xử lý bởi FIFO
     * @param trades Danh sách giao dịch
     * @return Khối lượng còn lại sau khi khớp
     */
    private BigDecimal matchOrdersProRataPart(MutableOrder mutableOrder, List<MutableOrder> ordersAtPrice,
                                           Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap,
                                           Money price, BigDecimal proRataVolume, BigDecimal remainingVolume,
                                           List<MutableOrder> processedFifoOrders, List<Trade> trades) {
        // Tính lại tổng khối lượng có thể khớp sau khi đã khớp FIFO
        BigDecimal remainingTotalAvailableVolume = calculateRemainingTotalAvailableVolume(
            ordersAtPrice, orderToRemainingVolumeMap, processedFifoOrders);

        // Nếu còn lệnh chưa được xử lý
        if (remainingTotalAvailableVolume.compareTo(BigDecimal.ZERO) > 0) {
            for (MutableOrder matchingOrder : ordersAtPrice) {
                if (!processedFifoOrders.contains(matchingOrder)) {
                    BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);

                    // Tính khối lượng khớp theo tỷ lệ
                    BigDecimal ratio = matchingOrderRemainingVolume.divide(remainingTotalAvailableVolume, 8, RoundingMode.DOWN);
                    BigDecimal matchVolume = proRataVolume.multiply(ratio).setScale(8, RoundingMode.DOWN);

                    // Đảm bảo khối lượng khớp không vượt quá khối lượng còn lại của lệnh đối ứng
                    matchVolume = matchVolume.min(matchingOrderRemainingVolume);

                    if (matchVolume.compareTo(BigDecimal.ZERO) > 0) {
                        // Tạo giao dịch
                        Trade trade = createTrade(mutableOrder, matchingOrder, price, matchVolume);
                        trades.add(trade);

                        // Cập nhật khối lượng đã khớp
                        remainingVolume = remainingVolume.subtract(matchVolume);

                        // Cập nhật lệnh đối ứng
                        updateMatchingOrder(matchingOrder, matchVolume, matchingOrderRemainingVolume, orderToRemainingVolumeMap);
                    }
                }
            }
        }

        return remainingVolume;
    }

    /**
     * Tính tổng khối lượng có thể khớp sau khi đã khớp FIFO
     * @param ordersAtPrice Danh sách lệnh ở mức giá
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     * @param processedFifoOrders Danh sách lệnh đã xử lý bởi FIFO
     * @return Tổng khối lượng có thể khớp
     */
    private BigDecimal calculateRemainingTotalAvailableVolume(List<MutableOrder> ordersAtPrice,
                                                           Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap,
                                                           List<MutableOrder> processedFifoOrders) {
        BigDecimal remainingTotalAvailableVolume = BigDecimal.ZERO;

        for (MutableOrder matchingOrder : ordersAtPrice) {
            if (!processedFifoOrders.contains(matchingOrder)) {
                BigDecimal matchingOrderRemainingVolume = orderToRemainingVolumeMap.get(matchingOrder);
                remainingTotalAvailableVolume = remainingTotalAvailableVolume.add(matchingOrderRemainingVolume);
            }
        }

        return remainingTotalAvailableVolume;
    }

    /**
     * Cập nhật lệnh đối ứng
     * @param matchingOrder Lệnh đối ứng
     * @param matchVolume Khối lượng khớp
     * @param matchingOrderRemainingVolume Khối lượng còn lại của lệnh đối ứng
     * @param orderToRemainingVolumeMap Map chứa lệnh và khối lượng còn lại
     */
    private void updateMatchingOrder(MutableOrder matchingOrder, BigDecimal matchVolume,
                                  BigDecimal matchingOrderRemainingVolume,
                                  Map<MutableOrder, BigDecimal> orderToRemainingVolumeMap) {
        // Cập nhật lệnh đối ứng
        BigDecimal newDealVolume = matchingOrder.getDealVolume().add(matchVolume);
        matchingOrder.setDealVolume(newDealVolume);

        // Cập nhật khối lượng còn lại của lệnh đối ứng
        orderToRemainingVolumeMap.put(matchingOrder, matchingOrderRemainingVolume.subtract(matchVolume));

        // Cập nhật trạng thái lệnh đối ứng
        if (newDealVolume.compareTo(matchingOrder.getVolume()) >= 0) {
            matchingOrder.setStatus(OrderStatus.FILLED);
        } else {
            matchingOrder.setStatus(OrderStatus.PARTIALLY_FILLED);
        }
    }

    /**
     * Lớp kết quả khớp lệnh Hybrid
     */
    private static class HybridMatchingResult {
        private final BigDecimal remainingVolume;
        private final List<MutableOrder> processedOrders;

        public HybridMatchingResult(BigDecimal remainingVolume, List<MutableOrder> processedOrders) {
            this.remainingVolume = remainingVolume;
            this.processedOrders = processedOrders;
        }

        public BigDecimal getRemainingVolume() {
            return remainingVolume;
        }

        public List<MutableOrder> getProcessedOrders() {
            return processedOrders;
        }
    }

    /**
     * Khớp lệnh theo thuật toán TWAP (Time-Weighted Average Price)
     * Thuật toán này chia nhỏ lệnh lớn thành nhiều lệnh nhỏ và thực hiện khớp lệnh theo thời gian
     * để giảm tác động đến thị trường
     *
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của order book
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderTWAP(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        MutableOrder mutableOrder = MutableOrder.fromOrder(order);

        // Lấy cấu hình TWAP từ OptimizedMatchingEngine
        MatchingEngineConfig.TWAPConfig twapConfig = OptimizedMatchingEngine.getTwapConfig();

        // Kiểm tra xem lệnh có đủ lớn để áp dụng TWAP không
        BigDecimal volume = mutableOrder.getVolume();
        if (volume.compareTo(BigDecimal.valueOf(twapConfig.getMinimumVolumeThreshold())) <= 0) {
            // Nếu khối lượng nhỏ, sử dụng FIFO
            matchOrderFIFO(order, snapshot, trades);
            return;
        }

        // Xác định số lượng phần cần chia
        int numSlices = determineNumSlices(volume, twapConfig);
        BigDecimal sliceVolume = volume.divide(BigDecimal.valueOf(numSlices), 8, RoundingMode.DOWN);

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = mutableOrder.getVolume().subtract(mutableOrder.getDealVolume());

        // Khớp từng phần nhỏ
        for (int i = 0; i < numSlices && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
            // Tính khối lượng cho phần hiện tại
            BigDecimal currentSliceVolume = sliceVolume.min(remainingVolume);

            // Tạo một lệnh tạm thời với khối lượng của phần hiện tại
            Order tempOrderBase = Order.builder()
                    .orderId(mutableOrder.getOrderId())
                    .memberId(mutableOrder.getMemberId())
                    .symbol(mutableOrder.getSymbol())
                    .direction(OrderDirectionConverter.toEntity(mutableOrder.getDirection()))
                    .type(OrderTypeConverter.toEntity(mutableOrder.getType()))
                    .price(mutableOrder.getPrice())
                    .volume(currentSliceVolume)
                    .dealVolume(BigDecimal.ZERO)
                    .status(OrderStatusConverter.toEntity(mutableOrder.getStatus()))
                    .createTime(mutableOrder.getCreateTime())
                    .build();
            MutableOrder tempOrder = MutableOrder.fromOrder(tempOrderBase);

            // Khớp lệnh tạm thời với các lệnh đối ứng
            BigDecimal sliceRemainingVolume = matchWithOppositeOrders(tempOrder, currentSliceVolume, snapshot, trades);

            // Cập nhật khối lượng đã khớp cho lệnh gốc
            BigDecimal matchedVolume = currentSliceVolume.subtract(sliceRemainingVolume);
            mutableOrder.setDealVolume(mutableOrder.getDealVolume().add(matchedVolume));

            // Cập nhật khối lượng còn lại
            remainingVolume = remainingVolume.subtract(matchedVolume);

            // Mô phỏng độ trễ giữa các phần (trong môi trường thực tế, sẽ có cơ chế lập lịch)
            // Trong môi trường mô phỏng, chúng ta chỉ ghi log
            log.debug("TWAP: Đã khớp phần {} với khối lượng {}, còn lại {} phần",
                    i + 1, matchedVolume, numSlices - i - 1);
        }

        // Cập nhật trạng thái lệnh
        updateOrderStatus(mutableOrder, remainingVolume, snapshot);
    }

    /**
     * Xác định số lượng phần cần chia dựa trên khối lượng lệnh và cấu hình TWAP
     * @param volume Khối lượng lệnh
     * @param twapConfig Cấu hình TWAP
     * @return Số lượng phần
     */
    private int determineNumSlices(BigDecimal volume, MatchingEngineConfig.TWAPConfig twapConfig) {
        // Sử dụng cấu hình từ TWAPConfig
        int numSlices = volume.divide(BigDecimal.valueOf(twapConfig.getVolumePerSlice()), 0, RoundingMode.UP).intValue();
        return Math.max(twapConfig.getMinSlices(), Math.min(twapConfig.getMaxSlices(), numSlices));
    }



    /**
     * Thiết lập thuật toán khớp lệnh
     * @param algorithm Thuật toán khớp lệnh mới
     */
    public void setMatchingAlgorithm(MatchingAlgorithm algorithm) {
        if (algorithm == null) {
            log.warn("Thuật toán khớp lệnh không được để trống, giữ nguyên thuật toán hiện tại: {}", this.matchingAlgorithm);
            return;
        }

        log.info("Thay đổi thuật toán khớp lệnh từ {} thành {} cho symbol {}",
                this.matchingAlgorithm, algorithm, this.symbol.getValue());
        this.matchingAlgorithm = algorithm;
    }

    /**
     * Kiểm tra xem lệnh chờ có thể kích hoạt ngay không
     * @param order Lệnh chờ
     * @return true nếu có thể kích hoạt ngay, false nếu không
     */
    private boolean canTriggerNow(Order order) {
        if (order == null || order.getType() == null || order.getTriggerPrice() == null) {
            return false;
        }

        // Lấy giá hiện tại
        Money currentPrice = markPrice;
        if (currentPrice == null || currentPrice.equals(Money.ZERO)) {
            currentPrice = lastPrice;
        }

        if (currentPrice == null || currentPrice.equals(Money.ZERO)) {
            return false;
        }

        // Kiểm tra điều kiện kích hoạt
        com.icetea.lotus.core.domain.entity.OrderType orderType = order.getType();
        com.icetea.lotus.core.domain.valueobject.OrderType valueObjectType = OrderTypeConverter.toValueObject(orderType);

        if (com.icetea.lotus.core.domain.valueobject.OrderType.STOP_MARKET.equals(valueObjectType) ||
            com.icetea.lotus.core.domain.valueobject.OrderType.STOP_LIMIT.equals(valueObjectType)) {
            // Lệnh Stop: kích hoạt khi giá hiện tại <= giá kích hoạt (đối với lệnh bán)
            // hoặc giá hiện tại >= giá kích hoạt (đối với lệnh mua)
            if (OrderDirection.SELL.equals(order.getDirection())) {
                return currentPrice.compareTo(order.getTriggerPrice()) <= 0;
            } else {
                return currentPrice.compareTo(order.getTriggerPrice()) >= 0;
            }
        } else if (com.icetea.lotus.core.domain.valueobject.OrderType.TAKE_PROFIT_MARKET.equals(valueObjectType) ||
                   com.icetea.lotus.core.domain.valueobject.OrderType.TAKE_PROFIT_LIMIT.equals(valueObjectType)) {
            // Lệnh Take Profit: kích hoạt khi giá hiện tại >= giá kích hoạt (đối với lệnh bán)
            // hoặc giá hiện tại <= giá kích hoạt (đối với lệnh mua)
            if (OrderDirection.SELL.equals(order.getDirection())) {
                return currentPrice.compareTo(order.getTriggerPrice()) >= 0;
            } else {
                return currentPrice.compareTo(order.getTriggerPrice()) <= 0;
            }
        } else {
            return false;
        }
    }

    /**
     * Kiểm tra lệnh chờ
     */
    public void checkTriggerOrders() {
        // Kiểm tra xem có lệnh chờ nào có thể kích hoạt không
        List<Order> ordersToTrigger = new ArrayList<>();

        // Kiểm tra từng lệnh chờ
        for (Order order : stopOrders) {
            if (canTriggerNow(order)) {
                ordersToTrigger.add(order);
            }
        }

        // Xóa các lệnh đã kích hoạt khỏi danh sách lệnh chờ
        stopOrders.removeAll(ordersToTrigger);

        // Kích hoạt các lệnh
        for (Order order : ordersToTrigger) {
            // Tạo lệnh mới từ lệnh chờ
            Order newOrder = order.toBuilder()
                    .type(OrderTypeConverter.toEntity(OrderType.LIMIT))
                    .triggerPrice(null)
                    .status(OrderStatusConverter.toEntity(OrderStatus.NEW))
                    .build();

            // Khớp lệnh mới
            matchOrder(newOrder);
        }
    }

    /**
     * Kiểm tra thanh lý
     */
    public void checkLiquidations() {
        // Kiểm tra xem có vị thế nào cần thanh lý không
        List<Position> positionsToLiquidate = new ArrayList<>();

        // Kiểm tra từng vị thế
        for (Position position : positions) {
            if (needLiquidation(position)) {
                positionsToLiquidate.add(position);
            }
        }

        // Thanh lý các vị thế
        for (Position position : positionsToLiquidate) {
            liquidatePosition(position);
        }
    }

    /**
     * Kiểm tra xem vị thế có cần thanh lý không
     * @param position Vị thế cần kiểm tra
     * @return true nếu cần thanh lý, false nếu không
     */
    private boolean needLiquidation(Position position) {
        if (position == null || markPrice == null || markPrice.equals(Money.ZERO)) {
            return false;
        }

        // Lấy giá thanh lý từ vị thế
        Money liquidationPrice = position.getLiquidationPrice();
        if (liquidationPrice == null || liquidationPrice.equals(Money.ZERO)) {
            return false;
        }

        // Kiểm tra điều kiện thanh lý
        if (position.getDirection().equals(PositionDirection.LONG)) {
            // Vị thế Long: thanh lý khi giá đánh dấu <= giá thanh lý
            return markPrice.compareTo(liquidationPrice) <= 0;
        } else {
            // Vị thế Short: thanh lý khi giá đánh dấu >= giá thanh lý
            return markPrice.compareTo(liquidationPrice) >= 0;
        }
    }

    /**
     * Thanh lý vị thế
     * @param position Vị thế cần thanh lý
     */
    private void liquidatePosition(Position position) {
        // Tạo lệnh thanh lý
        com.icetea.lotus.core.domain.entity.OrderDirection direction = position.getDirection().equals(com.icetea.lotus.core.domain.entity.PositionDirection.LONG) ?
                com.icetea.lotus.core.domain.entity.OrderDirection.SELL : com.icetea.lotus.core.domain.entity.OrderDirection.BUY;

        Order liquidationOrder = Order.builder()
                .orderId(OrderId.of(System.currentTimeMillis()))
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(direction)
                .type(OrderTypeConverter.toEntity(OrderType.MARKET))
                .price(markPrice)
                .volume(position.getVolume())
                .status(OrderStatusConverter.toEntity(OrderStatus.NEW))
                .build();

        // Khớp lệnh thanh lý
        matchOrder(liquidationOrder);
    }

    /**
     * Hủy lệnh
     * @param orderId ID của lệnh
     * @return true nếu hủy thành công, false nếu không
     */
    public boolean cancelOrder(OrderId orderId) {
        // Xử lý theo cơ chế lock-free
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Tìm và hủy lệnh
            boolean removed = false;

            // Tìm trong lệnh chờ
            for (int i = 0; i < stopOrders.size(); i++) {
                Order order = stopOrders.get(i);
                if (order.getOrderId().equals(orderId)) {
                    stopOrders.remove(i);
                    removed = true;
                    break;
                }
            }

            // Nếu không tìm thấy trong lệnh chờ, tìm trong sổ lệnh
            if (!removed) {
                // Tìm trong lệnh mua
                removed = newSnapshot.removeBuyOrder(orderId);

                // Nếu không tìm thấy trong lệnh mua, tìm trong lệnh bán
                if (!removed) {
                    removed = newSnapshot.removeSellOrder(orderId);
                }
            }

            // Thử cập nhật snapshot
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return removed;
            }
        }
    }

    /**
     * Hủy tất cả lệnh của một thành viên
     * @param memberId ID của thành viên
     * @return Danh sách các lệnh đã hủy
     */
    public List<Order> cancelAllOrders(Long memberId) {
        List<Order> canceledOrders = new ArrayList<>();

        // Xử lý theo cơ chế lock-free
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Hủy lệnh chờ
            for (int i = 0; i < stopOrders.size(); i++) {
                Order order = stopOrders.get(i);
                if (order.getMemberId().equals(memberId)) {
                    stopOrders.remove(i);
                    canceledOrders.add(order);
                    i--; // Điều chỉnh chỉ số
                }
            }

            // Hủy lệnh trong sổ lệnh
            List<Order> canceledBuyOrders = newSnapshot.removeBuyOrdersByMemberId(memberId);
            List<Order> canceledSellOrders = newSnapshot.removeSellOrdersByMemberId(memberId);

            canceledOrders.addAll(canceledBuyOrders);
            canceledOrders.addAll(canceledSellOrders);

            // Thử cập nhật snapshot
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return canceledOrders;
            }
        }
    }

    /**
     * Lấy order book
     * @return Order book
     */
    public OrderBook getOrderBook() {
        // Lấy snapshot hiện tại
        OrderBookSnapshot snapshot = orderBookRef.get();

        // Lấy danh sách lệnh mua
        List<OrderBook.PriceLevel> bids = new ArrayList<>();
        for (Money price : snapshot.getBuyPrices()) {
            List<MutableOrder> ordersAtPrice = snapshot.getBuyOrdersAtPrice(price);

            BigDecimal totalVolume = BigDecimal.ZERO;
            for (MutableOrder order : ordersAtPrice) {
                totalVolume = totalVolume.add(order.getVolume().subtract(order.getDealVolume()));
            }

            List<Order> orders = new ArrayList<>();
            for (MutableOrder mutableOrder : ordersAtPrice) {
                orders.add(mutableOrder.toOrder());
            }

            bids.add(OrderBook.PriceLevel.builder()
                .price(price)
                .volume(totalVolume)
                .orders(orders)
                .build());
        }

        // Lấy danh sách lệnh bán
        List<OrderBook.PriceLevel> asks = new ArrayList<>();
        for (Money price : snapshot.getSellPrices()) {
            List<MutableOrder> ordersAtPrice = snapshot.getSellOrdersAtPrice(price);

            BigDecimal totalVolume = BigDecimal.ZERO;
            for (MutableOrder order : ordersAtPrice) {
                totalVolume = totalVolume.add(order.getVolume().subtract(order.getDealVolume()));
            }

            List<Order> orders = new ArrayList<>();
            for (MutableOrder mutableOrder : ordersAtPrice) {
                orders.add(mutableOrder.toOrder());
            }

            asks.add(OrderBook.PriceLevel.builder()
                .price(price)
                .volume(totalVolume)
                .orders(orders)
                .build());
        }

        return OrderBook.builder()
            .symbol(symbol)
            .bids(bids)
            .asks(asks)
            .lastPrice(lastPrice)
            .markPrice(markPrice)
            .indexPrice(indexPrice)
            .build();
    }

    /**
     * Snapshot của order book
     */
    public static class OrderBookSnapshot {
        // Map lưu trữ lệnh mua theo giá
        private final Map<Money, List<MutableOrder>> buyOrders = new TreeMap<>(Collections.reverseOrder()); // Giá cao nhất đầu tiên

        // Map lưu trữ lệnh bán theo giá
        private final Map<Money, List<MutableOrder>> sellOrders = new TreeMap<>(); // Giá thấp nhất đầu tiên

        // Map lưu trữ lệnh theo ID
        private final Map<OrderId, MutableOrder> ordersById = new HashMap<>();

        /**
         * Thêm lệnh mua vào sổ lệnh
         * @param price Giá
         * @param order Lệnh
         */
        public void addBuyOrder(Money price, MutableOrder order) {
            buyOrders.computeIfAbsent(price, k -> new ArrayList<>()).add(order);
            ordersById.put(order.getOrderId(), order);
        }

        /**
         * Thêm lệnh bán vào sổ lệnh
         * @param price Giá
         * @param order Lệnh
         */
        public void addSellOrder(Money price, MutableOrder order) {
            sellOrders.computeIfAbsent(price, k -> new ArrayList<>()).add(order);
            ordersById.put(order.getOrderId(), order);
        }

        /**
         * Xóa lệnh mua khỏi sổ lệnh
         * @param orderId ID của lệnh
         * @return true nếu xóa thành công, false nếu không
         */
        public boolean removeBuyOrder(OrderId orderId) {
            MutableOrder order = ordersById.get(orderId);
            if (order == null) {
                return false;
            }

            Money price = order.getPrice();
            List<MutableOrder> ordersAtPrice = buyOrders.get(price);
            if (ordersAtPrice == null) {
                return false;
            }

            boolean removed = ordersAtPrice.remove(order);
            if (removed) {
                ordersById.remove(orderId);

                // Nếu không còn lệnh ở mức giá này, xóa mức giá
                if (ordersAtPrice.isEmpty()) {
                    buyOrders.remove(price);
                }
            }

            return removed;
        }

        /**
         * Xóa lệnh bán khỏi sổ lệnh
         * @param orderId ID của lệnh
         * @return true nếu xóa thành công, false nếu không
         */
        public boolean removeSellOrder(OrderId orderId) {
            MutableOrder order = ordersById.get(orderId);
            if (order == null) {
                return false;
            }

            Money price = order.getPrice();
            List<MutableOrder> ordersAtPrice = sellOrders.get(price);
            if (ordersAtPrice == null) {
                return false;
            }

            boolean removed = ordersAtPrice.remove(order);
            if (removed) {
                ordersById.remove(orderId);

                // Nếu không còn lệnh ở mức giá này, xóa mức giá
                if (ordersAtPrice.isEmpty()) {
                    sellOrders.remove(price);
                }
            }

            return removed;
        }

        /**
         * Xóa tất cả lệnh mua của một thành viên
         * @param memberId ID của thành viên
         * @return Danh sách các lệnh đã xóa
         */
        public List<Order> removeBuyOrdersByMemberId(Long memberId) {
            List<Order> removedOrders = new ArrayList<>();

            // Duyệt qua tất cả các mức giá
            for (Money price : new ArrayList<>(buyOrders.keySet())) {
                List<MutableOrder> ordersAtPrice = buyOrders.get(price);

                // Duyệt qua tất cả các lệnh ở mức giá này
                for (int i = 0; i < ordersAtPrice.size(); i++) {
                    MutableOrder order = ordersAtPrice.get(i);

                    // Nếu lệnh thuộc về thành viên cần xóa
                    if (order.getMemberId().equals(memberId)) {
                        // Xóa lệnh
                        ordersAtPrice.remove(i);
                        ordersById.remove(order.getOrderId());
                        removedOrders.add(order.toOrder());
                        i--; // Điều chỉnh chỉ số
                    }
                }

                // Nếu không còn lệnh ở mức giá này, xóa mức giá
                if (ordersAtPrice.isEmpty()) {
                    buyOrders.remove(price);
                }
            }

            return removedOrders;
        }

        /**
         * Xóa tất cả lệnh bán của một thành viên
         * @param memberId ID của thành viên
         * @return Danh sách các lệnh đã xóa
         */
        public List<Order> removeSellOrdersByMemberId(Long memberId) {
            List<Order> removedOrders = new ArrayList<>();

            // Duyệt qua tất cả các mức giá
            for (Money price : new ArrayList<>(sellOrders.keySet())) {
                List<MutableOrder> ordersAtPrice = sellOrders.get(price);

                // Duyệt qua tất cả các lệnh ở mức giá này
                for (int i = 0; i < ordersAtPrice.size(); i++) {
                    MutableOrder order = ordersAtPrice.get(i);

                    // Nếu lệnh thuộc về thành viên cần xóa
                    if (order.getMemberId().equals(memberId)) {
                        // Xóa lệnh
                        ordersAtPrice.remove(i);
                        ordersById.remove(order.getOrderId());
                        removedOrders.add(order.toOrder());
                        i--; // Điều chỉnh chỉ số
                    }
                }

                // Nếu không còn lệnh ở mức giá này, xóa mức giá
                if (ordersAtPrice.isEmpty()) {
                    sellOrders.remove(price);
                }
            }

            return removedOrders;
        }

        /**
         * Xóa mức giá mua
         * @param price Giá
         */
        public void removeBuyPrice(Money price) {
            List<MutableOrder> ordersAtPrice = buyOrders.remove(price);
            if (ordersAtPrice != null) {
                for (MutableOrder order : ordersAtPrice) {
                    ordersById.remove(order.getOrderId());
                }
            }
        }

        /**
         * Xóa mức giá bán
         * @param price Giá
         */
        public void removeSellPrice(Money price) {
            List<MutableOrder> ordersAtPrice = sellOrders.remove(price);
            if (ordersAtPrice != null) {
                for (MutableOrder order : ordersAtPrice) {
                    ordersById.remove(order.getOrderId());
                }
            }
        }

        /**
         * Lấy giá mua cao nhất
         * @return Giá mua cao nhất
         */
        public Money getHighestBuyPrice() {
            if (buyOrders.isEmpty()) {
                return null;
            }
            return buyOrders.keySet().iterator().next();
        }

        /**
         * Lấy giá bán thấp nhất
         * @return Giá bán thấp nhất
         */
        public Money getLowestSellPrice() {
            if (sellOrders.isEmpty()) {
                return null;
            }
            return sellOrders.keySet().iterator().next();
        }

        /**
         * Lấy danh sách lệnh mua ở mức giá
         * @param price Giá
         * @return Danh sách lệnh mua
         */
        public List<MutableOrder> getBuyOrdersAtPrice(Money price) {
            return buyOrders.getOrDefault(price, new ArrayList<>());
        }

        /**
         * Lấy danh sách lệnh bán ở mức giá
         * @param price Giá
         * @return Danh sách lệnh bán
         */
        public List<MutableOrder> getSellOrdersAtPrice(Money price) {
            return sellOrders.getOrDefault(price, new ArrayList<>());
        }

        /**
         * Lấy danh sách giá mua
         * @return Danh sách giá mua
         */
        public Set<Money> getBuyPrices() {
            return buyOrders.keySet();
        }

        /**
         * Lấy danh sách giá bán
         * @return Danh sách giá bán
         */
        public Set<Money> getSellPrices() {
            return sellOrders.keySet();
        }

        /**
         * Tạo bản sao của snapshot
         * @return Bản sao của snapshot
         */
        public OrderBookSnapshot copy() {
            OrderBookSnapshot copy = new OrderBookSnapshot();

            // Sao chép lệnh mua
            for (Map.Entry<Money, List<MutableOrder>> entry : buyOrders.entrySet()) {
                Money price = entry.getKey();
                List<MutableOrder> orders = entry.getValue();

                for (MutableOrder order : orders) {
                    copy.addBuyOrder(price, order.copy());
                }
            }

            // Sao chép lệnh bán
            for (Map.Entry<Money, List<MutableOrder>> entry : sellOrders.entrySet()) {
                Money price = entry.getKey();
                List<MutableOrder> orders = entry.getValue();

                for (MutableOrder order : orders) {
                    copy.addSellOrder(price, order.copy());
                }
            }

            return copy;
        }
    }
}
