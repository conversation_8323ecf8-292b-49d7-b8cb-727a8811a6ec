<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<!-- Application Context Name -->
	<contextName>logback-spring</contextName>

	<!-- Console Appender for Local Logs -->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
<!--			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [traceId=%X{X-B3-TraceId}, spanId=%X{X-B3-SpanId}] - %msg%n</pattern>
		</encoder>
	</appender>

	<!-- Configure the Root Logger -->
	<root level="info">
		<appender-ref ref="CONSOLE"/>
	</root>
</configuration>
