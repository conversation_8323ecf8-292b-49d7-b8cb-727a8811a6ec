package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QRedEnvelopeDetail is a Querydsl query type for RedEnvelopeDetail
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QRedEnvelopeDetail extends EntityPathBase<RedEnvelopeDetail> {

    private static final long serialVersionUID = 1126756322L;

    public static final QRedEnvelopeDetail redEnvelopeDetail = new QRedEnvelopeDetail("redEnvelopeDetail");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final NumberPath<Integer> cate = createNumber("cate", Integer.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> envelopeId = createNumber("envelopeId", Long.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final StringPath userIdentify = createString("userIdentify");

    public QRedEnvelopeDetail(String variable) {
        super(RedEnvelopeDetail.class, forVariable(variable));
    }

    public QRedEnvelopeDetail(Path<? extends RedEnvelopeDetail> path) {
        super(path.getType(), path.getMetadata());
    }

    public QRedEnvelopeDetail(PathMetadata metadata) {
        super(RedEnvelopeDetail.class, metadata);
    }

}

