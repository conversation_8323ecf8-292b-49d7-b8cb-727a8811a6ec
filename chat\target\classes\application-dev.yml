spring:
  datasource:
    driver-class-name: ${DATASOURCE_DRIVER_CLASS_NAME:org.postgresql.Driver}
    url: ${DATASOURCE_URL:********************************************}
    username: ${DATASOURCE_USERNAME:lotus_root}
    password: ${DATASOURCE_PASSWORD:Lotus12345!}

  jpa:
    properties:
      hibernate:
        default_schema: ${DEFAULT_SCHEMA:bizzan}

  redis:
    host: ${REDIS_HOST:************}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:2m0881Xc30Wh}

  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:465}
    properties:
      mail.smtp:
        socketFactory.class: javax.net.ssl.SSLSocketFactory
        auth: true
        starttls:
          enable: true
          required: true
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:erlzeziorjuccpvx}

  data:
    mongodb:
      uri: ${SPRING_MONGODB_URI:****************************************************}

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:9092}
    properties:
      #      security.protocol: SASL_PLAINTEXT
      sasl.mechanism: PLAIN
      #      sasl.jaas.config: >-
      #        org.apache.kafka.common.security.plain.PlainLoginModule required username=""
      #        password="2m0881Xc30Wh";

  cloud:
    consul:
      host: ${CONSUL_HOST:************}
      port: ${CONSUL_PORT:8500}

  zipkin:
    base-url: http://************:9411
    enabled: true
    sender:
      type: web

  sleuth:
    sampler:
      percentage: 1.0
      probability: 1.0
    enabled: true
