package com.icetea.lotus.infrastructure.persistence.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Utility class để validate và xử lý BigDecimal cho PostgreSQL NUMERIC constraints
 */
@Slf4j
public class BigDecimalValidator {

    // Giới hạn cho NUMERIC(18,8): phải nhỏ hơn 10^10 (không bằng)
    // PostgreSQL yêu cầu "absolute value less than 10^10"
    // 10^10 = 10000000000, nên max value phải < 10000000000
    // Đ<PERSON> an toàn, sử dụng 9999999999.00000000 thay vì 9999999999.99999999
    private static final BigDecimal MAX_VALUE_18_8 = new BigDecimal("9999999999.00000000"); // An toàn < 10^10
    private static final BigDecimal MIN_VALUE_18_8 = new BigDecimal("-9999999999.00000000");

    // Giới hạn cho NUMERIC(26,16): tối đa 10 chữ số trước dấu phẩy, 16 chữ số sau dấu phẩy
    private static final BigDecimal MAX_VALUE_26_16 = new BigDecimal("9999999999.9999999999999999"); // 10^10 - 1
    private static final BigDecimal MIN_VALUE_26_16 = new BigDecimal("-9999999999.9999999999999999");

    /**
     * Validate và scale BigDecimal để phù hợp với NUMERIC(18,8)
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @return Giá trị đã được validate và scale
     */
    public static BigDecimal validateAndScale(BigDecimal value, String fieldName) {
        return validateAndScale(value, fieldName, 18, 8);
    }

    /**
     * Validate và scale BigDecimal để phù hợp với NUMERIC(26,16)
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @return Giá trị đã được validate và scale
     */
    public static BigDecimal validateAndScale26_16(BigDecimal value, String fieldName) {
        return validateAndScale(value, fieldName, 26, 16);
    }

    /**
     * Validate và scale BigDecimal với precision và scale tùy chỉnh
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @param precision Tổng số chữ số
     * @param scale Số chữ số thập phân
     * @return Giá trị đã được validate và scale
     */
    public static BigDecimal validateAndScale(BigDecimal value, String fieldName, int precision, int scale) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal maxValue, minValue;

        if (precision == 18 && scale == 8) {
            maxValue = MAX_VALUE_18_8;
            minValue = MIN_VALUE_18_8;
        } else if (precision == 26 && scale == 16) {
            maxValue = MAX_VALUE_26_16;
            minValue = MIN_VALUE_26_16;
        } else {
            // Tính toán động cho precision/scale khác
            int integerDigits = precision - scale;
            StringBuilder maxBuilder = new StringBuilder();
            for (int i = 0; i < integerDigits; i++) {
                maxBuilder.append("9");
            }
            if (scale > 0) {
                maxBuilder.append(".");
                for (int i = 0; i < scale; i++) {
                    maxBuilder.append("9");
                }
            }
            maxValue = new BigDecimal(maxBuilder.toString());
            minValue = maxValue.negate();
        }

        if (value.compareTo(maxValue) > 0) {
            log.warn("Value {} for field {} exceeds maximum limit for NUMERIC({},{}), capping to {}",
                    value, fieldName, precision, scale, maxValue);
            return maxValue;
        }

        if (value.compareTo(minValue) < 0) {
            log.warn("Value {} for field {} below minimum limit for NUMERIC({},{}), capping to {}",
                    value, fieldName, precision, scale, minValue);
            return minValue;
        }

        // Scale về số chữ số thập phân chỉ định
        return value.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * Validate strict - throw exception nếu vượt quá giới hạn
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @return Giá trị đã được validate và scale
     * @throws IllegalArgumentException nếu giá trị vượt quá giới hạn
     */
    public static BigDecimal validateAndScaleStrict(BigDecimal value, String fieldName) {
        return validateAndScaleStrict(value, fieldName, 18, 8);
    }

    /**
     * Validate strict với precision và scale tùy chỉnh
     * @param value Giá trị cần validate
     * @param fieldName Tên field để log lỗi
     * @param precision Tổng số chữ số
     * @param scale Số chữ số thập phân
     * @return Giá trị đã được validate và scale
     * @throws IllegalArgumentException nếu giá trị vượt quá giới hạn
     */
    public static BigDecimal validateAndScaleStrict(BigDecimal value, String fieldName, int precision, int scale) {
        if (value == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal maxValue, minValue;

        if (precision == 18 && scale == 8) {
            maxValue = MAX_VALUE_18_8;
            minValue = MIN_VALUE_18_8;
        } else if (precision == 26 && scale == 16) {
            maxValue = MAX_VALUE_26_16;
            minValue = MIN_VALUE_26_16;
        } else {
            // Tính toán động cho precision/scale khác
            int integerDigits = precision - scale;
            StringBuilder maxBuilder = new StringBuilder();
            for (int i = 0; i < integerDigits; i++) {
                maxBuilder.append("9");
            }
            if (scale > 0) {
                maxBuilder.append(".");
                for (int i = 0; i < scale; i++) {
                    maxBuilder.append("9");
                }
            }
            maxValue = new BigDecimal(maxBuilder.toString());
            minValue = maxValue.negate();
        }

        if (value.compareTo(maxValue) > 0 || value.compareTo(minValue) < 0) {
            throw new IllegalArgumentException(
                    String.format("Value %s for field %s exceeds NUMERIC(%d,%d) limits [%s, %s]",
                            value, fieldName, precision, scale, minValue, maxValue));
        }

        return value.setScale(scale, RoundingMode.HALF_UP);
    }

    /**
     * Kiểm tra xem giá trị có vượt quá giới hạn NUMERIC(18,8) không
     * @param value Giá trị cần kiểm tra
     * @return true nếu vượt quá giới hạn
     */
    public static boolean exceedsLimit(BigDecimal value) {
        return exceedsLimit(value, 18, 8);
    }

    /**
     * Kiểm tra xem giá trị có vượt quá giới hạn NUMERIC với precision/scale tùy chỉnh không
     * @param value Giá trị cần kiểm tra
     * @param precision Tổng số chữ số
     * @param scale Số chữ số thập phân
     * @return true nếu vượt quá giới hạn
     */
    public static boolean exceedsLimit(BigDecimal value, int precision, int scale) {
        if (value == null) {
            return false;
        }

        BigDecimal maxValue, minValue;

        if (precision == 18 && scale == 8) {
            maxValue = MAX_VALUE_18_8;
            minValue = MIN_VALUE_18_8;
        } else if (precision == 26 && scale == 16) {
            maxValue = MAX_VALUE_26_16;
            minValue = MIN_VALUE_26_16;
        } else {
            // Tính toán động cho precision/scale khác
            int integerDigits = precision - scale;
            StringBuilder maxBuilder = new StringBuilder();
            for (int i = 0; i < integerDigits; i++) {
                maxBuilder.append("9");
            }
            if (scale > 0) {
                maxBuilder.append(".");
                for (int i = 0; i < scale; i++) {
                    maxBuilder.append("9");
                }
            }
            maxValue = new BigDecimal(maxBuilder.toString());
            minValue = maxValue.negate();
        }

        return value.compareTo(maxValue) > 0 || value.compareTo(minValue) < 0;
    }
}
