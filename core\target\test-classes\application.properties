server.port=8001
server.context-path=/timejob
spring.session.store-type=none
#================================start====================================================================
#datasource
spring.datasource.url=**************************************************************************************
spring.datasource.username=root
spring.datasource.password=asd1230
#mongodb
spring.data.mongodb.uri=**************************************************

#===================================end===================================================================
#kafka
spring.kafka.bootstrap-servers=*************:9092,*************:19092,3*************:29092
# \u91CD\u4F20\u6B21\u6570
spring.kafka.producer.retries=0
# \u6BCF\u6B21\u6279\u5904\u7406\u7684\u5927\u5C0F
spring.kafka.producer.batch.size=256
#linger\u6307\u5B9A\u7684\u65F6\u95F4\u7B49\u5F85\u66F4\u591A\u7684records\u51FA\u73B0
spring.kafka.producer.linger=1
# \u7F13\u5B58\u6570\u636E\u7684\u5185\u5B58\u5927\u5C0F
spring.kafka.producer.buffer.memory=1048576
spring.kafka.consumer.enable.auto.commit=false
spring.kafka.consumer.session.timeout=15000
spring.kafka.consumer.auto.commit.interval=100
spring.kafka.consumer.auto.offset.reset=earliest
spring.kafka.consumer.group.id=default-group
spring.kafka.consumer.concurrency=9
spring.kafka.consumer.maxPollRecordsConfig=50
spring.devtools.restart.enabled=true
#datasource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.initialSize=5
spring.datasource.minIdle=5
spring.datasource.maxActive=200
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat,wall,log4j


#ES\u914D\u7F6E\u6587\u4EF6
es.username=elastic
es.password=guo#2fGf@WE&
es.mine.index=exchange_order_transaction_mine
es.mine.type=transaction
es.public.ip=es-cn-0pp0pbvsh0006ctyy.public.elasticsearch.aliyuncs.com
es.private.ip=es-cn-0pp0pbvsh0006ctyy.elasticsearch.aliyuncs.com
es.port=9200

#redis47.90.121.145
redis.host=*************
redis.password=asd1230
redis.port=6379
redis.timeout=1800

#json
jackson.serialization.indent_output=true
jackson.date-format=yyyy-MM-dd HH:mm:ss
jackson.time-zone=GMT+8
