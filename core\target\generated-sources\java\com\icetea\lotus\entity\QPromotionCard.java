package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QPromotionCard is a Querydsl query type for PromotionCard
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QPromotionCard extends EntityPathBase<PromotionCard> {

    private static final long serialVersionUID = -501809405L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QPromotionCard promotionCard = new QPromotionCard("promotionCard");

    public final NumberPath<java.math.BigDecimal> amount = createNumber("amount", java.math.BigDecimal.class);

    public final StringPath cardDesc = createString("cardDesc");

    public final StringPath cardName = createString("cardName");

    public final StringPath cardNo = createString("cardNo");

    public final QCoin coin;

    public final NumberPath<Integer> count = createNumber("count", Integer.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Integer> exchangeCount = createNumber("exchangeCount", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isEnabled = createNumber("isEnabled", Integer.class);

    public final NumberPath<Integer> isFree = createNumber("isFree", Integer.class);

    public final NumberPath<Integer> isLock = createNumber("isLock", Integer.class);

    public final NumberPath<Integer> lockDays = createNumber("lockDays", Integer.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<java.math.BigDecimal> totalAmount = createNumber("totalAmount", java.math.BigDecimal.class);

    public QPromotionCard(String variable) {
        this(PromotionCard.class, forVariable(variable), INITS);
    }

    public QPromotionCard(Path<? extends PromotionCard> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QPromotionCard(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QPromotionCard(PathMetadata metadata, PathInits inits) {
        this(PromotionCard.class, metadata, inits);
    }

    public QPromotionCard(Class<? extends PromotionCard> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.coin = inits.isInitialized("coin") ? new QCoin(forProperty("coin")) : null;
    }

}

