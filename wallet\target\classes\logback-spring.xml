<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<contextName>logback</contextName>
	<!--Output to console -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n </pattern>
		</encoder>
	</appender>

	<root level="info">
		<appender-ref ref="console" />
	</root>
</configuration>