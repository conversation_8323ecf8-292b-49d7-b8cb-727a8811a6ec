package com.icetea.lotus.infrastructure.matching;

import com.icetea.lotus.core.domain.entity.Contract;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Position;
import com.icetea.lotus.core.domain.entity.PositionDirection;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.OrderId;
import com.icetea.lotus.core.domain.valueobject.PositionId;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.domain.valueobject.TradeId;
import com.icetea.lotus.core.domain.entity.MatchingAlgorithm;
import com.icetea.lotus.core.common.LogMessages;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Lock-Free Matching Engine sử dụng các cấu trúc dữ liệu không khóa để tránh tình trạng nghẽn cổ chai
 * khi nhiều thread cùng truy cập.
 */
@Slf4j
@ToString
public class LockFreeMatchingEngine {

    private final Symbol symbol;
    private final Contract contract;

    // Sử dụng AtomicReference để lưu trữ snapshot hiện tại của sổ lệnh
    private final AtomicReference<OrderBookSnapshot> orderBookRef = new AtomicReference<>(new OrderBookSnapshot());

    // Thuật toán khớp lệnh
    @Getter
    private volatile MatchingAlgorithm matchingAlgorithm = MatchingAlgorithm.FIFO;

    // Trạng thái giao dịch
    @Getter
    private volatile boolean tradingEnabled = true;

    // Giá đánh dấu, giá chỉ số, giá giao dịch cuối cùng
    @Getter
    private volatile Money markPrice = Money.of(BigDecimal.ZERO);

    @Getter
    private volatile Money indexPrice = Money.of(BigDecimal.ZERO);

    @Getter
    private volatile Money lastPrice = Money.of(BigDecimal.ZERO);

    // Các vị thế cần kiểm tra thanh lý
    private final Map<String, Position> positionsToCheck = new ConcurrentHashMap<>();

    /**
     * Khởi tạo Lock-Free Matching Engine
     * @param symbol Symbol của hợp đồng
     * @param contract Hợp đồng
     */
    public LockFreeMatchingEngine(Symbol symbol, Contract contract) {
        this.symbol = symbol;
        this.contract = contract;
    }

    /**
     * Lớp nội bộ đại diện cho snapshot của sổ lệnh tại một thời điểm
     */
    public static class OrderBookSnapshot {
        // Sử dụng Red-Black Tree để lưu trữ các lệnh theo giá
        private OrderBookRedBlackTree orderBook;

        // Map lưu trữ tất cả các lệnh theo ID
        private final Map<String, Order> allOrders;

        // Danh sách các lệnh chờ (stop orders)
        private final CopyOnWriteArrayList<Order> stopOrders;

        /**
         * Khởi tạo OrderBookSnapshot
         */
        public OrderBookSnapshot() {
            this.orderBook = new OrderBookRedBlackTree();
            this.allOrders = new ConcurrentHashMap<>();
            this.stopOrders = new CopyOnWriteArrayList<>();
        }

        /**
         * Tạo bản sao của snapshot hiện tại
         * @return Bản sao của snapshot
         */
        public OrderBookSnapshot copy() {
            OrderBookSnapshot copy = new OrderBookSnapshot();

            // Sao chép Red-Black Tree
            copy.orderBook = this.orderBook.copy();

            // Sao chép tất cả các lệnh
            copy.allOrders.putAll(this.allOrders);

            // Sao chép các lệnh chờ
            copy.stopOrders.addAll(this.stopOrders);

            return copy;
        }



        /**
         * Thêm lệnh mua vào snapshot
         * @param price Giá
         * @param order Lệnh
         */
        public void addBuyOrder(Money price, Order order) {
            orderBook.addBuyOrder(price, order);
        }

        /**
         * Thêm lệnh bán vào snapshot
         * @param price Giá
         * @param order Lệnh
         */
        public void addSellOrder(Money price, Order order) {
            orderBook.addSellOrder(price, order);
        }

        /**
         * Xóa lệnh mua khỏi snapshot
         * @param price Giá
         * @param order Lệnh
         * @return true nếu xóa thành công, false nếu không
         */
        public boolean removeBuyOrder(Money price, Order order) {
            return orderBook.removeBuyOrder(price, order);
        }

        /**
         * Xóa lệnh bán khỏi snapshot
         * @param price Giá
         * @param order Lệnh
         * @return true nếu xóa thành công, false nếu không
         */
        public boolean removeSellOrder(Money price, Order order) {
            return orderBook.removeSellOrder(price, order);
        }

        /**
         * Lấy entry đầu tiên của cây lệnh mua
         * @return Entry đầu tiên, hoặc null nếu cây rỗng
         */
        public Map.Entry<Money, CopyOnWriteArrayList<Order>> firstBuyEntry() {
            return orderBook.firstBuyEntry();
        }

        /**
         * Lấy entry đầu tiên của cây lệnh bán
         * @return Entry đầu tiên, hoặc null nếu cây rỗng
         */
        public Map.Entry<Money, CopyOnWriteArrayList<Order>> firstSellEntry() {
            return orderBook.firstSellEntry();
        }

        /**
         * Kiểm tra xem cây lệnh mua có rỗng không
         * @return true nếu cây rỗng, false nếu không
         */
        public boolean isBuyOrdersEmpty() {
            return orderBook.isBuyOrdersEmpty();
        }

        /**
         * Kiểm tra xem cây lệnh bán có rỗng không
         * @return true nếu cây rỗng, false nếu không
         */
        public boolean isSellOrdersEmpty() {
            return orderBook.isSellOrdersEmpty();
        }

        /**
         * Lấy danh sách các entry của cây lệnh mua
         * @return Danh sách các entry
         */
        public List<Map.Entry<Money, CopyOnWriteArrayList<Order>>> buyEntryList() {
            return orderBook.buyEntryList();
        }

        /**
         * Lấy danh sách các entry của cây lệnh bán
         * @return Danh sách các entry
         */
        public List<Map.Entry<Money, CopyOnWriteArrayList<Order>>> sellEntryList() {
            return orderBook.sellEntryList();
        }

        /**
         * Xóa một mức giá khỏi cây lệnh mua
         * @param price Giá
         * @return Danh sách lệnh trước đó ở mức giá này, hoặc null nếu không có
         */
        public CopyOnWriteArrayList<Order> removeBuyPrice(Money price) {
            return orderBook.removeBuyPrice(price);
        }

        /**
         * Xóa một mức giá khỏi cây lệnh bán
         * @param price Giá
         * @return Danh sách lệnh trước đó ở mức giá này, hoặc null nếu không có
         */
        public CopyOnWriteArrayList<Order> removeSellPrice(Money price) {
            return orderBook.removeSellPrice(price);
        }
    }

    /**
     * Xử lý lệnh mới
     * @param order Lệnh cần xử lý
     * @return Danh sách các giao dịch được tạo ra
     */
    public List<Trade> processOrder(Order order) {
        // Kiểm tra xem giao dịch có được bật không
        if (!tradingEnabled) {
            log.info(LogMessages.OrderMatching.INFO_TRADING_PAUSED(), symbol);
            return Collections.emptyList();
        }

        // Kiểm tra xem lệnh có phải là lệnh chờ không
        if (isStopOrder(order)) {
            log.debug("Xử lý lệnh chờ: {}, symbol: {}", order.getOrderId(), symbol);
            return handleStopOrder(order);
        }

        log.debug("Bắt đầu khớp lệnh: {}, symbol: {}, thuật toán: {}",
                order.getOrderId(), symbol, matchingAlgorithm);

        // Xử lý lệnh theo cơ chế lock-free
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Danh sách giao dịch được tạo ra
            List<Trade> trades = new ArrayList<>();

            // Khớp lệnh theo thuật toán đã chọn
            switch (matchingAlgorithm) {
                case FIFO:
                    matchOrderFIFO(order, newSnapshot, trades);
                    break;
                case PRO_RATA:
                    matchOrderProRata(order, newSnapshot, trades);
                    break;
                case HYBRID:
                    matchOrderHybrid(order, newSnapshot, trades);
                    break;
                default:
                    matchOrderFIFO(order, newSnapshot, trades);
                    break;
            }

            // Cập nhật giá giao dịch cuối cùng nếu có giao dịch
            if (!trades.isEmpty()) {
                lastPrice = trades.get(trades.size() - 1).getPrice();
            }

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return trades;
            }

            // Nếu CAS thất bại, thử lại
            log.debug("CAS thất bại, thử lại khớp lệnh: {}", order.getOrderId());
        }
    }

    /**
     * Kiểm tra xem lệnh có phải là lệnh chờ không
     * @param order Lệnh cần kiểm tra
     * @return true nếu là lệnh chờ, false nếu không phải
     */
    private boolean isStopOrder(Order order) {
        return order.getType() == com.icetea.lotus.core.domain.entity.OrderType.STOP_LOSS ||
               order.getType() == com.icetea.lotus.core.domain.entity.OrderType.STOP_LOSS_LIMIT;
    }

    /**
     * Xử lý lệnh chờ
     * @param order Lệnh chờ cần xử lý
     * @return Danh sách các giao dịch được tạo ra
     */
    private List<Trade> handleStopOrder(Order order) {
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Thêm lệnh vào danh sách lệnh chờ
            newSnapshot.stopOrders.add(order);

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return Collections.emptyList();
            }

            // Nếu CAS thất bại, thử lại
            log.debug("CAS thất bại, thử lại xử lý lệnh chờ: {}", order.getOrderId());
        }
    }

    /**
     * Khớp lệnh theo thuật toán FIFO
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của sổ lệnh
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderFIFO(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        // Xác định hướng lệnh và lấy danh sách lệnh đối ứng
        boolean isBuyOrder = order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY;

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = order.getVolume();

        // Thêm lệnh vào sổ lệnh
        addOrderToSnapshot(order, snapshot);

        // Khớp lệnh với các lệnh đối ứng
        while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 &&
               (isBuyOrder ? !snapshot.isSellOrdersEmpty() : !snapshot.isBuyOrdersEmpty())) {
            // Lấy mức giá tốt nhất
            Map.Entry<Money, CopyOnWriteArrayList<Order>> bestPriceEntry =
                    isBuyOrder ? snapshot.firstSellEntry() : snapshot.firstBuyEntry();

            if (bestPriceEntry == null) {
                break;
            }

            Money bestPrice = bestPriceEntry.getKey();
            CopyOnWriteArrayList<Order> ordersAtBestPrice = bestPriceEntry.getValue();

            // Kiểm tra điều kiện giá
            if (!isPriceAcceptable(order, bestPrice, isBuyOrder)) {
                break;
            }

            // Khớp lệnh với các lệnh ở mức giá tốt nhất theo thứ tự thời gian (FIFO)
            for (int i = 0; i < ordersAtBestPrice.size() && remainingVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
                Order oppositeOrder = ordersAtBestPrice.get(i);

                // Khối lượng còn lại của lệnh đối ứng
                BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                        .subtract(oppositeOrder.getDealVolume());

                if (oppositeRemainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // Khối lượng khớp
                BigDecimal matchedVolume = remainingVolume.min(oppositeRemainingVolume);

                // Tạo giao dịch
                Trade trade = createTrade(order, oppositeOrder, bestPrice, matchedVolume);
                trades.add(trade);

                // Cập nhật khối lượng đã khớp
                updateOrderDealVolume(order, matchedVolume, snapshot);
                updateOrderDealVolume(oppositeOrder, matchedVolume, snapshot);

                // Cập nhật khối lượng còn lại
                remainingVolume = remainingVolume.subtract(matchedVolume);

                // Nếu lệnh đối ứng đã khớp hết, xóa khỏi sổ lệnh
                if (oppositeOrder.getDealVolume().add(matchedVolume)
                        .compareTo(oppositeOrder.getVolume()) >= 0) {
                    if (isBuyOrder) {
                        snapshot.removeSellOrder(bestPrice, oppositeOrder);
                    } else {
                        snapshot.removeBuyOrder(bestPrice, oppositeOrder);
                    }
                    i--; // Điều chỉnh chỉ số do xóa phần tử
                }
            }

            // Nếu không còn lệnh ở mức giá này, xóa mức giá
            if (ordersAtBestPrice.isEmpty()) {
                if (isBuyOrder) {
                    snapshot.removeSellPrice(bestPrice);
                } else {
                    snapshot.removeBuyPrice(bestPrice);
                }
            }
        }

        // Nếu lệnh đã khớp hết, xóa khỏi sổ lệnh
        if (order.getDealVolume().add(remainingVolume)
                .compareTo(order.getVolume()) >= 0) {
            removeOrderFromSnapshot(order, snapshot);
        }
    }

    /**
     * Khớp lệnh theo thuật toán Pro-Rata
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của sổ lệnh
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderProRata(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        // Xác định hướng lệnh
        boolean isBuyOrder = order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY;

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = order.getVolume();

        // Thêm lệnh vào sổ lệnh
        addOrderToSnapshot(order, snapshot);

        // Khớp lệnh với các lệnh đối ứng
        while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 &&
               (isBuyOrder ? !snapshot.isSellOrdersEmpty() : !snapshot.isBuyOrdersEmpty())) {
            // Lấy mức giá tốt nhất
            Map.Entry<Money, CopyOnWriteArrayList<Order>> bestPriceEntry =
                    isBuyOrder ? snapshot.firstSellEntry() : snapshot.firstBuyEntry();

            if (bestPriceEntry == null) {
                break;
            }

            Money bestPrice = bestPriceEntry.getKey();
            CopyOnWriteArrayList<Order> ordersAtBestPrice = bestPriceEntry.getValue();

            // Kiểm tra điều kiện giá
            if (!isPriceAcceptable(order, bestPrice, isBuyOrder)) {
                break;
            }

            // Tính tổng khối lượng ở mức giá tốt nhất
            BigDecimal totalVolumeAtBestPrice = BigDecimal.ZERO;
            for (Order oppositeOrder : ordersAtBestPrice) {
                BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                        .subtract(oppositeOrder.getDealVolume());
                totalVolumeAtBestPrice = totalVolumeAtBestPrice.add(oppositeRemainingVolume);
            }

            // Khớp lệnh với các lệnh ở mức giá tốt nhất theo tỷ lệ khối lượng (Pro-Rata)
            for (Order oppositeOrder : new ArrayList<>(ordersAtBestPrice)) {
                // Khối lượng còn lại của lệnh đối ứng
                BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                        .subtract(oppositeOrder.getDealVolume());

                if (oppositeRemainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // Tính khối lượng khớp theo tỷ lệ
                BigDecimal ratio = oppositeRemainingVolume.divide(totalVolumeAtBestPrice, 8, RoundingMode.DOWN);
                BigDecimal matchedVolume = remainingVolume.multiply(ratio).setScale(8, RoundingMode.DOWN);

                // Đảm bảo khối lượng khớp không vượt quá khối lượng còn lại của lệnh đối ứng
                matchedVolume = matchedVolume.min(oppositeRemainingVolume);

                if (matchedVolume.compareTo(BigDecimal.ZERO) > 0) {
                    // Tạo giao dịch
                    Trade trade = createTrade(order, oppositeOrder, bestPrice, matchedVolume);
                    trades.add(trade);

                    // Cập nhật khối lượng đã khớp
                    updateOrderDealVolume(order, matchedVolume, snapshot);
                    updateOrderDealVolume(oppositeOrder, matchedVolume, snapshot);

                    // Cập nhật khối lượng còn lại
                    remainingVolume = remainingVolume.subtract(matchedVolume);

                    // Nếu lệnh đối ứng đã khớp hết, xóa khỏi sổ lệnh
                    if (oppositeOrder.getDealVolume().add(matchedVolume)
                            .compareTo(oppositeOrder.getVolume()) >= 0) {
                        if (isBuyOrder) {
                            snapshot.removeSellOrder(bestPrice, oppositeOrder);
                        } else {
                            snapshot.removeBuyOrder(bestPrice, oppositeOrder);
                        }
                    }

                    // Nếu đã khớp hết khối lượng, thoát vòng lặp
                    if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }
            }

            // Nếu không còn lệnh ở mức giá này, xóa mức giá
            if (ordersAtBestPrice.isEmpty()) {
                if (isBuyOrder) {
                    snapshot.removeSellPrice(bestPrice);
                } else {
                    snapshot.removeBuyPrice(bestPrice);
                }
            }
        }

        // Nếu lệnh đã khớp hết, xóa khỏi sổ lệnh
        if (order.getDealVolume().add(remainingVolume)
                .compareTo(order.getVolume()) >= 0) {
            removeOrderFromSnapshot(order, snapshot);
        }
    }

    /**
     * Khớp lệnh theo thuật toán Hybrid (kết hợp FIFO và Pro-Rata)
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của sổ lệnh
     * @param trades Danh sách giao dịch được tạo ra
     */
    /**
     * Thêm lệnh vào snapshot
     * @param order Lệnh cần thêm
     * @param snapshot Snapshot của sổ lệnh
     */
    private void addOrderToSnapshot(Order order, OrderBookSnapshot snapshot) {
        // Thêm lệnh vào map allOrders
        snapshot.allOrders.put(order.getOrderId().getValue(), order);

        // Nếu là lệnh thị trường (MARKET), không cần thêm vào OrderBookRedBlackTree
        if (order.getType() == com.icetea.lotus.core.domain.entity.OrderType.MARKET) {
            return;
        }

        // Kiểm tra giá không null trước khi thêm vào OrderBookRedBlackTree
        if (order.getPrice() == null) {
            log.warn("Không thể thêm lệnh vào OrderBookRedBlackTree vì giá là null: {}", order.getOrderId());
            return;
        }

        // Thêm lệnh vào OrderBookRedBlackTree theo hướng
        if (order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY) {
            snapshot.addBuyOrder(order.getPrice(), order);
        } else {
            snapshot.addSellOrder(order.getPrice(), order);
        }
    }

    /**
     * Xóa lệnh khỏi snapshot
     * @param order Lệnh cần xóa
     * @param snapshot Snapshot của sổ lệnh
     */
    private void removeOrderFromSnapshot(Order order, OrderBookSnapshot snapshot) {
        // Xóa lệnh khỏi map allOrders
        snapshot.allOrders.remove(order.getOrderId().getValue());

        // Nếu là lệnh thị trường (MARKET), không cần xóa khỏi OrderBookRedBlackTree
        if (order.getType() == com.icetea.lotus.core.domain.entity.OrderType.MARKET) {
            return;
        }

        // Kiểm tra giá không null trước khi xóa khỏi OrderBookRedBlackTree
        if (order.getPrice() == null) {
            log.warn("Không thể xóa lệnh khỏi OrderBookRedBlackTree vì giá là null: {}", order.getOrderId());
            return;
        }

        // Xóa lệnh khỏi OrderBookRedBlackTree theo hướng
        if (order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY) {
            snapshot.removeBuyOrder(order.getPrice(), order);
        } else {
            snapshot.removeSellOrder(order.getPrice(), order);
        }
    }

    /**
     * Cập nhật khối lượng đã khớp của lệnh
     * @param order Lệnh cần cập nhật
     * @param matchedVolume Khối lượng đã khớp
     * @param snapshot Snapshot của sổ lệnh
     */
    private void updateOrderDealVolume(Order order, BigDecimal matchedVolume, OrderBookSnapshot snapshot) {
        // Cập nhật khối lượng đã khớp
        BigDecimal newDealVolume = order.getDealVolume().add(matchedVolume);

        // Tạo lệnh mới với khối lượng đã khớp mới
        Order.OrderBuilder builder = Order.builder()
                .orderId(order.getOrderId())
                .memberId(order.getMemberId())
                .symbol(order.getSymbol())
                .direction(order.getDirection())
                .type(order.getType())
                .volume(order.getVolume())
                .dealVolume(newDealVolume)
                .status(newDealVolume.compareTo(order.getVolume()) >= 0 ?
                        com.icetea.lotus.core.domain.entity.OrderStatus.FILLED :
                        com.icetea.lotus.core.domain.entity.OrderStatus.PARTIALLY_FILLED)
                .createTime(order.getCreateTime());

        // Chỉ đặt giá nếu không phải lệnh thị trường hoặc giá không null
        if (order.getType() != com.icetea.lotus.core.domain.entity.OrderType.MARKET && order.getPrice() != null) {
            builder.price(order.getPrice());
        }

        Order updatedOrder = builder.build();

        // Cập nhật lệnh trong snapshot
        snapshot.allOrders.put(order.getOrderId().getValue(), updatedOrder);

        // Nếu là lệnh thị trường, không cần cập nhật OrderBookRedBlackTree
        if (order.getType() == com.icetea.lotus.core.domain.entity.OrderType.MARKET) {
            return;
        }

        // Kiểm tra giá không null trước khi cập nhật OrderBookRedBlackTree
        if (order.getPrice() == null || updatedOrder.getPrice() == null) {
            log.warn("Không thể cập nhật lệnh trong OrderBookRedBlackTree vì giá là null: {}", order.getOrderId());
            return;
        }

        // Xóa lệnh cũ và thêm lệnh mới vào OrderBookRedBlackTree
        if (order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY) {
            snapshot.removeBuyOrder(order.getPrice(), order);
            snapshot.addBuyOrder(updatedOrder.getPrice(), updatedOrder);
        } else {
            snapshot.removeSellOrder(order.getPrice(), order);
            snapshot.addSellOrder(updatedOrder.getPrice(), updatedOrder);
        }
    }

    /**
     * Tạo giao dịch mới
     * @param order Lệnh gốc
     * @param oppositeOrder Lệnh đối ứng
     * @param price Giá khớp
     * @param volume Khối lượng khớp
     * @return Giao dịch mới
     */
    private Trade createTrade(Order order, Order oppositeOrder, Money price, BigDecimal volume) {
        // Xác định lệnh mua và lệnh bán
        Order buyOrder = order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY ? order : oppositeOrder;
        Order sellOrder = order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.SELL ? order : oppositeOrder;

        // Tạo ID giao dịch mới
        TradeId tradeId = TradeId.of(UUID.randomUUID().toString());

        // Tạo giao dịch mới
        return Trade.builder()
                .id(tradeId)
                .symbol(order.getSymbol())
                .buyOrderId(buyOrder.getOrderId())
                .sellOrderId(sellOrder.getOrderId())
                .buyMemberId(buyOrder.getMemberId())
                .sellMemberId(sellOrder.getMemberId())
                .price(price)
                .volume(volume)
                .buyFee(Money.of(calculateFee(price, volume)))
                .sellFee(Money.of(calculateFee(price, volume)))
                .tradeTime(LocalDateTime.now())
                .buyOrderType(buyOrder.getType())
                .sellOrderType(sellOrder.getType())
                .leverage(order.getLeverage()) // ✅ FIX: Thêm leverage từ order
                .build();
    }

    /**
     * Tính phí giao dịch
     * @param price Giá khớp
     * @param volume Khối lượng khớp
     * @return Phí giao dịch
     */
    private BigDecimal calculateFee(Money price, BigDecimal volume) {
        // Tính phí giao dịch (ví dụ: 0.1%)
        BigDecimal feeRate = new BigDecimal("0.001");
        return price.getValue().multiply(volume).multiply(feeRate);
    }

    /**
     * Cập nhật giá đánh dấu
     * @param markPrice Giá đánh dấu mới
     */
    public void updateMarkPrice(Money markPrice) {
        this.markPrice = markPrice;

        // Kiểm tra các lệnh chờ
        checkTriggerOrders();

        // Kiểm tra các vị thế cần thanh lý
        checkPositionsToLiquidate();
    }

    /**
     * Cập nhật giá chỉ số
     * @param indexPrice Giá chỉ số mới
     */
    public void updateIndexPrice(Money indexPrice) {
        this.indexPrice = indexPrice;
    }

    /**
     * Kiểm tra các lệnh chờ
     */
    private void checkTriggerOrders() {
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Danh sách lệnh chờ cần kích hoạt
            List<Order> ordersToTrigger = new ArrayList<>();

            // Kiểm tra các lệnh chờ
            for (Order stopOrder : newSnapshot.stopOrders) {
                if (isStopOrderTriggered(stopOrder)) {
                    ordersToTrigger.add(stopOrder);
                }
            }

            // Nếu không có lệnh chờ nào cần kích hoạt, thoát
            if (ordersToTrigger.isEmpty()) {
                return;
            }

            // Xóa các lệnh chờ đã kích hoạt khỏi danh sách
            for (Order order : ordersToTrigger) {
                newSnapshot.stopOrders.remove(order);
            }

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                // Xử lý các lệnh chờ đã kích hoạt
                for (Order order : ordersToTrigger) {
                    // Chuyển đổi lệnh chờ thành lệnh thường
                    Order triggeredOrder = convertStopOrderToRegularOrder(order);

                    // Xử lý lệnh
                    processOrder(triggeredOrder);
                }

                return;
            }

            // Nếu CAS thất bại, thử lại
            System.out.println("CAS thất bại, thử lại kiểm tra lệnh chờ");
        }
    }

    /**
     * Kiểm tra xem lệnh chờ có được kích hoạt không
     * @param stopOrder Lệnh chờ
     * @return true nếu lệnh chờ được kích hoạt, false nếu không
     */
    private boolean isStopOrderTriggered(Order stopOrder) {
        // Nếu không có giá đánh dấu, không kích hoạt
        if (markPrice == null || markPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // Lệnh dừng lỗ mua: kích hoạt khi giá đánh dấu >= giá kích hoạt
        if (stopOrder.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY) {
            return markPrice.getValue().compareTo(stopOrder.getTriggerPrice().getValue()) >= 0;
        }

        // Lệnh dừng lỗ bán: kích hoạt khi giá đánh dấu <= giá kích hoạt
        return markPrice.getValue().compareTo(stopOrder.getTriggerPrice().getValue()) <= 0;
    }

    /**
     * Chuyển đổi lệnh chờ thành lệnh thường
     * @param stopOrder Lệnh chờ
     * @return Lệnh thường
     */
    private Order convertStopOrderToRegularOrder(Order stopOrder) {
        com.icetea.lotus.core.domain.entity.OrderType newType =
                stopOrder.getType() == com.icetea.lotus.core.domain.entity.OrderType.STOP_LOSS_LIMIT ?
                com.icetea.lotus.core.domain.entity.OrderType.LIMIT :
                com.icetea.lotus.core.domain.entity.OrderType.MARKET;

        return Order.builder()
                .orderId(stopOrder.getOrderId())
                .memberId(stopOrder.getMemberId())
                .symbol(stopOrder.getSymbol())
                .direction(stopOrder.getDirection())
                .type(newType)
                .price(stopOrder.getPrice())
                .volume(stopOrder.getVolume())
                .dealVolume(stopOrder.getDealVolume())
                .status(com.icetea.lotus.core.domain.entity.OrderStatus.NEW)
                .createTime(stopOrder.getCreateTime())
                .build();
    }

    /**
     * Kiểm tra các vị thế cần thanh lý
     */
    private void checkPositionsToLiquidate() {
        // Nếu không có giá đánh dấu, không kiểm tra
        if (markPrice == null || markPrice.getValue().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        // Kiểm tra từng vị thế
        for (Position position : positionsToCheck.values()) {
            // Tính toán tỷ lệ ký quỹ
            BigDecimal marginRatio = calculateMarginRatio(position);

            // Nếu tỷ lệ ký quỹ < ngưỡng thanh lý, tạo lệnh thanh lý
            if (marginRatio.compareTo(contract.getMaintenanceMarginRate().getValue()) < 0) {
                createLiquidationOrder(position);
            }
        }
    }

    /**
     * Tính toán tỷ lệ ký quỹ
     * @param position Vị thế
     * @return Tỷ lệ ký quỹ
     */
    private BigDecimal calculateMarginRatio(Position position) {
        // Tính toán giá trị vị thế
        BigDecimal positionValue = position.getVolume().multiply(markPrice.getValue());

        // Tính toán lãi/lỗ chưa thực hiện
        BigDecimal unrealizedPnl = calculateUnrealizedPnl(position);

        // Tính toán ký quỹ khả dụng
        BigDecimal availableMargin = position.getMargin().getValue().add(unrealizedPnl);

        // Tính toán tỷ lệ ký quỹ
        return availableMargin.divide(positionValue, 8, RoundingMode.DOWN);
    }

    /**
     * Tính toán lãi/lỗ chưa thực hiện
     * @param position Vị thế
     * @return Lãi/lỗ chưa thực hiện
     */
    private BigDecimal calculateUnrealizedPnl(Position position) {
        // Tính toán lãi/lỗ chưa thực hiện
        BigDecimal entryPrice = position.getOpenPrice().getValue();
        BigDecimal currentPrice = markPrice.getValue();
        BigDecimal volume = position.getVolume();

        if (position.getDirection() == PositionDirection.LONG) {
            return volume.multiply(currentPrice.subtract(entryPrice));
        } else {
            return volume.multiply(entryPrice.subtract(currentPrice));
        }
    }

    /**
     * Tạo lệnh thanh lý
     * @param position Vị thế cần thanh lý
     */
    private void createLiquidationOrder(Position position) {
        // Tạo lệnh thanh lý với hướng ngược lại với vị thế
        com.icetea.lotus.core.domain.entity.OrderDirection direction =
                position.getDirection() == PositionDirection.LONG ?
                com.icetea.lotus.core.domain.entity.OrderDirection.SELL :
                com.icetea.lotus.core.domain.entity.OrderDirection.BUY;

        // Tạo ID lệnh mới
        OrderId orderId = OrderId.of(UUID.randomUUID().toString());

        // Tạo lệnh thanh lý
        Order liquidationOrder = Order.builder()
                .orderId(orderId)
                .memberId(position.getMemberId())
                .symbol(position.getSymbol())
                .direction(direction)
                .type(com.icetea.lotus.core.domain.entity.OrderType.MARKET) // Lệnh thị trường để đảm bảo khớp ngay
                .price(markPrice) // Giá thị trường
                .volume(position.getVolume()) // Đóng toàn bộ vị thế
                .dealVolume(BigDecimal.ZERO)
                .status(com.icetea.lotus.core.domain.entity.OrderStatus.NEW)
                .createTime(LocalDateTime.now())
                .build();

        // Xử lý lệnh thanh lý
        processOrder(liquidationOrder);

        // Xóa vị thế khỏi danh sách kiểm tra
        positionsToCheck.remove(position.getId().getValue().toString());

        System.out.println("Đã tạo lệnh thanh lý cho vị thế: " + position.getId().getValue());
    }

    /**
     * Thêm vị thế vào danh sách kiểm tra
     * @param position Vị thế cần kiểm tra
     */
    public void addPositionToCheck(Position position) {
        positionsToCheck.put(position.getId().getValue().toString(), position);
    }

    /**
     * Xóa vị thế khỏi danh sách kiểm tra
     * @param positionId ID vị thế
     */
    public void removePositionToCheck(PositionId positionId) {
        positionsToCheck.remove(positionId.getValue().toString());
    }

    /**
     * Cập nhật vị thế trong danh sách kiểm tra
     * @param position Vị thế cập nhật
     */
    public void updatePositionToCheck(Position position) {
        positionsToCheck.put(position.getId().getValue().toString(), position);
    }

    /**
     * Lấy sổ lệnh hiện tại
     * @return Sổ lệnh
     */
    public com.icetea.lotus.core.domain.service.OrderBook getOrderBook() {
        try {
            // Lấy snapshot hiện tại - sử dụng cơ chế lock-free
            OrderBookSnapshot snapshot = orderBookRef.get();
//            log.info("getOrderBook snapshot: {}", snapshot.allOrders);
            // Danh sách các mức giá mua - khởi tạo với kích thước dự kiến để tối ưu bộ nhớ
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> bids = new ArrayList<>(snapshot.buyEntryList().size());
            for (Map.Entry<Money, CopyOnWriteArrayList<Order>> entry : snapshot.buyEntryList()) {
                log.info("getOrderBook bids entry: {}", entry.getValue());
                Money price = entry.getKey();
                CopyOnWriteArrayList<Order> ordersAtPrice = entry.getValue();

                // Tính tổng khối lượng ở mức giá này - sử dụng stream để tối ưu
                BigDecimal totalVolume = ordersAtPrice.stream()
                        .map(order -> order.getVolume().subtract(order.getDealVolume()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // Chỉ thêm vào danh sách nếu có khối lượng > 0
                if (totalVolume.compareTo(BigDecimal.ZERO) > 0) {
                    bids.add(com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                            .price(price)
                            .volume(totalVolume)
                            .orders(new ArrayList<>(ordersAtPrice))
                            .build());
                }
            }

            // Danh sách các mức giá bán - khởi tạo với kích thước dự kiến để tối ưu bộ nhớ
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> asks = new ArrayList<>(snapshot.sellEntryList().size());
            for (Map.Entry<Money, CopyOnWriteArrayList<Order>> entry : snapshot.sellEntryList()) {
                Money price = entry.getKey();
                CopyOnWriteArrayList<Order> ordersAtPrice = entry.getValue();

                // Tính tổng khối lượng ở mức giá này - sử dụng stream để tối ưu
                BigDecimal totalVolume = ordersAtPrice.stream()
                        .map(order -> order.getVolume().subtract(order.getDealVolume()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // Chỉ thêm vào danh sách nếu có khối lượng > 0
                if (totalVolume.compareTo(BigDecimal.ZERO) > 0) {
                    asks.add(com.icetea.lotus.core.domain.service.OrderBook.PriceLevel.builder()
                            .price(price)
                            .volume(totalVolume)
                            .orders(new ArrayList<>(ordersAtPrice))
                            .build());
                }
            }

            return com.icetea.lotus.core.domain.service.OrderBook.builder()
                    .symbol(symbol)
                    .bids(bids)
                    .asks(asks)
                    .lastPrice(lastPrice)
                    .markPrice(markPrice)
                    .indexPrice(indexPrice)
                    .build();
        } catch (Exception e) {
            log.error("Lỗi khi lấy sổ lệnh cho symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Lấy sổ lệnh với độ sâu giới hạn
     * @param depth Độ sâu của sổ lệnh (số lượng mức giá)
     * @return OrderBook
     */
    public com.icetea.lotus.core.domain.service.OrderBook getOrderBook(int depth) {
        try {
            com.icetea.lotus.core.domain.service.OrderBook fullOrderBook = getOrderBook();

            if (fullOrderBook == null) {
                return null;
            }

            // Giới hạn số lượng mức giá
            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> limitedBids = fullOrderBook.getBids().stream()
                    .limit(depth)
                    .collect(Collectors.toList());

            List<com.icetea.lotus.core.domain.service.OrderBook.PriceLevel> limitedAsks = fullOrderBook.getAsks().stream()
                    .limit(depth)
                    .collect(Collectors.toList());

            return com.icetea.lotus.core.domain.service.OrderBook.builder()
                    .symbol(fullOrderBook.getSymbol())
                    .bids(limitedBids)
                    .asks(limitedAsks)
                    .lastPrice(fullOrderBook.getLastPrice())
                    .markPrice(fullOrderBook.getMarkPrice())
                    .indexPrice(fullOrderBook.getIndexPrice())
                    .build();
        } catch (Exception e) {
            log.error("Lỗi khi lấy sổ lệnh với độ sâu giới hạn cho symbol: {}", symbol, e);
            return null;
        }
    }

    /**
     * Hủy lệnh
     * @param orderId ID lệnh cần hủy
     * @return true nếu hủy thành công, false nếu không
     */
    public boolean cancelOrder(OrderId orderId) {
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tìm lệnh cần hủy
            Order order = currentSnapshot.allOrders.get(orderId.getValue());

            // Nếu không tìm thấy lệnh, trả về false
            if (order == null) {
                return false;
            }

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Xóa lệnh khỏi snapshot
            removeOrderFromSnapshot(order, newSnapshot);

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return true;
            }

            // Nếu CAS thất bại, thử lại
            System.out.println("CAS thất bại, thử lại hủy lệnh: " + orderId);
        }
    }

    /**
     * Hủy tất cả các lệnh của một thành viên
     * @param memberId ID thành viên
     * @return Danh sách các lệnh đã hủy
     */
    public List<Order> cancelAllOrders(Long memberId) {
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Danh sách các lệnh cần hủy
            List<Order> ordersToCancel = new ArrayList<>();

            // Tìm tất cả các lệnh của thành viên
            for (Order order : newSnapshot.allOrders.values()) {
                if (order.getMemberId().equals(memberId)) {
                    ordersToCancel.add(order);
                }
            }

            // Nếu không có lệnh nào cần hủy, trả về danh sách rỗng
            if (ordersToCancel.isEmpty()) {
                return Collections.emptyList();
            }

            // Xóa các lệnh khỏi snapshot
            for (Order order : ordersToCancel) {
                removeOrderFromSnapshot(order, newSnapshot);
            }

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                return ordersToCancel;
            }

            // Nếu CAS thất bại, thử lại
            System.out.println("CAS thất bại, thử lại hủy tất cả lệnh của thành viên: " + memberId);
        }
    }

    /**
     * Đặt thuật toán khớp lệnh
     * @param algorithm Thuật toán khớp lệnh
     */
    public void setMatchingAlgorithm(MatchingAlgorithm algorithm) {
        this.matchingAlgorithm = algorithm;
    }

    /**
     * Bật/tắt giao dịch
     * @param enabled true để bật, false để tắt
     */
    public void setTradingEnabled(boolean enabled) {
        this.tradingEnabled = enabled;
    }

    /**
     * Cập nhật lệnh
     * @param updatedOrder Lệnh cần cập nhật
     * @return true nếu cập nhật thành công, false nếu không
     */
    public boolean updateOrder(Order updatedOrder) {
        // Kiểm tra xem giao dịch có được bật không
        if (!tradingEnabled) {
            log.info(LogMessages.OrderMatching.INFO_TRADING_PAUSED(), symbol);
            return false;
        }

        log.debug("Bắt đầu cập nhật lệnh: {}, symbol: {}", updatedOrder.getOrderId(), symbol);

        // Xử lý cập nhật lệnh theo cơ chế lock-free
        while (true) {
            // Lấy snapshot hiện tại
            OrderBookSnapshot currentSnapshot = orderBookRef.get();

            // Tìm lệnh cần cập nhật
            Order existingOrder = currentSnapshot.allOrders.get(updatedOrder.getOrderId().getValue());

            // Nếu không tìm thấy lệnh, trả về false
            if (existingOrder == null) {
                log.warn("Không tìm thấy lệnh cần cập nhật: {}", updatedOrder.getOrderId());
                return false;
            }

            // Tạo bản sao để thực hiện các thay đổi
            OrderBookSnapshot newSnapshot = currentSnapshot.copy();

            // Xóa lệnh cũ khỏi sổ lệnh
            removeOrderFromSnapshot(existingOrder, newSnapshot);

            // Thêm lệnh mới vào sổ lệnh
            addOrderToSnapshot(updatedOrder, newSnapshot);

            // Cập nhật snapshot bằng CAS
            if (orderBookRef.compareAndSet(currentSnapshot, newSnapshot)) {
                log.debug("Đã cập nhật lệnh thành công: {}", updatedOrder.getOrderId());
                return true;
            }

            // Nếu CAS thất bại, thử lại
            log.debug("CAS thất bại, thử lại cập nhật lệnh: {}", updatedOrder.getOrderId());
        }
    }

    /**
     * Kiểm tra điều kiện giá
     * @param order Lệnh
     * @param bestPrice Giá tốt nhất
     * @param isBuyOrder Có phải lệnh mua không
     * @return true nếu giá chấp nhận được, false nếu không
     */
    private boolean isPriceAcceptable(Order order, Money bestPrice, boolean isBuyOrder) {
        // Chuyển đổi OrderType từ entity sang valueobject
        com.icetea.lotus.core.domain.valueobject.OrderType orderType =
                com.icetea.lotus.core.domain.util.OrderTypeConverter.toValueObject(order.getType());

        // Nếu là lệnh thị trường, luôn chấp nhận
        if (orderType == com.icetea.lotus.core.domain.valueobject.OrderType.MARKET) {
            return true;
        }

        // Nếu là lệnh mua, giá đặt phải >= giá bán tốt nhất
        if (isBuyOrder) {
            return order.getPrice().getValue().compareTo(bestPrice.getValue()) >= 0;
        }

        // Nếu là lệnh bán, giá đặt phải <= giá mua tốt nhất
        return order.getPrice().getValue().compareTo(bestPrice.getValue()) <= 0;
    }

    /**
     * Khớp lệnh theo thuật toán Hybrid (kết hợp FIFO và Pro-Rata)
     * @param order Lệnh cần khớp
     * @param snapshot Snapshot của sổ lệnh
     * @param trades Danh sách giao dịch được tạo ra
     */
    private void matchOrderHybrid(Order order, OrderBookSnapshot snapshot, List<Trade> trades) {
        // Xác định hướng lệnh
        boolean isBuyOrder = order.getDirection() == com.icetea.lotus.core.domain.entity.OrderDirection.BUY;

        // Khối lượng còn lại cần khớp
        BigDecimal remainingVolume = order.getVolume();

        // Thêm lệnh vào sổ lệnh
        addOrderToSnapshot(order, snapshot);

        // Tỷ lệ FIFO (ví dụ: 20% khối lượng khớp theo FIFO, 80% theo Pro-Rata)
        BigDecimal fifoRatio = new BigDecimal("0.2");

        // Khớp lệnh với các lệnh đối ứng
        while (remainingVolume.compareTo(BigDecimal.ZERO) > 0 &&
               (isBuyOrder ? !snapshot.isSellOrdersEmpty() : !snapshot.isBuyOrdersEmpty())) {
            // Lấy mức giá tốt nhất
            Map.Entry<Money, CopyOnWriteArrayList<Order>> bestPriceEntry =
                    isBuyOrder ? snapshot.firstSellEntry() : snapshot.firstBuyEntry();

            if (bestPriceEntry == null) {
                break;
            }

            Money bestPrice = bestPriceEntry.getKey();
            CopyOnWriteArrayList<Order> ordersAtBestPrice = bestPriceEntry.getValue();

            // Kiểm tra điều kiện giá
            if (!isPriceAcceptable(order, bestPrice, isBuyOrder)) {
                break;
            }

            // Chia khối lượng cần khớp thành phần FIFO và phần Pro-Rata
            BigDecimal fifoVolume = remainingVolume.multiply(fifoRatio).setScale(8, RoundingMode.DOWN);
            BigDecimal proRataVolume = remainingVolume.subtract(fifoVolume);

            // Khớp phần FIFO
            BigDecimal remainingFifoVolume = fifoVolume;
            List<Order> processedOrders = new ArrayList<>();

            // Khớp lệnh với các lệnh ở mức giá tốt nhất theo thứ tự thời gian (FIFO)
            for (int i = 0; i < ordersAtBestPrice.size() && remainingFifoVolume.compareTo(BigDecimal.ZERO) > 0; i++) {
                Order oppositeOrder = ordersAtBestPrice.get(i);

                // Khối lượng còn lại của lệnh đối ứng
                BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                        .subtract(oppositeOrder.getDealVolume());

                if (oppositeRemainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // Khối lượng khớp
                BigDecimal matchedVolume = remainingFifoVolume.min(oppositeRemainingVolume);

                // Tạo giao dịch
                Trade trade = createTrade(order, oppositeOrder, bestPrice, matchedVolume);
                trades.add(trade);

                // Cập nhật khối lượng đã khớp
                updateOrderDealVolume(order, matchedVolume, snapshot);
                updateOrderDealVolume(oppositeOrder, matchedVolume, snapshot);

                // Cập nhật khối lượng còn lại
                remainingFifoVolume = remainingFifoVolume.subtract(matchedVolume);
                remainingVolume = remainingVolume.subtract(matchedVolume);

                // Thêm vào danh sách lệnh đã xử lý
                processedOrders.add(oppositeOrder);

                // Nếu lệnh đối ứng đã khớp hết, xóa khỏi sổ lệnh
                if (oppositeOrder.getDealVolume().add(matchedVolume)
                        .compareTo(oppositeOrder.getVolume()) >= 0) {
                    if (isBuyOrder) {
                        snapshot.removeSellOrder(bestPrice, oppositeOrder);
                    } else {
                        snapshot.removeBuyOrder(bestPrice, oppositeOrder);
                    }
                }
            }

            // Khớp phần Pro-Rata nếu còn khối lượng
            if (proRataVolume.compareTo(BigDecimal.ZERO) > 0 && remainingVolume.compareTo(BigDecimal.ZERO) > 0) {
                // Tính tổng khối lượng ở mức giá tốt nhất (không bao gồm các lệnh đã xử lý trong phần FIFO)
                BigDecimal totalVolumeAtBestPrice = BigDecimal.ZERO;
                for (Order oppositeOrder : ordersAtBestPrice) {
                    if (!processedOrders.contains(oppositeOrder)) {
                        BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                                .subtract(oppositeOrder.getDealVolume());
                        totalVolumeAtBestPrice = totalVolumeAtBestPrice.add(oppositeRemainingVolume);
                    }
                }

                // Khớp lệnh với các lệnh ở mức giá tốt nhất theo tỷ lệ khối lượng (Pro-Rata)
                for (Order oppositeOrder : new ArrayList<>(ordersAtBestPrice)) {
                    if (!processedOrders.contains(oppositeOrder)) {
                        // Khối lượng còn lại của lệnh đối ứng
                        BigDecimal oppositeRemainingVolume = oppositeOrder.getVolume()
                                .subtract(oppositeOrder.getDealVolume());

                        if (oppositeRemainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }

                        // Tính khối lượng khớp theo tỷ lệ
                        BigDecimal ratio = oppositeRemainingVolume.divide(totalVolumeAtBestPrice, 8, RoundingMode.DOWN);
                        BigDecimal matchedVolume = proRataVolume.multiply(ratio).setScale(8, RoundingMode.DOWN);

                        // Đảm bảo khối lượng khớp không vượt quá khối lượng còn lại của lệnh đối ứng
                        matchedVolume = matchedVolume.min(oppositeRemainingVolume);

                        if (matchedVolume.compareTo(BigDecimal.ZERO) > 0) {
                            // Tạo giao dịch
                            Trade trade = createTrade(order, oppositeOrder, bestPrice, matchedVolume);
                            trades.add(trade);

                            // Cập nhật khối lượng đã khớp
                            updateOrderDealVolume(order, matchedVolume, snapshot);
                            updateOrderDealVolume(oppositeOrder, matchedVolume, snapshot);

                            // Cập nhật khối lượng còn lại
                            remainingVolume = remainingVolume.subtract(matchedVolume);

                            // Nếu lệnh đối ứng đã khớp hết, xóa khỏi sổ lệnh
                            if (oppositeOrder.getDealVolume().add(matchedVolume)
                                    .compareTo(oppositeOrder.getVolume()) >= 0) {
                                if (isBuyOrder) {
                                    snapshot.removeSellOrder(bestPrice, oppositeOrder);
                                } else {
                                    snapshot.removeBuyOrder(bestPrice, oppositeOrder);
                                }
                            }

                            // Nếu đã khớp hết khối lượng, thoát vòng lặp
                            if (remainingVolume.compareTo(BigDecimal.ZERO) <= 0) {
                                break;
                            }
                        }
                    }
                }
            }

            // Nếu không còn lệnh ở mức giá này, xóa mức giá
            if (ordersAtBestPrice.isEmpty()) {
                if (isBuyOrder) {
                    snapshot.removeSellPrice(bestPrice);
                } else {
                    snapshot.removeBuyPrice(bestPrice);
                }
            }
        }

        // Nếu lệnh đã khớp hết, xóa khỏi sổ lệnh
        if (order.getDealVolume().add(remainingVolume)
                .compareTo(order.getVolume()) >= 0) {
            removeOrderFromSnapshot(order, snapshot);
        }
    }
}
