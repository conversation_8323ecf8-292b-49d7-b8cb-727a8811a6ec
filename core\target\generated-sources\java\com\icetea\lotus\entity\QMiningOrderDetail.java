package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMiningOrderDetail is a Querydsl query type for MiningOrderDetail
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMiningOrderDetail extends EntityPathBase<MiningOrderDetail> {

    private static final long serialVersionUID = -794985601L;

    public static final QMiningOrderDetail miningOrderDetail = new QMiningOrderDetail("miningOrderDetail");

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final NumberPath<Long> miningOrderId = createNumber("miningOrderId", Long.class);

    public final StringPath miningUnit = createString("miningUnit");

    public final NumberPath<java.math.BigDecimal> output = createNumber("output", java.math.BigDecimal.class);

    public QMiningOrderDetail(String variable) {
        super(MiningOrderDetail.class, forVariable(variable));
    }

    public QMiningOrderDetail(Path<? extends MiningOrderDetail> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMiningOrderDetail(PathMetadata metadata) {
        super(MiningOrderDetail.class, metadata);
    }

}

