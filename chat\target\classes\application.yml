server:
  port: ${SERVER_PORT:6008}
  context-path: ${SERVER_CONTEXT_PATH:/chat}

spring:
  profiles:
    active: dev
  application:
    name: ${SPRING_APPLICATION_NAME:chat}
  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}

  datasource:
    hikari:
      minimum-idle: 2
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
    initialSize: 5
    minIdle: 5
    maxActive: 200
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall,log4j

  redis:
    pool:
      max-active: ${SPRING_REDIS_POOL_MAX_ACTIVE:300}
      max-wait: ${SPRING_REDIS_POOL_MAX_WAIT:60000}
      max-idle: ${SPRING_REDIS_POOL_MAX_IDLE:100}
      min-idle: ${SPRING_REDIS_POOL_MIN_IDLE:20}
    timeout: ${SPRING_REDIS_TIMEOUT:30000}

  kafka:
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:0}
      batch:
        size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer:
        memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
    consumer:
      enable.auto.commit: ${KAFKA_CONSUMER_ENABLE_AUTO_COMMIT:false}
      session.timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto.commit.interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto.offset.reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      group.id: ${KAFKA_CONSUMER_GROUP_ID:default-group}
      concurrency: 9
      maxPollRecordsConfig: 50

  data:
    jpa:
      repositories:
        enabled: true

  devtools:
    restart:
      enabled: true

  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    show-sql: true
  cloud:
    consul:
      discovery:
        enabled: true
        service-name: ${SPRING_APPLICATION_NAME:chat}
        health-check-path: ${server.context-path:/chat}/actuator/health
        health-check-interval: 10s
        prefer-ip-address: true
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent

management:
  health:
    mail:
      enabled: false
  security:
    enabled: false
  context-path: /actuator

endpoints:
  health:
    sensitive: false
    enabled: true
  info:
    sensitive: false
  metrics:
    sensitive: false

spark:
  system:
    work-id: 1
    data-center-id: 1
    host: "@spark.system.host@"
    name: "@spark.system.name@"
    admins: "@spark.system.admins@"
    admin-phones: "@spark.system.admin-phones@"

aliyun:
  mail-sms:
    region: "@aliyun.mail-sms.region@"
    access-key-id: "@aliyun.mail-sms.access-key-id@"
    access-secret: "@aliyun.mail-sms.access-secret@"
    from-address: "@aliyun.mail-sms.from-address@"
    from-alias: "@aliyun.mail-sms.from-alias@"
    sms-sign: "@aliyun.mail-sms.sms-sign@"
    sms-template: "@aliyun.mail-sms.sms-template@"

es:
  username: ""
  password: ""
  mine:
    index: ""
    type: ""
  public:
    ip: ""
  private:
    ip: ""
  port: 9200

security:
  management:
    context-path: /monitor
  user:
    name: test2
    password: test112**3828**@#&@

aqmd:
  netty:
    port: ${AQMD_NETTY_PORT:28902}
    bossThreadSize: ${AQMD_NETTY_BOSS_THREAD_SIZE:1}
    worker-thread-size: ${AQMD_NETTY_WORKER_THREAD_SIZE:3}
    packetHeaderLength: ${AQMD_NETTY_WORKER_THREAD_SIZE:3}
    max-frame-length: ${AQMD_NETTY_MAX_FRAME_LENGTH:2147483647}
    writer-idle: ${AQMD_NETTY_WRITER_IDLE:200}
    max-timeout: ${AQMD_NETTY_MAX_TIMEOUT:60}
    defaultTimeout: ${AQMD_NETTY_DEFAULT_TIMEOUT:30}
    deal-handler-thread-size: ${AQMD_NETTY_DEAL_HANDLER_THREAD_SIZE:10}
    serviceLoggerLevel: ${AQMD_NETTY_SERVICE_LOGGER_LEVEL:debug}
    direct-access-flag: ${AQMD_NETTY_DIRECT_ACCESS_FLAG:1}
    direct-access-command: ${AQMD_NETTY_DIRECT_ACCESS_COMMAND:20021,20022,20031,20032,20033,20034,20035,20036,20037,20038}
    websocket-flag: ${AQMD_NETTY_WEBSOCKET_FLAG:1}
    websocket-port: ${AQMD_NETTY_WEBSOCKET_PORT:28903}

apns:
  cert-file-path: ${APNS_CERT_FILE_PATH:/web/apns.p12}
  cert-file-password: ${APNS_CERT_FILE_PASSWORD:}
  bundle-id: ${APNS_BUNDLE_ID:}
  dev-env: ${APNS_DEV_ENV:true}