package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QAutomainconfig is a Querydsl query type for Automainconfig
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QAutomainconfig extends EntityPathBase<Automainconfig> {

    private static final long serialVersionUID = 297001594L;

    public static final QAutomainconfig automainconfig = new QAutomainconfig("automainconfig");

    public final StringPath address = createString("address");

    public final NumberPath<Integer> coinid = createNumber("coinid", Integer.class);

    public final StringPath coinname = createString("coinname");

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Double> minnum = createNumber("minnum", Double.class);

    public final NumberPath<Integer> protocol = createNumber("protocol", Integer.class);

    public QAutomainconfig(String variable) {
        super(Automainconfig.class, forVariable(variable));
    }

    public QAutomainconfig(Path<? extends Automainconfig> path) {
        super(path.getType(), path.getMetadata());
    }

    public QAutomainconfig(PathMetadata metadata) {
        super(Automainconfig.class, metadata);
    }

}

