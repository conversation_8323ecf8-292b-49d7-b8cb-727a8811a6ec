package com.icetea.lotus.infrastructure.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.icetea.lotus.application.dto.ContractDto;
import com.icetea.lotus.application.port.input.ManageContractUseCase;
import com.icetea.lotus.application.port.input.ManageMarketDataUseCase;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.valueobject.Money;
import com.icetea.lotus.core.domain.valueobject.Symbol;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service xử lý dữ liệu thị trường
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketDataService implements ManageMarketDataUseCase {

    private final ManageContractUseCase manageContractUseCase;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    @Value("${topic-kafka.contract.mark-price}")
    private String contractMarkPriceTopic;

    @Value("${topic-kafka.contract.index-price}")
    private String contractIndexPriceTopic;

    private final Map<String, BigDecimal> markPriceMap = new ConcurrentHashMap<>();
    private final Map<String, BigDecimal> indexPriceMap = new ConcurrentHashMap<>();
    private final Map<String, BigDecimal> lastPriceMap = new ConcurrentHashMap<>();
    private final Map<String, Long> lastTradeTimeMap = new ConcurrentHashMap<>();

    /**
     * Cập nhật giá đánh dấu từ giao dịch
     * @param trade Giao dịch
     */
    @Override
    public void updateMarkPriceFromTrade(Trade trade) {
        log.info("Cập nhật giá đánh dấu từ giao dịch, symbol = {}, price = {}", trade.getSymbol(), trade.getPrice());

        String symbolStr = trade.getSymbol().getValue();
        BigDecimal price = trade.getPrice().getValue();

        // Cập nhật giá đánh dấu
        markPriceMap.put(symbolStr, price);
        // Cập nhật giá giao dịch cuối cùng
        lastPriceMap.put(symbolStr, price);
        lastTradeTimeMap.put(symbolStr, System.currentTimeMillis());

        // Gửi giá đánh dấu đến Kafka
        sendMarkPriceToKafka(symbolStr, price);
    }

    /**
     * Cập nhật giá chỉ số
     * @param symbol Ký hiệu của hợp đồng
     * @param indexPrice Giá chỉ số
     */
    @Override
    public void updateIndexPrice(String symbol, BigDecimal indexPrice) {
        log.info("Cập nhật giá chỉ số, symbol = {}, indexPrice = {}", symbol, indexPrice);

        // Cập nhật giá chỉ số
        indexPriceMap.put(symbol, indexPrice);

        // Gửi giá chỉ số đến Kafka
        sendIndexPriceToKafka(symbol, indexPrice);
    }

    /**
     * Lấy giá đánh dấu
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Override
    public BigDecimal getMarkPrice(String symbol) {
        return markPriceMap.getOrDefault(symbol, BigDecimal.ZERO);
    }

    /**
     * Lấy giá chỉ số
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Override
    public BigDecimal getIndexPrice(String symbol) {
        return indexPriceMap.getOrDefault(symbol, BigDecimal.ZERO);
    }

    /**
     * Lấy giá giao dịch cuối cùng
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Override
    public BigDecimal getLastPrice(String symbol) {
        BigDecimal lastPrice = lastPriceMap.getOrDefault(symbol, BigDecimal.ZERO);

        // Nếu không có giá giao dịch cuối cùng, sử dụng giá đánh dấu
        if (lastPrice.compareTo(BigDecimal.ZERO) <= 0) {
            lastPrice = getMarkPrice(symbol);
            log.debug("Không có giá giao dịch cuối cùng, sử dụng giá đánh dấu, symbol = {}, markPrice = {}", symbol, lastPrice);
        }

        return lastPrice;
    }

    // Cờ bật/tắt tính toán giá đánh dấu
    @Value("${contract.pricing.market-data-calculation-enabled:false}")
    private boolean marketDataCalculationEnabled;

    /**
     * Tính toán giá đánh dấu
     * Chạy mỗi 5 giây
     * ⚠️ WARNING: Có thể conflict với PriceUpdateScheduler
     */
    @Scheduled(fixedRate = 5000)
    public void calculateMarkPrice() {
        if (!marketDataCalculationEnabled) {
            return; // Tắt để tránh conflict
        }

        log.info("Tính toán giá đánh dấu");

        // Lấy danh sách hợp đồng
        List<ContractDto> contracts = manageContractUseCase.findAllEnabled();

        // Tính toán giá đánh dấu cho từng hợp đồng
        for (ContractDto contract : contracts) {
            try {
                String symbolStr = contract.getSymbol();

                // Lấy giá đánh dấu hiện tại
                BigDecimal markPrice = markPriceMap.getOrDefault(symbolStr, BigDecimal.ZERO);

                // Lấy giá chỉ số
                BigDecimal indexPrice = indexPriceMap.getOrDefault(symbolStr, BigDecimal.ZERO);

                // Nếu không có giá đánh dấu hoặc giá chỉ số, bỏ qua
                if (markPrice.compareTo(BigDecimal.ZERO) <= 0 || indexPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                // Lấy thời gian giao dịch cuối cùng
                Long lastTradeTime = lastTradeTimeMap.getOrDefault(symbolStr, 0L);

                // Nếu không có giao dịch trong 5 phút, sử dụng giá chỉ số
                if (System.currentTimeMillis() - lastTradeTime > 5 * 60 * 1000) {
                    markPrice = indexPrice;
                } else {
                    // Tính toán giá đánh dấu mới (EMA)
                    BigDecimal alpha = new BigDecimal("0.1"); // Hệ số EMA
                    markPrice = indexPrice.multiply(alpha).add(markPrice.multiply(BigDecimal.ONE.subtract(alpha)));
                }

                // Làm tròn giá đánh dấu
                int priceScale = contract.getPriceScale() != null ? contract.getPriceScale() : 8;
                markPrice = markPrice.setScale(priceScale, RoundingMode.HALF_UP);

                // Cập nhật giá đánh dấu
                markPriceMap.put(symbolStr, markPrice);

                // Gửi giá đánh dấu đến Kafka
                sendMarkPriceToKafka(symbolStr, markPrice);
            } catch (Exception e) {
                log.error("Tính toán giá đánh dấu thất bại, symbol = {}", contract.getSymbol(), e);
            }
        }
    }

    /**
     * Gửi giá đánh dấu đến Kafka
     * @param symbol Ký hiệu của hợp đồng
     * @param markPrice Giá đánh dấu
     */
    private void sendMarkPriceToKafka(String symbol, BigDecimal markPrice) {
        try {
            ObjectNode json = objectMapper.createObjectNode();
            json.put("symbol", symbol);
            json.put("price", markPrice);
            json.put("time", System.currentTimeMillis());

            kafkaTemplate.send(contractMarkPriceTopic, symbol, json.toString());
        } catch (Exception e) {
            log.error("Gửi giá đánh dấu đến Kafka thất bại, symbol = {}, markPrice = {}", symbol, markPrice, e);
        }
    }

    /**
     * Gửi giá chỉ số đến Kafka
     * @param symbol Ký hiệu của hợp đồng
     * @param indexPrice Giá chỉ số
     */
    private void sendIndexPriceToKafka(String symbol, BigDecimal indexPrice) {
        try {
            ObjectNode json = objectMapper.createObjectNode();
            json.put("symbol", symbol);
            json.put("price", indexPrice);
            json.put("time", System.currentTimeMillis());

            kafkaTemplate.send(contractIndexPriceTopic, symbol, json.toString());
        } catch (Exception e) {
            log.error("Gửi giá chỉ số đến Kafka thất bại, symbol = {}, indexPrice = {}", symbol, indexPrice, e);
        }
    }

    /**
     * Lấy giá mua tốt nhất
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Override
    public BigDecimal getBestBid(String symbol) {
        // Trong môi trường thực tế, cần lấy giá từ sổ lệnh
        // Ở đây, chúng ta sẽ sử dụng giá mô phỏng
        BigDecimal lastPrice = getLastPrice(symbol);
        if (lastPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return lastPrice.multiply(new BigDecimal("0.999")).setScale(8, RoundingMode.DOWN);
    }

    /**
     * Lấy giá bán tốt nhất
     * @param symbol Ký hiệu của hợp đồng
     * @return BigDecimal
     */
    @Override
    public BigDecimal getBestAsk(String symbol) {
        // Trong môi trường thực tế, cần lấy giá từ sổ lệnh
        // Ở đây, chúng ta sẽ sử dụng giá mô phỏng
        BigDecimal lastPrice = getLastPrice(symbol);
        if (lastPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return lastPrice.multiply(new BigDecimal("1.001")).setScale(8, RoundingMode.UP);
    }
}
