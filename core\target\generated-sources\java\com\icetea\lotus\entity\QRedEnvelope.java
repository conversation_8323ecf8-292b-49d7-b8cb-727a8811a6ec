package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QRedEnvelope is a Querydsl query type for RedEnvelope
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QRedEnvelope extends EntityPathBase<RedEnvelope> {

    private static final long serialVersionUID = 541561905L;

    public static final QRedEnvelope redEnvelope = new QRedEnvelope("redEnvelope");

    public final StringPath bgImage = createString("bgImage");

    public final NumberPath<Integer> count = createNumber("count", Integer.class);

    public final DateTimePath<java.util.Date> createTime = createDateTime("createTime", java.util.Date.class);

    public final StringPath detail = createString("detail");

    public final StringPath envelopeNo = createString("envelopeNo");

    public final NumberPath<Integer> expiredHours = createNumber("expiredHours", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> invite = createNumber("invite", Integer.class);

    public final StringPath logoImage = createString("logoImage");

    public final NumberPath<java.math.BigDecimal> maxRand = createNumber("maxRand", java.math.BigDecimal.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final StringPath name = createString("name");

    public final NumberPath<Integer> plateform = createNumber("plateform", Integer.class);

    public final NumberPath<java.math.BigDecimal> receiveAmount = createNumber("receiveAmount", java.math.BigDecimal.class);

    public final NumberPath<Integer> receiveCount = createNumber("receiveCount", Integer.class);

    public final NumberPath<Integer> state = createNumber("state", Integer.class);

    public final NumberPath<java.math.BigDecimal> totalAmount = createNumber("totalAmount", java.math.BigDecimal.class);

    public final NumberPath<Integer> type = createNumber("type", Integer.class);

    public final StringPath unit = createString("unit");

    public QRedEnvelope(String variable) {
        super(RedEnvelope.class, forVariable(variable));
    }

    public QRedEnvelope(Path<? extends RedEnvelope> path) {
        super(path.getType(), path.getMetadata());
    }

    public QRedEnvelope(PathMetadata metadata) {
        super(RedEnvelope.class, metadata);
    }

}

