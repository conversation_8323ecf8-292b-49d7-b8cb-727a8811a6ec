package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.util.GeneratorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * The ExchangePushJob class is responsible for handling the push of trades, plates,
 * and coin thumb data to a messaging system. It uses scheduled tasks to periodically
 * process and push this data to ensure real-time updates to subscribers.
 * <p>
 * Fields:
 * - coinService: Provides services related to coin operations.
 * - messagingTemplate: Facilitates sending of messages to messaging queues.
 * - tradesQueue: Queue to manage trades data.
 * - plateQueue: Queue to manage trade plate data.
 * - thumbQueue: Queue to manage coin thumb data.
 * - rand: Random number generator for probabilistic behavior.
 * - plateLastBuy: Last recorded buy trade plate.
 * - plateLastSell: Last recorded sell trade plate.
 * - plateLastBuyOrigin: Origin of the last buy trade plate.
 * - plateLastSellOrigin: Origin of the last sell trade plate.
 * - lastBuyHeightPrice: Highest price for the last buy operation.
 * - lastSellLowPrice: Lowest price for the last sell operation.
 * - lastPushThumb: Timestamp of the last pushed thumb data.
 * - log: Logger instance for logging activities.
 * <p>
 * Methods:
 * - addTrades(String symbol, List<ExchangeTrade> trades):
 * Adds a list of trades associated with a given symbol to the trade queue.
 * <p>
 * - addPlates(String symbol, TradePlate plate):
 * Adds a trade plate associated with a given symbol to the plate queue.
 * <p>
 * - addThumb(String symbol, CoinThumb thumb):
 * Adds a coin thumb object associated with a given symbol to the thumb queue.
 * <p>
 * - pushTrade():
 * Scheduled task executed at a fixed rate of 800 milliseconds to process and
 * push trade information from the queue to subscribers.
 * <p>
 * - pushPlate():
 * Scheduled task executed after a fixed delay of 1000 milliseconds to process
 * and push trade plate information from the queue to subscribers.
 * <p>
 * - pushThumb():
 * Scheduled task executed at a fixed rate of 1000 milliseconds to process and
 * push coin thumb data from the queue to subscribers.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ExchangePushJob {

    private static final ObjectMapper mapper = new ObjectMapper();
    private final ExchangeCoinService coinService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    // Thread-safe collections for queues
    private final Map<String, List<ExchangeTrade>> tradesQueue = new ConcurrentHashMap<>();
    private final Map<String, List<TradePlate>> plateQueue = new ConcurrentHashMap<>();
    private final Map<String, List<CoinThumb>> thumbQueue = new ConcurrentHashMap<>();

    // Locks for more granular synchronization
    private final ReentrantLock tradesLock = new ReentrantLock();
    private final ReentrantLock platesLock = new ReentrantLock();
    private final ReentrantLock thumbsLock = new ReentrantLock();
    private final ReentrantLock plateLastBuyLock = new ReentrantLock();
    private final ReentrantLock plateLastSellLock = new ReentrantLock();

    private final Random rand = new Random();

    // Trade plate data
    private final Map<String, TradePlate> plateLastBuy = new ConcurrentHashMap<>(); // The last push of the buy plate
    private final Map<String, TradePlate> plateLastSell = new ConcurrentHashMap<>(); // The last push of the sell plate
    private final Map<String, TradePlate> plateLastBuyOrigin = new ConcurrentHashMap<>(); // Original buy plate for reference
    private final Map<String, TradePlate> plateLastSellOrigin = new ConcurrentHashMap<>(); // Original sell plate for reference

    private BigDecimal lastBuyHeightPrice = BigDecimal.ZERO;
    private BigDecimal lastSellLowPrice = BigDecimal.ZERO;

    // Last pushed market data
    private final Map<String, CoinThumb> lastPushThumb = new ConcurrentHashMap<>(); // The last push Thumb

    public void addTrades(String symbol, List<ExchangeTrade> trades) {
        List<ExchangeTrade> list = tradesQueue.get(symbol);
        if (list == null) {
            list = new ArrayList<>();
            tradesQueue.put(symbol, list);
        }
        synchronized (list) {
            list.addAll(trades);
        }
    }

    public void addPlates(String symbol, TradePlate plate) {
        List<TradePlate> list = plateQueue.get(symbol);
        if (list == null) {
            list = new ArrayList<>();
            plateQueue.put(symbol, list);
        }
        synchronized (list) {
            list.add(plate);
        }

        if (plate.getDirection() == ExchangeOrderDirection.BUY) {
            // Update the latest trading post
            synchronized (plateLastBuy) {
                plateLastBuy.put(symbol, plate);
                plateLastBuyOrigin = (HashMap<String, TradePlate>) plateLastBuy.clone();
                lastBuyHeightPrice = plate.getHighestPrice();
            }
        }
        if (plate.getDirection() == ExchangeOrderDirection.SELL) {
            // Update the latest trading post
            synchronized (plateLastSell) {
                plateLastSell.put(symbol, plate);
                plateLastSellOrigin = (HashMap<String, TradePlate>) plateLastSell.clone();
                lastSellLowPrice = plate.getLowestPrice();
            }
        }
    }

    public void addThumb(String symbol, CoinThumb thumb) {
        List<CoinThumb> list = thumbQueue.get(symbol);
        if (list == null) {
            list = new ArrayList<>();
            thumbQueue.put(symbol, list);
        }
        synchronized (list) {
            list.add(thumb);
        }
    }


    @Scheduled(fixedRate = 800)
    public void pushTrade() {
        Iterator<Map.Entry<String, List<ExchangeTrade>>> entryIterator = tradesQueue.entrySet().iterator();
        while (entryIterator.hasNext()) {
            Map.Entry<String, List<ExchangeTrade>> entry = entryIterator.next();
            String symbol = entry.getKey();
            List<ExchangeTrade> trades = entry.getValue();
            if (trades.size() > 0) {
                synchronized (trades) {
                    messagingTemplate.convertAndSend("/topic/market/trade/" + symbol, trades);
                    trades.clear();
                }
            }
        }
    }

    /**
     * Pushes trade plate data periodically through WebSocket and Netty communication.
     * This method is scheduled to run at a fixed interval and updates trade plates and market
     * depth, ensuring real-time synchronization of trading data with connected clients.
     * <p>
     * Functionality:
     * - Iterates over a queue of trade plates grouped by trading pairs (symbols).
     * - Synchronizes access to trade plates, ensuring thread-safe updates and pushes.
     * - Pushes BUY and SELL trade plate data based on their direction.
     * - Sends trade plate and depth information to WebSocket endpoints and processes data through Netty handlers.
     * - Handles cases of empty trade plates by generating and sending virtual trading data to simulate an active market.
     * - Generates virtual trades, updates market summary (thumb), and pushes the updated information via WebSocket.
     * - Ensures that data integrity is maintained during random data simulation.
     * <p>
     * Behavior in specific cases:
     * - Virtual trading data is generated when the individual trade plates are empty, simulating market activity.
     * - Last recorded BUY and SELL plates are used to create simulated trades.
     * - Thorough randomness is introduced into the trade prices and amounts to imitate a dynamic market.
     * - Pushes virtual market and trade updates to WebSocket topics.
     * <p>
     * Constraints:
     * - Assumes `plateQueue` contains valid trade plate data.
     * - Requires proper initialization of `messagingTemplate`, `nettyHandler`, and other dependencies.
     * - Ensures virtual trades respect specified constraints, like price increments and trade amounts.
     * <p>
     * Thread Safety:
     * This method implements concurrent data manipulation and delivery. Synchronization blocks ensure data consistency
     * during access and updates of shared resources such as trade plates and market information.
     */
    /**
     * Pushes trade plate data to clients.
     * <p>
     * This method processes the plateQueue, sending real trade plate data when available,
     * or generating and sending virtual trade data when no real data exists.
     */
    @Scheduled(fixedDelay = 1000)
    public void pushPlate() {
        Iterator<Map.Entry<String, List<TradePlate>>> entryIterator = plateQueue.entrySet().iterator();
        while (entryIterator.hasNext()) {
            Map.Entry<String, List<TradePlate>> entry = entryIterator.next();
            String symbol = entry.getKey();
            List<TradePlate> plates = entry.getValue();

            if (CollectionUtils.isNotEmpty(plates)) {
                // Process real trade plates
                processRealTradePlates(symbol, plates);
            } else {
                // Process virtual trade plates
                processVirtualTradePlates(symbol);
            }
        }
    }

    /**
     * Processes real trade plates from the queue
     *
     * @param symbol Trading pair symbol
     * @param plates List of trade plates to process
     */
    private void processRealTradePlates(String symbol, List<TradePlate> plates) {
        boolean hasPushAskPlate = false;
        boolean hasPushBidPlate = false;
        synchronized (plates) {
            for (TradePlate plate : plates) {
                if (plate.getDirection() == ExchangeOrderDirection.BUY && !hasPushBidPlate) {
                    hasPushBidPlate = true;
                } else if (plate.getDirection() == ExchangeOrderDirection.SELL && !hasPushAskPlate) {
                    hasPushAskPlate = true;
                } else {
                    continue;
                }
                // Push plate data to WebSocket
                pushPlateToWebSocket(symbol, plate);
            }
            plates.clear();
        }
    }

    /**
     * Pushes plate data to WebSocket topics
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to push
     */
    private void pushPlateToWebSocket(String symbol, TradePlate plate) {
        // Websocket push handover information
        messagingTemplate.convertAndSend("/topic/market/trade-plate/" + symbol, plate.toJSON(24));
        // Websocket push in-depth information
        messagingTemplate.convertAndSend("/topic/market/trade-depth/" + symbol, plate.toJSON(50));
    }

    /**
     * Processes virtual trade plates when no real data is available
     *
     * @param symbol Trading pair symbol
     */
    private void processVirtualTradePlates(String symbol) {
        log.info("Starting virtual trade plate processing for symbol: {}", symbol);

        // Check if fake data generation is enabled for this symbol
        ExchangeCoin focusCoin = coinService.findBySymbol(symbol);
        if (focusCoin != null && focusCoin.getFakeDataStatus() == 0) {
            log.info("No trade plate data for symbol: {}, fake data generation disabled", symbol);
            return;
        }

        // Get the last known buy and sell plates
        TradePlate plateBuy = plateLastBuy.get(symbol);
        TradePlate plateSell = plateLastSell.get(symbol);

        log.info("Virtual trade plates for {}: Buy plate exists: {}, Sell plate exists: {}",
                symbol, plateBuy != null, plateSell != null);

        if (plateBuy != null) {
            log.info("Buy plate for {}: Items count: {}, Highest price: {}",
                    symbol, plateBuy.getItems().size(), plateBuy.getHighestPrice());
        }

        if (plateSell != null) {
            log.info("Sell plate for {}: Items count: {}, Lowest price: {}",
                    symbol, plateSell.getItems().size(), plateSell.getLowestPrice());
        }

        // Randomly refresh plates from original state
        refreshPlatesIfNeeded(symbol, plateBuy, plateSell);

        // Generate virtual trades
        int randomValue = rand.nextInt(100);
        log.info("Virtual trade generation random value for {}: {} (generates if > 50)", symbol, randomValue);
        if (randomValue > 50) {
            log.info("Generating virtual trade for symbol: {}", symbol);
            generateVirtualTrade(symbol, plateBuy, plateSell);
        } else {
            log.info("Skipping virtual trade generation for symbol: {}", symbol);
        }

        // Modify and push buy plate
        if (plateBuy != null) {
            log.info("Modifying and pushing buy plate for symbol: {}", symbol);
            modifyAndPushPlate(symbol, plateBuy);
        }

        // Modify and push sell plate
        if (plateSell != null) {
            log.info("Modifying and pushing sell plate for symbol: {}", symbol);
            modifyAndPushPlate(symbol, plateSell);
        }

        log.info("Completed virtual trade plate processing for symbol: {}", symbol);
    }

    /**
     * Refreshes plates from original state if needed
     *
     * @param symbol    Trading pair symbol
     * @param plateBuy  Buy plate
     * @param plateSell Sell plate
     */
    private void refreshPlatesIfNeeded(String symbol, TradePlate plateBuy, TradePlate plateSell) {
        log.info("Checking if plates need refreshing for symbol: {}", symbol);

        int buyRandom = rand.nextInt(100);
        boolean shouldRefreshBuy = buyRandom > 50 && plateBuy != null;
        log.info("Buy plate refresh check for {}: random value={}, should refresh={}",
                symbol, buyRandom, shouldRefreshBuy);

        if (shouldRefreshBuy) {
            log.info("Refreshing buy plate from original state for symbol: {}", symbol);
            plateLastBuy = (HashMap<String, TradePlate>) plateLastBuyOrigin.clone();
        }

        int sellRandom = rand.nextInt(100);
        boolean shouldRefreshSell = sellRandom > 50 && plateSell != null;
        log.info("Sell plate refresh check for {}: random value={}, should refresh={}",
                symbol, sellRandom, shouldRefreshSell);

        if (shouldRefreshSell) {
            log.info("Refreshing sell plate from original state for symbol: {}", symbol);
            plateLastSell = (HashMap<String, TradePlate>) plateLastSellOrigin.clone();
        }

        log.info("Plate refresh completed for symbol: {}", symbol);
    }

    /**
     * Generates a virtual trade
     *
     * @param symbol    Trading pair symbol
     * @param plateBuy  Buy plate
     * @param plateSell Sell plate
     */
    private void generateVirtualTrade(String symbol, TradePlate plateBuy, TradePlate plateSell) {
        CoinThumb lastThumb = lastPushThumb.get(symbol);
        if (plateSell == null || plateBuy == null || lastThumb == null) {
            return;
        }

        BigDecimal buyFistPrice = plateBuy.getHighestPrice();
        BigDecimal sellLastPrice = plateSell.getLowestPrice();

        // Check if spread is sufficient
        if (sellLastPrice.subtract(buyFistPrice).compareTo(BigDecimal.valueOf(0.0001)) < 0) {
            return;
        }

        // Generate random trade direction
        ExchangeOrderDirection direction = rand.nextInt(100) > 50 ?
                ExchangeOrderDirection.BUY : ExchangeOrderDirection.SELL;

        // Generate random price and amount
        int seed = rand.nextInt(10);
        BigDecimal randThumbPrice;
        if (direction == ExchangeOrderDirection.BUY) {
            randThumbPrice = sellLastPrice.subtract(sellLastPrice.subtract(buyFistPrice)
                            .multiply(BigDecimal.valueOf(seed).divide(BigDecimal.valueOf(1000))))
                    .setScale(6, RoundingMode.HALF_UP);
        } else {
            randThumbPrice = buyFistPrice.add(sellLastPrice.subtract(buyFistPrice)
                            .multiply(BigDecimal.valueOf(seed).divide(BigDecimal.valueOf(1000))))
                    .setScale(6, RoundingMode.HALF_UP);
        }

        BigDecimal randThumbAmount = plateBuy.getMinAmount()
                .multiply(BigDecimal.valueOf(0.85))
                .setScale(8, RoundingMode.HALF_UP);

        // Handle edge cases
        if (randThumbPrice.compareTo(sellLastPrice) >= 0) {
            randThumbPrice = buyFistPrice;
        }

        // Reset plates if needed for non-BTC pairs with invalid price
        if (!symbol.equalsIgnoreCase("BTC/USDT") && randThumbPrice.compareTo(BigDecimal.ZERO) <= 0) {
            resetPlates(symbol);
            plateBuy = plateLastBuy.get(symbol);
            plateSell = plateLastSell.get(symbol);
            randThumbPrice = buyFistPrice;
        }

        // Reset plates if amount is too small
        if ((!symbol.equalsIgnoreCase("BTC/USDT") && randThumbAmount.compareTo(BigDecimal.valueOf(0.0001)) <= 0) ||
                (symbol.equalsIgnoreCase("BTC/USDT") && randThumbAmount.compareTo(BigDecimal.valueOf(0.000001)) <= 0)) {
            resetPlates(symbol);
            plateBuy = plateLastBuy.get(symbol);
            plateSell = plateLastSell.get(symbol);
            randThumbAmount = plateBuy.getMinAmount();
        }

        log.info("Trading pair: {}, virtual trading volume: {}, minimum volume: {}",
                symbol, randThumbAmount.toPlainString(), plateBuy.getMinAmount());

        // Create virtual trade
        ExchangeTrade randTrade = new ExchangeTrade();
        randTrade.setPrice(randThumbPrice);
        randTrade.setAmount(randThumbAmount);
        randTrade.setBuyOrderId(GeneratorUtil.getOrderId("E"));
        randTrade.setSellOrderId(GeneratorUtil.getOrderId("E"));
        randTrade.setDirection(direction);
        randTrade.setSymbol(symbol);
        randTrade.setTime(Calendar.getInstance().getTimeInMillis());

        // Check if order should be sent
        boolean sendOrder = true;
        if (randTrade.getDirection() == ExchangeOrderDirection.SELL) {
            if (randThumbPrice.subtract(buyFistPrice).compareTo(BigDecimal.valueOf(0.0001)) <= 0) {
                sendOrder = false;
            }
        } else {
            if (sellLastPrice.subtract(randThumbPrice).compareTo(BigDecimal.valueOf(0.0001)) <= 0) {
                sendOrder = false;
            }
        }

        if (sendOrder) {
            // Push virtual trade
            List<ExchangeTrade> virtualTrades = new ArrayList<>();
            virtualTrades.add(randTrade);
            messagingTemplate.convertAndSend("/topic/market/trade/" + symbol, virtualTrades);

            // Update market summary
            if (lastThumb != null) {
                lastThumb.setClose(randTrade.getPrice());
                lastThumb.setHigh(randThumbPrice.max(lastThumb.getHigh()));
                lastThumb.setLow(randThumbPrice.min(lastThumb.getLow()));
                lastThumb.setVolume(lastThumb.getVolume().add(randTrade.getAmount()));
                lastThumb.setTurnover(lastThumb.getTurnover().add(randTrade.getAmount().multiply(randTrade.getPrice())));
                lastThumb.setChange(lastThumb.getClose().subtract(lastThumb.getOpen()));
                lastThumb.setChg(lastThumb.getChange().divide(lastThumb.getOpen(), 4, RoundingMode.UP));
                lastThumb.setUsdRate(randTrade.getPrice());
                lastThumb.setTime(System.currentTimeMillis());
                messagingTemplate.convertAndSend("/topic/market/thumb", lastThumb);

                // nettyHandler.handleTrade(symbol, randTrade, lastThumb);
            }

            // Create corresponding order on opposite side
            BigDecimal orderAmount = randTrade.getAmount().multiply(BigDecimal.valueOf(1.1));
            if (randTrade.getDirection() == ExchangeOrderDirection.BUY) {
                ExchangeOrder sellOrder = new ExchangeOrder();
                sellOrder.setType(ExchangeOrderType.LIMIT_PRICE);
                sellOrder.setDirection(ExchangeOrderDirection.SELL);
                sellOrder.setAmount(orderAmount);
                sellOrder.setPrice(randTrade.getPrice());
                sellOrder.setTradedAmount(BigDecimal.ZERO);
                plateSell.add(sellOrder);
            } else {
                ExchangeOrder buyOrder = new ExchangeOrder();
                buyOrder.setType(ExchangeOrderType.LIMIT_PRICE);
                buyOrder.setDirection(ExchangeOrderDirection.BUY);
                buyOrder.setAmount(orderAmount);
                buyOrder.setPrice(randTrade.getPrice());
                buyOrder.setTradedAmount(BigDecimal.ZERO);
                plateBuy.add(buyOrder);
            }
        }
    }

    /**
     * Resets plates to original state
     *
     * @param symbol Trading pair symbol
     */
    private void resetPlates(String symbol) {
        plateLastBuy = (HashMap<String, TradePlate>) plateLastBuyOrigin.clone();
        plateLastSell = (HashMap<String, TradePlate>) plateLastSellOrigin.clone();
    }

    /**
     * Modifies a trade plate item and pushes the updated plate
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to modify and push
     */
    private void modifyAndPushPlate(String symbol, TradePlate plate) {
        List<TradePlateItem> list = plate.getItems();
        if (list.size() <= 9) {
            return;
        }

        int randInt = rand.nextInt(9);
        BigDecimal value = list.get(randInt).getAmount();
        String[] split = value.toPlainString().split("\\.");
        int number = split.length == 2 ? split[1].length() : 1;
        BigDecimal base = BigDecimal.valueOf(Math.pow(10.0, number));
        BigDecimal valueMultiply = BigDecimal.valueOf(Long.parseLong(split[0]));
        BigDecimal value1 = split.length == 2 ? BigDecimal.valueOf(Long.parseLong(split[1])) : BigDecimal.valueOf(1);
        int number1 = split.length == 2 ? split[1].length() : 1;
        // Add safety check for base1 calculation
        BigDecimal base1 = BigDecimal.valueOf(Math.pow(10.0, number1)).subtract(BigDecimal.valueOf(1));
        int randBound = Math.max(1, base1.intValue()); // Ensure the bound is at least 1
        int randAmountScale = rand.nextInt(randBound);

        BigDecimal multiplyValue1 = BigDecimal.ZERO;
        if (plate.getDirection() == ExchangeOrderDirection.BUY) {
            multiplyValue1 = new BigDecimal(randAmountScale).divide(base, number1, RoundingMode.DOWN);
        }

        BigDecimal multiply = valueMultiply.add(multiplyValue1);

        if (multiply.compareTo(BigDecimal.ZERO) > 0) {
            list.get(randInt).setAmount(multiply);

            // Push updated plate
            pushPlateToWebSocket(symbol, plate);

            // nettyHandler.handlePlate(symbol, plate);
        }
    }

    @Scheduled(fixedRate = 500)
    public void pushThumb() throws JsonProcessingException {
        Iterator<Map.Entry<String, List<CoinThumb>>> entryIterator = thumbQueue.entrySet().iterator();
        while (entryIterator.hasNext()) {
            Map.Entry<String, List<CoinThumb>> entry = entryIterator.next();
            String symbol = entry.getKey();
            List<CoinThumb> thumbs = entry.getValue();
            log.info("MARKET | Pushing thumbs for symbol: {}, thumbs count: {}", symbol, thumbs.size());
            if (!thumbs.isEmpty()) {
                synchronized (thumbs) {
                    CoinThumb pushThumb = thumbs.get(thumbs.size() - 1);
                    if (lastPushThumb.get(symbol) != null && lastPushThumb.get(symbol).getVolume().compareTo(pushThumb.getVolume()) > 0) {
                        pushThumb.setVolume(lastPushThumb.get(symbol).getVolume());
                    }
                    pushThumb.setTime(System.currentTimeMillis());
                    log.info("MARKET | Pushing thumb: {}", objectMapper.writeValueAsString(pushThumb));
                    messagingTemplate.convertAndSend("/topic/market/thumb", thumbs.get(thumbs.size() - 1));
                    lastPushThumb.put(symbol, thumbs.get(thumbs.size() - 1));
                    thumbs.clear();
                }
            }
        }
    }
}
