# Cấu hình tắt cache ở ứng dụng nhưng vẫn lưu dữ liệu vào Redis

spring:
  # Tắt Spring Cache
  cache:
    type: none
    # Tắt JCache
    jcache:
      config: 
    # Tắt Caffeine Cache
    caffeine:
      spec: 
  
  # Cấu hình Hibernate
  jpa:
    properties:
      hibernate:
        # Tắt Hibernate Second-Level Cache
        cache:
          use_second_level_cache: false
          use_query_cache: false
          # Xóa cấu hình region factory
          region:
            factory_class: 
  
  # Giữ nguyên cấu hình Redis
  data:
    redis:
      # Không thay đổi các cấu hình kết nối Redis
      # host, port, password, database, timeout giữ nguyên
      # Chỉ tắt Redis Cache
      cache:
        enabled: false

# Tắt Freemarker Cache
freemarker:
  cache: false

# Logging
logging:
  level:
    # Ghi log chi tiết cho các thao tác với Redis
    org.springframework.data.redis: DEBUG
    # Ghi log chi tiết cho các thao tác với cache
    org.springframework.cache: DEBUG
    # Ghi log chi tiết cho service thao tác với Redis
    com.icetea.lotus.infrastructure.cache.RedisDataService: DEBUG
