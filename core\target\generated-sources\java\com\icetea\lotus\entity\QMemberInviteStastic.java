package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QMemberInviteStastic is a Querydsl query type for MemberInviteStastic
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QMemberInviteStastic extends EntityPathBase<MemberInviteStastic> {

    private static final long serialVersionUID = -2066425272L;

    public static final QMemberInviteStastic memberInviteStastic = new QMemberInviteStastic("memberInviteStastic");

    public final NumberPath<java.math.BigDecimal> btcReward = createNumber("btcReward", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> estimatedReward = createNumber("estimatedReward", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> ethReward = createNumber("ethReward", java.math.BigDecimal.class);

    public final NumberPath<java.math.BigDecimal> extraReward = createNumber("extraReward", java.math.BigDecimal.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isRobot = createNumber("isRobot", Integer.class);

    public final NumberPath<Integer> levelOne = createNumber("levelOne", Integer.class);

    public final NumberPath<Integer> levelTwo = createNumber("levelTwo", Integer.class);

    public final NumberPath<Long> memberId = createNumber("memberId", Long.class);

    public final StringPath otherReward = createString("otherReward");

    public final StringPath stasticDate = createString("stasticDate");

    public final NumberPath<java.math.BigDecimal> usdtReward = createNumber("usdtReward", java.math.BigDecimal.class);

    public final StringPath userIdentify = createString("userIdentify");

    public QMemberInviteStastic(String variable) {
        super(MemberInviteStastic.class, forVariable(variable));
    }

    public QMemberInviteStastic(Path<? extends MemberInviteStastic> path) {
        super(path.getType(), path.getMetadata());
    }

    public QMemberInviteStastic(PathMetadata metadata) {
        super(MemberInviteStastic.class, metadata);
    }

}

