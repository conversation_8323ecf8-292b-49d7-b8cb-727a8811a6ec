package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.*;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.util.GeneratorUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.locks.ReentrantLock;

/**
 * The ExchangePushJob class is responsible for handling the push of trades, plates,
 * and coin thumb data to a messaging system. It uses scheduled tasks to periodically
 * process and push this data to ensure real-time updates to subscribers.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ExchangePushJob_Refactored {

    // Thread-safe collections for queues
    private final Map<String, List<ExchangeTrade>> tradesQueue = new ConcurrentHashMap<>();
    private final Map<String, List<TradePlate>> plateQueue = new ConcurrentHashMap<>();
    private final Map<String, List<CoinThumb>> thumbQueue = new ConcurrentHashMap<>();

    // Locks for more granular synchronization
    private final ReentrantLock tradesLock = new ReentrantLock();
    private final ReentrantLock platesLock = new ReentrantLock();
    private final ReentrantLock thumbsLock = new ReentrantLock();
    private final ReentrantLock plateLastBuyLock = new ReentrantLock();
    private final ReentrantLock plateLastSellLock = new ReentrantLock();

    // Dependencies
    private final ExchangeCoinService coinService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    // Random number generator
    private final Random rand = new Random();

    // Trade plate data
    private final Map<String, TradePlate> plateLastBuy = new ConcurrentHashMap<>();
    private final Map<String, TradePlate> plateLastSell = new ConcurrentHashMap<>();
    private final Map<String, TradePlate> plateLastBuyOrigin = new ConcurrentHashMap<>();
    private final Map<String, TradePlate> plateLastSellOrigin = new ConcurrentHashMap<>();
    // Last pushed market data
    private final Map<String, CoinThumb> lastPushThumb = new ConcurrentHashMap<>();
    // Price tracking
    private volatile BigDecimal lastBuyHeightPrice = BigDecimal.ZERO;
    private volatile BigDecimal lastSellLowPrice = BigDecimal.ZERO;

    /**
     * Adds trades to the queue for processing
     *
     * @param symbol Trading pair symbol
     * @param trades List of trades to add
     */
    public void addTrades(String symbol, List<ExchangeTrade> trades) {
        if (CollectionUtils.isEmpty(trades)) {
            return;
        }

        tradesLock.lock();
        try {
            List<ExchangeTrade> list = tradesQueue.computeIfAbsent(symbol, k -> new CopyOnWriteArrayList<>());
            list.addAll(trades);
        } finally {
            tradesLock.unlock();
        }
    }

    /**
     * Adds a trade plate to the queue for processing
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to add
     */
    public void addPlates(String symbol, TradePlate plate) {
        if (plate == null) {
            return;
        }

        platesLock.lock();
        try {
            List<TradePlate> list = plateQueue.computeIfAbsent(symbol, k -> new CopyOnWriteArrayList<>());
            list.add(plate);
        } finally {
            platesLock.unlock();
        }

        // Update the latest trading plates
        if (plate.getDirection() == ExchangeOrderDirection.BUY) {
            updateBuyPlate(symbol, plate);
        } else if (plate.getDirection() == ExchangeOrderDirection.SELL) {
            updateSellPlate(symbol, plate);
        }
    }

    /**
     * Updates the buy plate for a symbol
     *
     * @param symbol Trading pair symbol
     * @param plate  Buy trade plate
     */
    private void updateBuyPlate(String symbol, TradePlate plate) {
        plateLastBuyLock.lock();
        try {
            plateLastBuy.put(symbol, plate);
            copyTradePlate(plateLastBuy, plateLastBuyOrigin);
            lastBuyHeightPrice = plate.getHighestPrice();
        } finally {
            plateLastBuyLock.unlock();
        }
    }

    /**
     * Updates the sell plate for a symbol
     *
     * @param symbol Trading pair symbol
     * @param plate  Sell trade plate
     */
    private void updateSellPlate(String symbol, TradePlate plate) {
        plateLastSellLock.lock();
        try {
            plateLastSell.put(symbol, plate);
            copyTradePlate(plateLastSell, plateLastSellOrigin);
            lastSellLowPrice = plate.getLowestPrice();
        } finally {
            plateLastSellLock.unlock();
        }
    }

    /**
     * Copies trade plates from source to destination map
     *
     * @param source      Source map
     * @param destination Destination map
     */
    private void copyTradePlate(Map<String, TradePlate> source, Map<String, TradePlate> destination) {
        destination.clear();
        for (Map.Entry<String, TradePlate> entry : source.entrySet()) {
            destination.put(entry.getKey(), entry.getValue());
        }
    }

    /**
     * Adds a coin thumb to the queue for processing
     *
     * @param symbol Trading pair symbol
     * @param thumb  Coin thumb to add
     */
    public void addThumb(String symbol, CoinThumb thumb) {
        if (thumb == null) {
            return;
        }

        thumbsLock.lock();
        try {
            List<CoinThumb> list = thumbQueue.computeIfAbsent(symbol, k -> new CopyOnWriteArrayList<>());
            list.add(thumb);
        } finally {
            thumbsLock.unlock();
        }
    }

    /**
     * Scheduled task to push trades to subscribers
     */
    @Scheduled(fixedRate = 800)
    public void pushTrade() {
        for (Map.Entry<String, List<ExchangeTrade>> entry : tradesQueue.entrySet()) {
            String symbol = entry.getKey();
            List<ExchangeTrade> trades = entry.getValue();

            if (CollectionUtils.isNotEmpty(trades)) {
                tradesLock.lock();
                try {
                    messagingTemplate.convertAndSend("/topic/market/trade/" + symbol, new ArrayList<>(trades));
                    trades.clear();
                } finally {
                    tradesLock.unlock();
                }
            }
        }
    }

    /**
     * Scheduled task to push trade plates to subscribers
     */
    @Scheduled(fixedDelay = 1000)
    public void pushPlate() {
        for (Map.Entry<String, List<TradePlate>> entry : plateQueue.entrySet()) {
            String symbol = entry.getKey();
            List<TradePlate> plates = entry.getValue();

            if (CollectionUtils.isNotEmpty(plates)) {
                // Process real trade plates
                processRealTradePlates(symbol, plates);
            } else {
                // Process virtual trade plates if needed
                processVirtualTradePlates(symbol);
            }
        }
    }

    /**
     * Processes real trade plates from the queue
     *
     * @param symbol Trading pair symbol
     * @param plates List of trade plates to process
     */
    private void processRealTradePlates(String symbol, List<TradePlate> plates) {
        boolean hasPushAskPlate = false;
        boolean hasPushBidPlate = false;

        platesLock.lock();
        try {
            for (TradePlate plate : plates) {
                if (plate.getDirection() == ExchangeOrderDirection.BUY && !hasPushBidPlate) {
                    hasPushBidPlate = true;
                } else if (plate.getDirection() == ExchangeOrderDirection.SELL && !hasPushAskPlate) {
                    hasPushAskPlate = true;
                } else {
                    continue;
                }
                // Push plate data to WebSocket
                pushPlateToWebSocket(symbol, plate);
            }
            plates.clear();
        } finally {
            platesLock.unlock();
        }
    }

    /**
     * Pushes plate data to WebSocket topics
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to push
     */
    private void pushPlateToWebSocket(String symbol, TradePlate plate) {
        // Websocket push handover information
        messagingTemplate.convertAndSend("/topic/market/trade-plate/" + symbol, plate.toJSON(24));
        // Websocket push in-depth information
        messagingTemplate.convertAndSend("/topic/market/trade-depth/" + symbol, plate.toJSON(50));
    }

    /**
     * Processes virtual trade plates when no real data is available
     *
     * @param symbol Trading pair symbol
     */
    private void processVirtualTradePlates(String symbol) {
        if (log.isDebugEnabled()) {
            log.debug("Starting virtual trade plate processing for symbol: {}", symbol);
        }

        // Check if fake data generation is enabled for this symbol
        ExchangeCoin focusCoin = coinService.findBySymbol(symbol);
        if (focusCoin != null && focusCoin.getFakeDataStatus() == 0) {
            if (log.isDebugEnabled()) {
                log.debug("No trade plate data for symbol: {}, fake data generation disabled", symbol);
            }
            return;
        }

        // Get the last known buy and sell plates
        TradePlate plateBuy = plateLastBuy.get(symbol);
        TradePlate plateSell = plateLastSell.get(symbol);

        if (log.isDebugEnabled()) {
            log.debug("Virtual trade plates for {}: Buy plate exists: {}, Sell plate exists: {}",
                    symbol, plateBuy != null, plateSell != null);
        }

        // Randomly refresh plates from original state
        refreshPlatesIfNeeded(symbol, plateBuy, plateSell);

        // Generate virtual trades
        int randomValue = rand.nextInt(100);
        if (log.isDebugEnabled()) {
            log.debug("Virtual trade generation random value for {}: {} (generates if > 50)", symbol, randomValue);
        }

        if (randomValue > 50) {
            if (log.isDebugEnabled()) {
                log.debug("Generating virtual trade for symbol: {}", symbol);
            }
            generateVirtualTrade(symbol, plateLastBuy.get(symbol), plateLastSell.get(symbol));
        }

        // Modify and push buy plate
        TradePlate updatedBuyPlate = plateLastBuy.get(symbol);
        if (updatedBuyPlate != null) {
            if (log.isDebugEnabled()) {
                log.debug("Modifying and pushing buy plate for symbol: {}", symbol);
            }
            modifyAndPushPlate(symbol, updatedBuyPlate);
        }

        // Modify and push sell plate
        TradePlate updatedSellPlate = plateLastSell.get(symbol);
        if (updatedSellPlate != null) {
            if (log.isDebugEnabled()) {
                log.debug("Modifying and pushing sell plate for symbol: {}", symbol);
            }
            modifyAndPushPlate(symbol, updatedSellPlate);
        }
    }

    /**
     * Refreshes plates from original state if needed
     *
     * @param symbol    Trading pair symbol
     * @param plateBuy  Buy plate
     * @param plateSell Sell plate
     */
    private void refreshPlatesIfNeeded(String symbol, TradePlate plateBuy, TradePlate plateSell) {
        if (log.isDebugEnabled()) {
            log.debug("Checking if plates need refreshing for symbol: {}", symbol);
        }

        int buyRandom = rand.nextInt(100);
        boolean shouldRefreshBuy = buyRandom > 50 && plateBuy != null;

        if (log.isDebugEnabled()) {
            log.debug("Buy plate refresh check for {}: random value={}, should refresh={}",
                    symbol, buyRandom, shouldRefreshBuy);
        }

        if (shouldRefreshBuy) {
            if (log.isDebugEnabled()) {
                log.debug("Refreshing buy plate from original state for symbol: {}", symbol);
            }
            plateLastBuyLock.lock();
            try {
                TradePlate originalPlate = plateLastBuyOrigin.get(symbol);
                if (originalPlate != null) {
                    plateLastBuy.put(symbol, originalPlate);
                }
            } finally {
                plateLastBuyLock.unlock();
            }
        }

        int sellRandom = rand.nextInt(100);
        boolean shouldRefreshSell = sellRandom > 50 && plateSell != null;

        if (log.isDebugEnabled()) {
            log.debug("Sell plate refresh check for {}: random value={}, should refresh={}",
                    symbol, sellRandom, shouldRefreshSell);
        }

        if (shouldRefreshSell) {
            if (log.isDebugEnabled()) {
                log.debug("Refreshing sell plate from original state for symbol: {}", symbol);
            }
            plateLastSellLock.lock();
            try {
                TradePlate originalPlate = plateLastSellOrigin.get(symbol);
                if (originalPlate != null) {
                    plateLastSell.put(symbol, originalPlate);
                }
            } finally {
                plateLastSellLock.unlock();
            }
        }
    }

    /**
     * Generates a virtual trade
     *
     * @param symbol    Trading pair symbol
     * @param plateBuy  Buy plate
     * @param plateSell Sell plate
     */
    private void generateVirtualTrade(String symbol, TradePlate plateBuy, TradePlate plateSell) {
        CoinThumb lastThumb = lastPushThumb.get(symbol);
        if (plateSell == null || plateBuy == null || lastThumb == null) {
            return;
        }

        BigDecimal buyFistPrice = plateBuy.getHighestPrice();
        BigDecimal sellLastPrice = plateSell.getLowestPrice();

        // Check if spread is sufficient
        if (sellLastPrice.subtract(buyFistPrice).compareTo(BigDecimal.valueOf(0.0001)) < 0) {
            return;
        }

        // Generate random trade direction
        ExchangeOrderDirection direction = rand.nextInt(100) > 50 ?
                ExchangeOrderDirection.BUY : ExchangeOrderDirection.SELL;

        // Generate random price and amount
        int seed = rand.nextInt(10);
        BigDecimal randThumbPrice;
        if (direction == ExchangeOrderDirection.BUY) {
            randThumbPrice = sellLastPrice.subtract(sellLastPrice.subtract(buyFistPrice)
                            .multiply(BigDecimal.valueOf(seed).divide(BigDecimal.valueOf(1000))))
                    .setScale(6, RoundingMode.HALF_UP);
        } else {
            randThumbPrice = buyFistPrice.add(sellLastPrice.subtract(buyFistPrice)
                            .multiply(BigDecimal.valueOf(seed).divide(BigDecimal.valueOf(1000))))
                    .setScale(6, RoundingMode.HALF_UP);
        }

        BigDecimal randThumbAmount = plateBuy.getMinAmount()
                .multiply(BigDecimal.valueOf(0.85))
                .setScale(8, RoundingMode.HALF_UP);

        // Handle edge cases
        if (randThumbPrice.compareTo(sellLastPrice) >= 0) {
            randThumbPrice = buyFistPrice;
        }

        // Reset plates if needed for non-BTC pairs with invalid price
        if (!symbol.equalsIgnoreCase("BTC/USDT") && randThumbPrice.compareTo(BigDecimal.ZERO) <= 0) {
            resetPlates(symbol);
            plateBuy = plateLastBuy.get(symbol);
            plateSell = plateLastSell.get(symbol);
            randThumbPrice = buyFistPrice;
        }

        // Reset plates if amount is too small
        if ((!symbol.equalsIgnoreCase("BTC/USDT") && randThumbAmount.compareTo(BigDecimal.valueOf(0.0001)) <= 0) ||
                (symbol.equalsIgnoreCase("BTC/USDT") && randThumbAmount.compareTo(BigDecimal.valueOf(0.000001)) <= 0)) {
            resetPlates(symbol);
            plateBuy = plateLastBuy.get(symbol);
            plateSell = plateLastSell.get(symbol);
            randThumbAmount = plateBuy.getMinAmount();
        }

        if (log.isDebugEnabled()) {
            log.debug("Trading pair: {}, virtual trading volume: {}, minimum volume: {}",
                    symbol, randThumbAmount.toPlainString(), plateBuy.getMinAmount());
        }

        // Create virtual trade
        ExchangeTrade randTrade = new ExchangeTrade();
        randTrade.setPrice(randThumbPrice);
        randTrade.setAmount(randThumbAmount);
        randTrade.setBuyOrderId(GeneratorUtil.getOrderId("E"));
        randTrade.setSellOrderId(GeneratorUtil.getOrderId("E"));
        randTrade.setDirection(direction);
        randTrade.setSymbol(symbol);
        randTrade.setTime(Calendar.getInstance().getTimeInMillis());

        // Check if order should be sent
        boolean sendOrder = true;
        if (randTrade.getDirection() == ExchangeOrderDirection.SELL) {
            if (randThumbPrice.subtract(buyFistPrice).compareTo(BigDecimal.valueOf(0.0001)) <= 0) {
                sendOrder = false;
            }
        } else {
            if (sellLastPrice.subtract(randThumbPrice).compareTo(BigDecimal.valueOf(0.0001)) <= 0) {
                sendOrder = false;
            }
        }

        if (sendOrder) {
            // Push virtual trade
            List<ExchangeTrade> virtualTrades = new ArrayList<>();
            virtualTrades.add(randTrade);
            messagingTemplate.convertAndSend("/topic/market/trade/" + symbol, virtualTrades);

            // Update market summary
            if (lastThumb != null) {
                updateThumbWithTrade(lastThumb, randTrade);
                messagingTemplate.convertAndSend("/topic/market/thumb", lastThumb);
            }

            // Create corresponding order on opposite side
            BigDecimal orderAmount = randTrade.getAmount().multiply(BigDecimal.valueOf(1.1));
            if (randTrade.getDirection() == ExchangeOrderDirection.BUY) {
                ExchangeOrder sellOrder = new ExchangeOrder();
                sellOrder.setType(ExchangeOrderType.LIMIT_PRICE);
                sellOrder.setDirection(ExchangeOrderDirection.SELL);
                sellOrder.setAmount(orderAmount);
                sellOrder.setPrice(randTrade.getPrice());
                sellOrder.setTradedAmount(BigDecimal.ZERO);
                plateSell.add(sellOrder);
            } else {
                ExchangeOrder buyOrder = new ExchangeOrder();
                buyOrder.setType(ExchangeOrderType.LIMIT_PRICE);
                buyOrder.setDirection(ExchangeOrderDirection.BUY);
                buyOrder.setAmount(orderAmount);
                buyOrder.setPrice(randTrade.getPrice());
                buyOrder.setTradedAmount(BigDecimal.ZERO);
                plateBuy.add(buyOrder);
            }
        }
    }

    /**
     * Updates a thumb with trade data
     *
     * @param thumb Thumb to update
     * @param trade Trade data
     */
    private void updateThumbWithTrade(CoinThumb thumb, ExchangeTrade trade) {
        thumb.setClose(trade.getPrice());
        thumb.setHigh(trade.getPrice().max(thumb.getHigh()));
        thumb.setLow(trade.getPrice().min(thumb.getLow()));
        thumb.setVolume(thumb.getVolume().add(trade.getAmount()));
        thumb.setTurnover(thumb.getTurnover().add(trade.getAmount().multiply(trade.getPrice())));
        thumb.setChange(thumb.getClose().subtract(thumb.getOpen()));
        thumb.setChg(thumb.getChange().divide(thumb.getOpen(), 4, RoundingMode.UP));
        thumb.setUsdRate(trade.getPrice());
        thumb.setTime(System.currentTimeMillis());
    }

    /**
     * Resets plates to original state
     *
     * @param symbol Trading pair symbol
     */
    private void resetPlates(String symbol) {
        plateLastBuyLock.lock();
        try {
            TradePlate originalBuyPlate = plateLastBuyOrigin.get(symbol);
            if (originalBuyPlate != null) {
                plateLastBuy.put(symbol, originalBuyPlate);
            }
        } finally {
            plateLastBuyLock.unlock();
        }

        plateLastSellLock.lock();
        try {
            TradePlate originalSellPlate = plateLastSellOrigin.get(symbol);
            if (originalSellPlate != null) {
                plateLastSell.put(symbol, originalSellPlate);
            }
        } finally {
            plateLastSellLock.unlock();
        }
    }

    /**
     * Modifies a trade plate item and pushes the updated plate
     *
     * @param symbol Trading pair symbol
     * @param plate  Trade plate to modify and push
     */
    private void modifyAndPushPlate(String symbol, TradePlate plate) {
        List<TradePlateItem> list = plate.getItems();
        if (list.size() <= 9) {
            return;
        }

        int randInt = rand.nextInt(9);
        BigDecimal value = list.get(randInt).getAmount();
        String[] split = value.toPlainString().split("\\.");
        int number = split.length == 2 ? split[1].length() : 1;
        BigDecimal base = BigDecimal.valueOf(Math.pow(10.0, number));
        BigDecimal valueMultiply = BigDecimal.valueOf(Long.parseLong(split[0]));
        BigDecimal value1 = split.length == 2 ? BigDecimal.valueOf(Long.parseLong(split[1])) : BigDecimal.valueOf(1);
        int number1 = split.length == 2 ? split[1].length() : 1;

        // Add safety check for base1 calculation
        BigDecimal base1 = BigDecimal.valueOf(Math.pow(10.0, number1)).subtract(BigDecimal.valueOf(1));
        int randBound = Math.max(1, base1.intValue()); // Ensure the bound is at least 1
        int randAmountScale = rand.nextInt(randBound);

        BigDecimal multiplyValue1 = BigDecimal.ZERO;
        if (plate.getDirection() == ExchangeOrderDirection.BUY) {
            multiplyValue1 = new BigDecimal(randAmountScale).divide(base, number1, RoundingMode.DOWN);
        }

        BigDecimal multiply = valueMultiply.add(multiplyValue1);

        if (multiply.compareTo(BigDecimal.ZERO) > 0) {
            list.get(randInt).setAmount(multiply);
            // Push updated plate
            pushPlateToWebSocket(symbol, plate);
        }
    }

    /**
     * Scheduled task to push coin thumbs to subscribers
     */
    @Scheduled(fixedRate = 500)
    public void pushThumb() {
        try {
            for (Map.Entry<String, List<CoinThumb>> entry : thumbQueue.entrySet()) {
                String symbol = entry.getKey();
                List<CoinThumb> thumbs = entry.getValue();

                if (log.isDebugEnabled()) {
                    log.debug("MARKET | Pushing thumbs for symbol: {}, thumbs count: {}", symbol, thumbs.size());
                }

                if (!thumbs.isEmpty()) {
                    thumbsLock.lock();
                    try {
                        CoinThumb pushThumb = thumbs.get(thumbs.size() - 1);
                        CoinThumb lastThumb = lastPushThumb.get(symbol);

                        if (lastThumb != null && lastThumb.getVolume().compareTo(pushThumb.getVolume()) > 0) {
                            pushThumb.setVolume(lastThumb.getVolume());
                        }

                        pushThumb.setTime(System.currentTimeMillis());

                        if (log.isDebugEnabled()) {
                            log.debug("MARKET | Pushing thumb: {}", objectMapper.writeValueAsString(pushThumb));
                        }

                        messagingTemplate.convertAndSend("/topic/market/thumb", pushThumb);
                        lastPushThumb.put(symbol, pushThumb);
                        thumbs.clear();
                    } finally {
                        thumbsLock.unlock();
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.error("Error processing thumb data: {}", e.getMessage(), e);
        }
    }
}
