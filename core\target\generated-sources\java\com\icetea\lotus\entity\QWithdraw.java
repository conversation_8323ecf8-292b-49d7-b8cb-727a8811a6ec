package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QWithdraw is a Querydsl query type for Withdraw
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QWithdraw extends EntityPathBase<Withdraw> {

    private static final long serialVersionUID = 1581432922L;

    public static final QWithdraw withdraw = new QWithdraw("withdraw");

    public final StringPath address = createString("address");

    public final NumberPath<Long> addtime = createNumber("addtime", Long.class);

    public final NumberPath<Integer> coinid = createNumber("coinid", Integer.class);

    public final StringPath coinname = createString("coinname");

    public final NumberPath<Double> fee = createNumber("fee", Double.class);

    public final StringPath hash = createString("hash");

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Long> memberid = createNumber("memberid", Long.class);

    public final NumberPath<Double> money = createNumber("money", Double.class);

    public final NumberPath<Integer> processmold = createNumber("processmold", Integer.class);

    public final NumberPath<Long> processtime = createNumber("processtime", Long.class);

    public final NumberPath<Integer> protocol = createNumber("protocol", Integer.class);

    public final StringPath protocolname = createString("protocolname");

    public final NumberPath<Double> real_money = createNumber("real_money", Double.class);

    public final StringPath remark = createString("remark");

    public final NumberPath<Integer> status = createNumber("status", Integer.class);

    public final StringPath withdrawinfo = createString("withdrawinfo");

    public QWithdraw(String variable) {
        super(Withdraw.class, forVariable(variable));
    }

    public QWithdraw(Path<? extends Withdraw> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWithdraw(PathMetadata metadata) {
        super(Withdraw.class, metadata);
    }

}

