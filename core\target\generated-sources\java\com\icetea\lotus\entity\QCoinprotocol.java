package com.icetea.lotus.entity;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QCoinprotocol is a Querydsl query type for Coinprotocol
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QCoinprotocol extends EntityPathBase<Coinprotocol> {

    private static final long serialVersionUID = -666850951L;

    public static final QCoinprotocol coinprotocol = new QCoinprotocol("coinprotocol");

    public final StringPath browser = createString("browser");

    public final NumberPath<Integer> chainid = createNumber("chainid", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> protocol = createNumber("protocol", Integer.class);

    public final StringPath protocolname = createString("protocolname");

    public final StringPath rpcpassword = createString("rpcpassword");

    public final StringPath rpcserver = createString("rpcserver");

    public final StringPath rpcuser = createString("rpcuser");

    public final StringPath symbol = createString("symbol");

    public QCoinprotocol(String variable) {
        super(Coinprotocol.class, forVariable(variable));
    }

    public QCoinprotocol(Path<? extends Coinprotocol> path) {
        super(path.getType(), path.getMetadata());
    }

    public QCoinprotocol(PathMetadata metadata) {
        super(Coinprotocol.class, metadata);
    }

}

