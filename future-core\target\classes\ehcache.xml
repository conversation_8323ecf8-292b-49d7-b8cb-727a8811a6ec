<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.ehcache.org/v3"
        xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core-3.0.xsd">
    
    <persistence directory="${java.io.tmpdir}/ehcache" />
    
    <cache-template name="default">
        <expiry>
            <ttl unit="minutes">30</ttl>
        </expiry>
        <resources>
            <heap unit="entries">1000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache-template>
    
    <cache-template name="long-lived">
        <expiry>
            <ttl unit="hours">12</ttl>
        </expiry>
        <resources>
            <heap unit="entries">1000</heap>
            <offheap unit="MB">10</offheap>
        </resources>
    </cache-template>
    
    <cache-template name="short-lived">
        <expiry>
            <ttl unit="minutes">5</ttl>
        </expiry>
        <resources>
            <heap unit="entries">1000</heap>
            <offheap unit="MB">5</offheap>
        </resources>
    </cache-template>
    
    <!-- Cấu hình cache cho các entity -->
    <cache alias="com.icetea.lotus.core.domain.entity.Contract" uses-template="long-lived" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractOrder" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractPosition" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractTrade" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractWallet" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractTransaction" uses-template="default" />
    
    <!-- Cấu hình cache cho các query -->
    <cache alias="org.hibernate.cache.internal.StandardQueryCache" uses-template="short-lived" />
    <cache alias="org.hibernate.cache.spi.UpdateTimestampsCache" uses-template="long-lived">
        <expiry>
            <none />
        </expiry>
    </cache>
    
    <!-- Cấu hình cache cho các collection -->
    <cache alias="com.icetea.lotus.core.domain.entity.Contract.orders" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.Contract.positions" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.Contract.trades" uses-template="default" />
    
    <!-- Cấu hình cache cho các natural id -->
    <cache alias="com.icetea.lotus.core.domain.entity.Contract##NaturalId" uses-template="long-lived" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractOrder##NaturalId" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractPosition##NaturalId" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractTrade##NaturalId" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractWallet##NaturalId" uses-template="default" />
    <cache alias="com.icetea.lotus.core.domain.entity.ContractTransaction##NaturalId" uses-template="default" />
</config>
