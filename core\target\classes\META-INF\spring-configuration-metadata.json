{"groups": [{"name": "cex-security", "type": "com.icetea.lotus.config.CexSecurityProperties", "sourceType": "com.icetea.lotus.config.CexSecurityProperties"}, {"name": "keycloak", "type": "com.icetea.lotus.config.KeycloakPropsConfig", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig"}, {"name": "keycloak.credentials", "type": "com.icetea.lotus.config.KeycloakPropsConfig$Credentials", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig", "sourceMethod": "public com.icetea.lotus.config.KeycloakPropsConfig.Credentials getCredentials() "}, {"name": "twi<PERSON>", "type": "com.icetea.lotus.config.twilio.TwilioConfiguration", "sourceType": "com.icetea.lotus.config.twilio.TwilioConfiguration"}], "properties": [{"name": "cex-security.default-principal-name", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.CexSecurityProperties"}, {"name": "cex-security.permit-all-endpoints", "type": "java.util.List<java.lang.String>", "sourceType": "com.icetea.lotus.config.CexSecurityProperties"}, {"name": "cex-security.resource-server-enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.icetea.lotus.config.CexSecurityProperties"}, {"name": "keycloak.auth-server-url", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig"}, {"name": "keycloak.credentials.secret", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig$Credentials"}, {"name": "keycloak.realm", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig"}, {"name": "keycloak.resource", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.KeycloakPropsConfig"}, {"name": "twilio.account-sid", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.twilio.TwilioConfiguration"}, {"name": "twilio.auth-token", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.twilio.TwilioConfiguration"}, {"name": "twilio.trial-number", "type": "java.lang.String", "sourceType": "com.icetea.lotus.config.twilio.TwilioConfiguration"}], "hints": []}