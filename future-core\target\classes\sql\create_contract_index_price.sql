-- T<PERSON><PERSON> bảng contract_index_price
CREATE TABLE IF NOT EXISTS contract_index_price (
    id BIGSERIAL PRIMARY KEY,
    contract_id BIGINT,
    symbol VARCHAR(50) NOT NULL,
    price DECIMAL(18,8) NOT NULL,
    reference_prices TEXT,
    create_time TIMESTAMP NOT NULL
);

-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> chỉ mục cho bảng contract_index_price
CREATE INDEX IF NOT EXISTS idx_contract_index_price_symbol ON contract_index_price (symbol);
CREATE INDEX IF NOT EXISTS idx_contract_index_price_create_time ON contract_index_price (create_time);

-- <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> liệu mẫu cho bảng contract_index_price
INSERT INTO contract_index_price (contract_id, symbol, price, reference_prices, create_time)
VALUES
(1, 'BTCUSDT', 50000.00000000, '{"binance": 50010.00000000, "okex": 49990.00000000, "huobi": 50000.00000000}', NOW() - INTERVAL '1 HOUR'),
(2, 'ETHUSDT', 3000.00000000, '{"binance": 3005.00000000, "okex": 2995.00000000, "huobi": 3000.00000000}', NOW() - INTERVAL '1 HOUR'),
(3, 'SOLUSDT', 100.00000000, '{"binance": 100.50000000, "okex": 99.50000000, "huobi": 100.00000000}', NOW() - INTERVAL '1 HOUR'),
(4, 'BNBUSDT', 400.00000000, '{"binance": 401.00000000, "okex": 399.00000000, "huobi": 400.00000000}', NOW() - INTERVAL '1 HOUR'),
(5, 'ADAUSDT', 1.20000000, '{"binance": 1.21000000, "okex": 1.19000000, "huobi": 1.20000000}', NOW() - INTERVAL '1 HOUR');
